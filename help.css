body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    color: #333;
}
h1 {
    color: #34D399;
    border-bottom: 2px solid #34D399;
    padding-bottom: 10px;
}
h2 {
    color: #10B981;
    margin-top: 30px;
    border-bottom: 1px solid #D1FAE5;
    padding-bottom: 5px;
}
h3 {
    color: #1F2937;
    margin-top: 20px;
}
.feature-card {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.feature-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}
.feature-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #D1FAE5;
    border-radius: 50%;
    color: #10B981;
}
.feature-title {
    margin: 0;
    font-size: 18px;
}
.steps {
    margin-top: 15px;
}
.step {
    margin-bottom: 10px;
    padding-left: 15px;
    border-left: 3px solid #D1FAE5;
}
.step-number {
    font-weight: bold;
    color: #10B981;
    margin-right: 5px;
}
.tip {
    background-color: #DBEAFE;
    border-left: 4px solid #3B82F6;
    padding: 10px 15px;
    margin: 15px 0;
}
.warning {
    background-color: #FEE2E2;
    border-left: 4px solid #EF4444;
    padding: 10px 15px;
    margin: 15px 0;
}
.keyboard-shortcut {
    background-color: #E5E7EB;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
}
img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 15px 0;
}
.toc {
    background-color: #F3F4F6;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}
.toc h2 {
    margin-top: 0;
    border-bottom: none;
}
.toc ul {
    padding-left: 20px;
}
.toc a {
    color: #10B981;
    text-decoration: none;
}
.toc a:hover {
    text-decoration: underline;
}
.last-updated {
    font-style: italic;
    color: #666;
    margin-top: 40px;
}

// --- START OF FILE popup.js ---

// Listen for messages from the background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Validate the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("Popup: Rejected message from unauthorized sender:", sender.id);
        return false;
    }

    if (message.action === 'updateVoiceSettingsUI' && message.html) {
        const container = document.getElementById('voice-settings-container');
        if (container) {
            container.innerHTML = message.html;
            // Set up event listeners for the voice settings UI
            setupVoiceSettingsEventListeners();
        }
    } else if (message.action === 'updateSyncStatus') { // Listener moved inside DOMContentLoaded in original, keeping it there for consistency
        // Handle this inside DOMContentLoaded
    } else if (message.action === 'updateConnectionStatus') { // Listener moved inside DOMContentLoaded in original, keeping it there for consistency
         // Handle this inside DOMContentLoaded
    }
    // Return true to indicate you wish to send a response asynchronously
    // Only relevant if you actually use sendResponse, otherwise false or omit return.
    // In this case, since we don't call sendResponse, returning false or omitting is fine.
    return false;
});

// Set up event listeners for the voice settings UI
function setupVoiceSettingsEventListeners() {
    // Provider select
    const providerSelect = document.getElementById('voice-provider-select');
    if (providerSelect) {
        providerSelect.addEventListener('change', () => {
            const selectedProvider = providerSelect.value;
            const apiKeyContainer = document.getElementById('api-key-container');

            if (apiKeyContainer) {
                // Show/hide API key input based on provider
                const requiresKey = ['google', 'azure', 'assembly'].includes(selectedProvider);
                apiKeyContainer.style.display = requiresKey ? 'block' : 'none';
            }

            // Update language options
            updateLanguageOptions(selectedProvider);
        });
    }

    // Save button
    const saveButton = document.querySelector('#voice-settings-container .Stickara-button');
    if (saveButton) {
        saveButton.addEventListener('click', saveVoiceSettings); // Note: saveVoiceSettings defined later
    }
}

// Update language options based on selected provider
function updateLanguageOptions(provider) {
    // Try to use the enhanced voice language system first
    const langSelect = document.getElementById('voice-language-select') || document.getElementById('voice-language');
    if (!langSelect) {
        console.warn('Popup: Language select element not found');
        return;
    }

    // Clear existing options
    langSelect.innerHTML = '';

    // Try to get languages from the enhanced voice module
    let languages = [];
    let languageDisplayNames = {};

    if (typeof window.getDefaultLanguagesForProvider === 'function' &&
        typeof window.getLanguageDisplayName === 'function') {
        // Use the enhanced voice module functions
        languages = window.getDefaultLanguagesForProvider(provider);
        console.log(`Popup: Using enhanced voice languages for ${provider}:`, languages.length, 'languages');
    } else {
        // Fallback to basic language sets
        console.log(`Popup: Using fallback languages for ${provider}`);
        switch (provider) {
            case 'browser':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'es-ES', 'es-MX', 'fr-FR', 'fr-CA', 'de-DE', 'de-AT', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'ar-SA', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU'];
                break;
            case 'google':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-ZA', 'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-CL', 'es-PE', 'es-VE', 'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'de-DE', 'de-AT', 'de-CH', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK', 'ar-SA', 'ar-AE', 'ar-EG', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU'];
                break;
            case 'azure':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-ZA', 'es-ES', 'es-MX', 'es-AR', 'es-CO', 'es-CL', 'es-PE', 'es-VE', 'fr-FR', 'fr-CA', 'fr-BE', 'fr-CH', 'de-DE', 'de-AT', 'de-CH', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'zh-TW', 'zh-HK', 'ar-SA', 'ar-AE', 'ar-EG', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI', 'cs-CZ', 'hu-HU'];
                break;
            case 'assembly':
                languages = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'es-ES', 'es-MX', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN', 'ar-SA', 'hi-IN', 'th-TH', 'vi-VN', 'tr-TR', 'pl-PL', 'nl-NL', 'sv-SE', 'da-DK', 'no-NO', 'fi-FI'];
                break;
            default:
                languages = ['en-US'];
                break;
        }

        // Basic language display names for fallback
        languageDisplayNames = {
            'en-US': 'English (United States)',
            'en-GB': 'English (United Kingdom)',
            'en-AU': 'English (Australia)',
            'en-CA': 'English (Canada)',
            'en-IN': 'English (India)',
            'es-ES': 'Spanish (Spain)',
            'es-MX': 'Spanish (Mexico)',
            'fr-FR': 'French (France)',
            'fr-CA': 'French (Canada)',
            'de-DE': 'German (Germany)',
            'de-AT': 'German (Austria)',
            'it-IT': 'Italian (Italy)',
            'pt-BR': 'Portuguese (Brazil)',
            'pt-PT': 'Portuguese (Portugal)',
            'ru-RU': 'Russian (Russia)',
            'ja-JP': 'Japanese (Japan)',
            'ko-KR': 'Korean (South Korea)',
            'zh-CN': 'Chinese (Simplified)',
            'zh-TW': 'Chinese (Traditional)',
            'ar-SA': 'Arabic (Saudi Arabia)',
            'hi-IN': 'Hindi (India)',
            'th-TH': 'Thai (Thailand)',
            'vi-VN': 'Vietnamese (Vietnam)',
            'tr-TR': 'Turkish (Turkey)',
            'pl-PL': 'Polish (Poland)',
            'nl-NL': 'Dutch (Netherlands)',
            'sv-SE': 'Swedish (Sweden)',
            'da-DK': 'Danish (Denmark)',
            'no-NO': 'Norwegian (Norway)',
            'fi-FI': 'Finnish (Finland)',
            'cs-CZ': 'Czech (Czech Republic)',
            'hu-HU': 'Hungarian (Hungary)'
        };
    }

    // Add options to select
    if (languages && languages.length > 0) {
        languages.forEach(lang => {
            const option = document.createElement('option');
            option.value = lang;

            // Use enhanced display name function if available, otherwise fallback
            if (typeof window.getLanguageDisplayName === 'function') {
                option.textContent = window.getLanguageDisplayName(lang);
            } else {
                option.textContent = languageDisplayNames[lang] || lang;
            }

            langSelect.appendChild(option);
        });

        console.log(`Popup: Added ${languages.length} language options for provider ${provider}`);
    } else {
        // Emergency fallback
        const option = document.createElement('option');
        option.value = 'en-US';
        option.textContent = 'English (United States)';
        langSelect.appendChild(option);
        console.warn('Popup: No languages found, using emergency fallback');
    }
}

// Save voice settings (Placeholder, full definition is inside DOMContentLoaded)
function saveVoiceSettings() {
    // Implementation is within initVoiceSettings inside DOMContentLoaded
    console.warn("saveVoiceSettings called before initVoiceSettings was fully defined or outside its scope. Check implementation.");
}

document.addEventListener('DOMContentLoaded', function () {

  // URL utilities are no longer needed - using direct URL handling

  // Existing DOM Elements
  const clearButton = document.getElementById('clear-note');
  const searchButton = document.getElementById('search-button');
  const searchQueryInput = document.getElementById('search-query');
  const searchResultsDiv = document.getElementById('search-results');
  const viewDashboardButton = document.getElementById('view-dashboard-button');
  const statusMessage = document.getElementById('main-status-message');

  // New DOM Elements for Drive Sync
  const connectDriveButton = document.getElementById('connect-drive-button');
  const disconnectDriveButton = document.getElementById('disconnect-drive-button');
  const syncStatusIndicator = document.getElementById('sync-status-indicator');

  // Global Notes Element
  const globalNotesResultsDiv = document.getElementById('global-notes-results')?.querySelector('.results-content'); // Target the inner content div

  // *** MODIFIED/NEW EXPORT ELEMENTS ***
  const exportNotesButton = document.getElementById('export-notes'); // Renamed for clarity
  const exportHighlightsButton = document.getElementById('export-highlights');
  const commonExportFormatSelect = document.getElementById('common-export-format-select'); // *** NEW COMMON SELECT ID ***

  // Close Buttons
  const closeVoiceSettingsButton = document.getElementById('close-voice-settings');
  const closeNoteSettingsButton = document.getElementById('close-note-settings');


  // Other Settings Elements

  // Constants
  const STORAGE_KEY_PREFIX = 'Stickara_note_';
  const STATE_KEY_PREFIX = 'Stickara_state_';
  const HIGHLIGHT_KEY_PREFIX = 'Stickara_highlights_';
  const DEFAULT_HIGHLIGHT_COLOR = 'yellow'; // Should match content-state.js

  // --- Utility Functions ---

  /**
   * Displays a status message for a short duration.
   * @param {string} message - The message to display.
   * @param {('info'|'success'|'error')} [type='info'] - The type of message (for styling).
   */
  function showStatus(message, type = 'info') {
      if (!statusMessage) return; // Guard against missing element
      statusMessage.textContent = message;
      statusMessage.className = 'visible ' + type; // Use classes for styling
      // Clear any existing timeouts to prevent overlaps
      if (statusMessage.timeoutId) {
          clearTimeout(statusMessage.timeoutId);
      }
      if (statusMessage.resetTimeoutId) {
          clearTimeout(statusMessage.resetTimeoutId);
      }
      statusMessage.style.opacity = '1'; // Ensure visible initially

      statusMessage.timeoutId = setTimeout(() => {
          statusMessage.style.opacity = '0'; // Fade out first
          // Reset text content and class after fade out transition
          statusMessage.resetTimeoutId = setTimeout(() => {
              // Only clear if the message hasn't changed in the meantime
              if (statusMessage.textContent === message) {
                    statusMessage.textContent = '';
                    statusMessage.className = '';
              }
              statusMessage.timeoutId = null;
              statusMessage.resetTimeoutId = null;
            }, 300); // Match CSS transition duration if any
      }, 3000);
  }

  /**
   * Helper to convert basic HTML to plain text for export/search with enhanced security.
   * Handles breaks, basic tags, and images. More complex HTML might need refinement.
   * Includes security measures to prevent XSS attacks.
   * @param {string} html - The HTML string.
   * @param {string} format - The target format ('txt', 'md', or 'html').
   * @returns {string} - The plain text representation.
   */
  function getPlainText(html, format = 'txt') {
      if (!html) return '';

      try {
          // SECURITY: Sanitize the HTML before processing
          // First replace <script> tags and other potentially dangerous elements
          const sanitizedHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                                  .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
                                  .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
                                  .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
                                  .replace(/javascript:/gi, 'removed:')
                                  .replace(/on\w+(\s*)=/gi, 'data-removed$1=');

          // Replace <br> tags with newlines first for better block element handling
          const processedHtml = sanitizedHtml.replace(/<br\s*\/?>/gi, '\n');

          // Use a temporary div to let the browser parse HTML
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = processedHtml;

          // Remove any remaining script elements that might have been nested
          const scriptElements = tempDiv.querySelectorAll('script, iframe, object, embed');
          scriptElements.forEach(el => el.parentNode.removeChild(el));

          // Remove event handlers from all elements
          const allElements = tempDiv.querySelectorAll('*');
          allElements.forEach(el => {
              // Remove all attributes that start with "on" (event handlers)
              Array.from(el.attributes).forEach(attr => {
                  if (attr.name.startsWith('on') || attr.name.includes('javascript:')) {
                      el.removeAttribute(attr.name);
                  }
              });
          });

          // Note: For HTML format, we don't modify images here as we'll use the original HTML

          // Add newlines after block elements for better structure
          tempDiv.querySelectorAll('p, li, h1, h2, h3, h4, h5, h6, div, blockquote, pre').forEach(el => {
               // Check if the element itself or its last child already ends with whitespace effectively
               const content = el.textContent || '';
               // Add newline only if content exists and doesn't end with whitespace
               if (content.length > 0 && !/\s$/.test(content)) {
                   el.append('\n');
               }
          });

          // Finally, get the text content, which includes the added newlines and markers
          let text = tempDiv.textContent || tempDiv.innerText || "";

          // Optional: Collapse multiple spaces/newlines for cleaner output
          text = text.replace(/ +/g, ' '); // Collapse multiple spaces to one
          text = text.replace(/\n\s*\n/g, '\n\n'); // Collapse multiple newlines but keep double newlines for paragraphs
          text = text.trim(); // Remove leading/trailing whitespace

          return text;
      } catch (e) {
          console.error("Error in getPlainText:", e);
          return html ? String(html).replace(/<[^>]*>/g, '') : ''; // Fallback to simple tag stripping
      }


  }

  /**
   * Convert styled HTML content to PDF using html2canvas and jsPDF
   * @param {string} htmlContent - The complete HTML document content with styling
   * @param {string} filename - The filename for the PDF
   * @param {string} statusMessage - Status message to show during conversion
   * @returns {Promise} - Promise that resolves when PDF is generated and downloaded
   */
  async function convertStyledHtmlToPdf(htmlContent, filename, statusMessage) {
      try {
          showStatus(statusMessage, 'info');

          // Check if required libraries are available
          if (typeof window.jspdf === 'undefined') {
              throw new Error('jsPDF library not loaded. Please refresh the popup and try again.');
          }

          if (typeof html2canvas === 'undefined') {
              throw new Error('html2canvas library not loaded. Please refresh the popup and try again.');
          }

          console.log('PDF Export: Libraries loaded successfully');

          // Create a temporary container for the HTML content with improved capture settings
          const tempContainer = document.createElement('div');
          tempContainer.style.position = 'absolute';
          tempContainer.style.left = '-9999px';
          tempContainer.style.top = '-9999px';
          tempContainer.style.width = '1200px'; // Increased width for better capture
          tempContainer.style.backgroundColor = 'white';
          tempContainer.style.padding = '20px';
          tempContainer.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
          tempContainer.innerHTML = htmlContent;

          document.body.appendChild(tempContainer);

          console.log('PDF Export: Temporary container created');

          // Simple image optimization for PDF export
          const images = tempContainer.querySelectorAll('img');
          console.log(`PDF Export: Found ${images.length} images to process`);

          if (images.length > 0) {
              showStatus(`Preparing ${images.length} images for PDF...`, 'info');

              // Simple, reliable image processing
              await Promise.all(Array.from(images).map((img, index) => {
                  return new Promise((resolve) => {
                      try {
                          // Ensure image is loaded
                          if (img.complete && img.naturalWidth > 0) {
                              // Simple optimization: just ensure reasonable size
                              if (img.naturalWidth > 2000 || img.naturalHeight > 2000) {
                                  img.style.maxWidth = '2000px';
                                  img.style.maxHeight = '2000px';
                                  img.style.width = 'auto';
                                  img.style.height = 'auto';
                              }
                              resolve();
                          } else {
                              // Wait for image to load with timeout
                              const timeout = setTimeout(() => {
                                  console.warn(`PDF Export: Image ${index + 1} load timeout`);
                                  resolve();
                              }, 5000);

                              img.onload = () => {
                                  clearTimeout(timeout);
                                  if (img.naturalWidth > 2000 || img.naturalHeight > 2000) {
                                      img.style.maxWidth = '2000px';
                                      img.style.maxHeight = '2000px';
                                      img.style.width = 'auto';
                                      img.style.height = 'auto';
                                  }
                                  resolve();
                              };

                              img.onerror = () => {
                                  clearTimeout(timeout);
                                  console.warn(`PDF Export: Image ${index + 1} failed to load`);
                                  resolve();
                              };
                          }
                      } catch (error) {
                          console.error(`PDF Export: Error processing image ${index + 1}:`, error);
                          resolve();
                      }
                  });
              }));
          }

          console.log('PDF Export: All images optimized, starting canvas capture');

          // Show progress indicator
          showStatus('Generating PDF canvas...', 'info');

          // Use simple, reliable html2canvas settings
          console.log(`PDF Export: Processing note with ${images.length} images, height: ${tempContainer.scrollHeight}px`);

          const canvas = await html2canvas(tempContainer, {
              width: 1200,
              height: tempContainer.scrollHeight,
              scale: 1, // Use 1x scale for reliability
              useCORS: true,
              allowTaint: true,
              backgroundColor: '#ffffff',
              logging: true, // Enable logging for debugging
              imageTimeout: 15000, // Shorter timeout
              removeContainer: false,
              foreignObjectRendering: false, // Disable for better compatibility
              scrollX: 0,
              scrollY: 0,
              windowWidth: 1200,
              windowHeight: tempContainer.scrollHeight,
              onclone: (clonedDoc) => {
                  console.log('PDF Export: Cloning document for canvas capture');
                  // Ensure all images are visible and properly sized
                  const clonedImages = clonedDoc.querySelectorAll('img');
                  console.log(`PDF Export: Found ${clonedImages.length} images in cloned document`);

                  clonedImages.forEach((img, index) => {
                      // Ensure images are visible
                      img.style.display = 'block';
                      img.style.visibility = 'visible';
                      img.style.opacity = '1';
                      img.style.maxWidth = '100%';
                      img.style.height = 'auto';

                      // Force load state
                      if (!img.complete) {
                          console.log(`PDF Export: Image ${index + 1} not complete, setting src`);
                          const originalSrc = img.src;
                          img.src = originalSrc;
                      }
                  });

                  // Ensure container is visible
                  const clonedContainer = clonedDoc.body;
                  if (clonedContainer) {
                      clonedContainer.style.display = 'block';
                      clonedContainer.style.visibility = 'visible';
                      clonedContainer.style.opacity = '1';
                  }
              }
          });

          console.log('PDF Export: Canvas capture completed');

          // Remove the temporary container
          document.body.removeChild(tempContainer);

          // Create PDF with jsPDF
          console.log('PDF Export: Creating PDF document');
          const { jsPDF } = window.jspdf;
          const pdf = new jsPDF({
              orientation: 'portrait',
              unit: 'mm',
              format: 'a4'
          });

          // Calculate dimensions to fit the canvas in PDF
          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();
          const canvasWidth = canvas.width;
          const canvasHeight = canvas.height;

          console.log(`PDF Export: Canvas dimensions: ${canvasWidth}x${canvasHeight}`);
          console.log(`PDF Export: PDF page dimensions: ${pdfWidth}x${pdfHeight}mm`);

          // Calculate scaling to fit width (using 1x scale)
          const pdfScale = pdfWidth / canvasWidth; // Simple 1:1 scaling since we use scale: 1
          const scaledHeight = canvasHeight * pdfScale;

          console.log(`PDF Export: Scaled dimensions: ${pdfWidth}x${scaledHeight}mm`);

          // Add the canvas as image to PDF
          console.log('PDF Export: Converting canvas to image data');
          showStatus('Converting to PDF format...', 'info');

          // Simple, reliable image conversion
          const imgData = canvas.toDataURL('image/png', 1.0); // Use full quality to avoid issues

          if (!imgData || imgData === 'data:,') {
              throw new Error('Failed to convert canvas to image data');
          }

          console.log(`PDF Export: Image data size: ${Math.round(imgData.length / 1024)}KB`);
          console.log('PDF Export: Canvas converted successfully');

          // Validate canvas content before adding to PDF
          if (canvas.width === 0 || canvas.height === 0) {
              throw new Error('Canvas has zero dimensions');
          }

          console.log(`PDF Export: Adding content to PDF (${scaledHeight <= pdfHeight ? 'single' : 'multiple'} page)`);

          if (scaledHeight <= pdfHeight) {
              // Single page
              console.log('PDF Export: Adding single page');
              showStatus('Finalizing PDF...', 'info');

              try {
                  pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, scaledHeight);
                  console.log('PDF Export: Single page added successfully');
              } catch (addImageError) {
                  console.error('PDF Export: Error adding image to PDF:', addImageError);
                  throw new Error(`Failed to add image to PDF: ${addImageError.message}`);
              }
          } else {
              // Multiple pages needed
              const totalPages = Math.ceil(scaledHeight / pdfHeight);
              console.log(`PDF Export: Adding multiple pages (${totalPages} pages)`);
              let yPosition = 0;
              const pageHeight = pdfHeight;
              let pageCount = 1;

              while (yPosition < scaledHeight) {
                  try {
                      if (yPosition > 0) {
                          console.log(`PDF Export: Adding page ${++pageCount} of ${totalPages}`);
                          showStatus(`Adding page ${pageCount} of ${totalPages}...`, 'info');
                          pdf.addPage();
                      }

                      pdf.addImage(imgData, 'PNG', 0, -yPosition, pdfWidth, scaledHeight);
                      yPosition += pageHeight;

                      console.log(`PDF Export: Page ${pageCount} added successfully`);

                      // Allow UI to update between pages
                      if (pageCount % 2 === 0) {
                          await new Promise(resolve => setTimeout(resolve, 10));
                      }
                  } catch (pageError) {
                      console.error(`PDF Export: Error adding page ${pageCount}:`, pageError);
                      throw new Error(`Failed to add page ${pageCount}: ${pageError.message}`);
                  }
              }
          }

          // Download the PDF
          console.log('PDF Export: Generating PDF blob');
          showStatus('Generating PDF file...', 'info');

          let pdfBlob;
          try {
              pdfBlob = pdf.output('blob');
          } catch (blobError) {
              console.error('PDF Export: Error generating PDF blob:', blobError);
              throw new Error(`Failed to generate PDF blob: ${blobError.message}`);
          }

          if (!pdfBlob) {
              throw new Error('PDF blob is null or undefined');
          }

          if (pdfBlob.size === 0) {
              throw new Error('PDF blob has zero size - content may not have been captured');
          }

          if (pdfBlob.size < 1000) {
              console.warn('PDF Export: PDF blob is very small, may indicate content capture issues');
          }

          console.log(`PDF Export: PDF blob generated successfully - size: ${Math.round(pdfBlob.size / 1024)}KB`);

          const objectUrl = URL.createObjectURL(pdfBlob);

          console.log('PDF Export: Starting download');
          chrome.downloads.download({
              url: objectUrl,
              filename: filename,
              saveAs: true
          }, (id) => {
              URL.revokeObjectURL(objectUrl);
              if (chrome.runtime.lastError) {
                  console.error('PDF Export: Download failed:', chrome.runtime.lastError);
                  showStatus(`PDF export failed: ${chrome.runtime.lastError.message}`, 'error');
              } else if (!id) {
                  console.warn('PDF Export: Download cancelled or blocked');
                  showStatus('PDF export may have been cancelled or blocked.', 'info');
              } else {
                  console.log('PDF Export: Download completed successfully');
                  showStatus('PDF export completed successfully!', 'success');
              }
          });

      } catch (error) {
          console.error('Error converting HTML to PDF:', error);
          showStatus(`PDF generation failed: ${error.message}`, 'error');

          // Clean up any temporary container that might still exist
          const tempContainer = document.querySelector('div[style*="-9999px"]');
          if (tempContainer && tempContainer.parentNode) {
              tempContainer.parentNode.removeChild(tempContainer);
          }

          throw error;
      }
  }

  /**
   * Optimizes an image element for PDF export to prevent hanging.
   * @param {HTMLImageElement} img - The image element to optimize
   * @param {number} index - The image index for logging
   * @param {number} totalImages - Total number of images being processed
   */
  async function optimizeImageForPDF(img, index, totalImages = 0) {
      try {
          // Check if image is a large video screenshot
          const src = img.src;
          const isVideoScreenshot = img.classList.contains('Stickara-video-screenshot') ||
                                   img.getAttribute('data-timestamp') ||
                                   src.includes('data:image');

          if (!isVideoScreenshot) {
              return; // Skip non-video screenshots silently for performance
          }

          // Check image size
          const imageSizeKB = Math.round((src.length * 0.75) / 1024);

          // Aggressive compression thresholds based on total number of images
          let compressionThreshold = 2048; // 2MB default
          let compressionQuality = 0.7; // 70% default
          let maxDimensions = { width: 1920, height: 1080 };

          if (totalImages > 50) {
              // Very aggressive for 50+ images
              compressionThreshold = 512; // 512KB
              compressionQuality = 0.5; // 50% quality
              maxDimensions = { width: 1280, height: 720 };
          } else if (totalImages > 20) {
              // Aggressive for 20+ images
              compressionThreshold = 1024; // 1MB
              compressionQuality = 0.6; // 60% quality
              maxDimensions = { width: 1600, height: 900 };
          } else if (totalImages > 10) {
              // Moderate compression for 10+ images
              compressionThreshold = 1536; // 1.5MB
              compressionQuality = 0.65; // 65% quality
              maxDimensions = { width: 1920, height: 1080 };
          }

          // Apply compression if needed
          if (imageSizeKB > compressionThreshold) {
              if (totalImages <= 10) {
                  console.log(`PDF Export: Compressing image ${index} (${imageSizeKB}KB)`);
              }

              const compressedSrc = await compressImageForPDF(src, compressionQuality, maxDimensions);
              if (compressedSrc && compressedSrc !== src) {
                  img.src = compressedSrc;
                  const newSizeKB = Math.round((compressedSrc.length * 0.75) / 1024);
                  if (totalImages <= 10) {
                      console.log(`PDF Export: Image ${index} compressed from ${imageSizeKB}KB to ${newSizeKB}KB`);
                  }
              }
          }

          // Ensure image has reasonable dimensions for PDF
          if (img.naturalWidth > maxDimensions.width || img.naturalHeight > maxDimensions.height) {
              img.style.maxWidth = `${maxDimensions.width}px`;
              img.style.maxHeight = `${maxDimensions.height}px`;
              img.style.width = 'auto';
              img.style.height = 'auto';
          }

          // Force smaller display size for many images
          if (totalImages > 30) {
              img.style.maxWidth = '800px';
              img.style.maxHeight = '600px';
          }

      } catch (error) {
          console.error(`PDF Export: Error optimizing image ${index}:`, error);
      }
  }

  /**
   * Compresses an image data URL for PDF export.
   * @param {string} dataUrl - The original image data URL
   * @param {number} quality - Compression quality (0-1)
   * @param {object} maxDimensions - Maximum dimensions {width, height}
   * @returns {Promise<string>} Compressed image data URL
   */
  async function compressImageForPDF(dataUrl, quality = 0.7, maxDimensions = { width: 1920, height: 1080 }) {
      return new Promise((resolve) => {
          try {
              const img = new Image();
              img.onload = () => {
                  try {
                      const canvas = document.createElement('canvas');
                      const ctx = canvas.getContext('2d', { willReadFrequently: true });

                      // Calculate new dimensions based on provided max dimensions
                      let { width, height } = img;
                      const maxWidth = maxDimensions.width;
                      const maxHeight = maxDimensions.height;

                      if (width > maxWidth || height > maxHeight) {
                          const ratio = Math.min(maxWidth / width, maxHeight / height);
                          width = Math.floor(width * ratio);
                          height = Math.floor(height * ratio);
                      }

                      canvas.width = width;
                      canvas.height = height;

                      // Draw and compress
                      ctx.drawImage(img, 0, 0, width, height);
                      const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

                      resolve(compressedDataUrl);
                  } catch (error) {
                      console.error('PDF Export: Error in image compression:', error);
                      resolve(dataUrl); // Return original on error
                  }
              };
              img.onerror = () => {
                  console.error('PDF Export: Error loading image for compression');
                  resolve(dataUrl); // Return original on error
              };
              img.src = dataUrl;
          } catch (error) {
              console.error('PDF Export: Error setting up image compression:', error);
              resolve(dataUrl); // Return original on error
          }
      });
  }

  /**
   * Compresses a canvas for PDF export to prevent memory issues.
   * @param {HTMLCanvasElement} canvas - The canvas to compress
   * @returns {Promise<string>} Compressed canvas data URL
   */
  async function compressCanvasForPDF(canvas) {
      return new Promise((resolve) => {
          try {
              // Create a smaller canvas for compression
              const compressCanvas = document.createElement('canvas');
              const compressCtx = compressCanvas.getContext('2d', { willReadFrequently: true });

              // Reduce canvas size by 50% to manage memory
              compressCanvas.width = Math.floor(canvas.width * 0.5);
              compressCanvas.height = Math.floor(canvas.height * 0.5);

              // Draw the original canvas onto the smaller one
              compressCtx.drawImage(canvas, 0, 0, compressCanvas.width, compressCanvas.height);

              // Convert with compression
              const compressedDataUrl = compressCanvas.toDataURL('image/jpeg', 0.8);

              console.log(`PDF Export: Canvas compressed from ${canvas.width}x${canvas.height} to ${compressCanvas.width}x${compressCanvas.height}`);
              resolve(compressedDataUrl);

          } catch (error) {
              console.error('PDF Export: Error compressing canvas:', error);
              // Fallback to original with lower quality
              resolve(canvas.toDataURL('image/jpeg', 0.6));
          }
      });
  }

  /**
   * Generates PDF in chunks for very large notes to prevent hanging.
   * @param {HTMLElement} container - The container element
   * @param {string} filename - The PDF filename
   */
  async function generateChunkedPDF(container, filename) {
      try {
          console.log('PDF Export: Starting chunked PDF generation');

          // Create PDF instance
          const { jsPDF } = window.jspdf;
          const pdf = new jsPDF({
              orientation: 'portrait',
              unit: 'mm',
              format: 'a4'
          });

          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();

          // Split content into manageable chunks
          const chunkHeight = 3000; // 3000px chunks
          const totalHeight = container.scrollHeight;
          const chunks = Math.ceil(totalHeight / chunkHeight);

          console.log(`PDF Export: Splitting into ${chunks} chunks of ${chunkHeight}px each`);

          let isFirstPage = true;

          for (let i = 0; i < chunks; i++) {
              const startY = i * chunkHeight;
              const endY = Math.min(startY + chunkHeight, totalHeight);
              const actualChunkHeight = endY - startY;

              console.log(`PDF Export: Processing chunk ${i + 1}/${chunks} (${startY}-${endY}px)`);
              showStatus(`Processing chunk ${i + 1} of ${chunks}...`, 'info');

              // Create a temporary container for this chunk
              const chunkContainer = document.createElement('div');
              chunkContainer.style.cssText = `
                  position: absolute;
                  left: -9999px;
                  top: 0;
                  width: 1200px;
                  height: ${actualChunkHeight}px;
                  overflow: hidden;
                  background: white;
              `;

              // Clone the content and position it for this chunk
              const clonedContent = container.cloneNode(true);
              clonedContent.style.position = 'relative';
              clonedContent.style.top = `-${startY}px`;
              clonedContent.style.width = '1200px';

              chunkContainer.appendChild(clonedContent);
              document.body.appendChild(chunkContainer);

              try {
                  // Capture this chunk with html2canvas
                  const chunkCanvas = await html2canvas(chunkContainer, {
                      width: 1200,
                      height: actualChunkHeight,
                      scale: 0.8, // Lower scale for chunked processing
                      useCORS: true,
                      allowTaint: true,
                      backgroundColor: '#ffffff',
                      logging: false,
                      imageTimeout: 20000,
                      removeContainer: false,
                      foreignObjectRendering: true,
                      scrollX: 0,
                      scrollY: 0,
                      windowWidth: 1200,
                      windowHeight: actualChunkHeight
                  });

                  // Convert chunk to image data
                  const chunkImgData = chunkCanvas.toDataURL('image/jpeg', 0.7); // Use JPEG for better compression

                  // Calculate scaling for PDF
                  const chunkScale = pdfWidth / (chunkCanvas.width / 0.8); // Adjust for scale factor
                  const scaledHeight = (chunkCanvas.height / 0.8) * chunkScale;

                  // Add page if not first
                  if (!isFirstPage) {
                      pdf.addPage();
                  }
                  isFirstPage = false;

                  // Add chunk to PDF
                  if (scaledHeight <= pdfHeight) {
                      // Chunk fits on one page
                      pdf.addImage(chunkImgData, 'JPEG', 0, 0, pdfWidth, scaledHeight);
                  } else {
                      // Chunk needs multiple pages
                      let yPosition = 0;
                      let pageCount = 0;

                      while (yPosition < scaledHeight) {
                          if (pageCount > 0) {
                              pdf.addPage();
                          }

                          pdf.addImage(chunkImgData, 'JPEG', 0, -yPosition, pdfWidth, scaledHeight);
                          yPosition += pdfHeight;
                          pageCount++;
                      }
                  }

              } finally {
                  // Clean up chunk container
                  document.body.removeChild(chunkContainer);
              }

              // Allow UI to update and garbage collection
              await new Promise(resolve => setTimeout(resolve, 100));
              if (window.gc) {
                  window.gc();
              }
          }

          // Generate and download PDF
          console.log('PDF Export: Generating final PDF blob');
          showStatus('Finalizing chunked PDF...', 'info');

          const pdfBlob = pdf.output('blob');

          if (!pdfBlob || pdfBlob.size === 0) {
              throw new Error('Failed to generate PDF blob');
          }

          console.log(`PDF Export: Chunked PDF blob size: ${Math.round(pdfBlob.size / 1024)}KB`);

          const objectUrl = URL.createObjectURL(pdfBlob);

          chrome.downloads.download({
              url: objectUrl,
              filename: filename,
              saveAs: true
          }, (id) => {
              URL.revokeObjectURL(objectUrl);
              if (chrome.runtime.lastError) {
                  console.error('PDF Export: Download failed:', chrome.runtime.lastError);
                  showStatus(`PDF export failed: ${chrome.runtime.lastError.message}`, 'error');
              } else if (!id) {
                  console.warn('PDF Export: Download cancelled or blocked');
                  showStatus('PDF export may have been cancelled or blocked.', 'info');
              } else {
                  console.log('PDF Export: Chunked PDF download completed successfully');
                  showStatus('Large note PDF export completed successfully!', 'success');
              }
          });

      } catch (error) {
          console.error('PDF Export: Error in chunked PDF generation:', error);
          showStatus(`Chunked PDF generation failed: ${error.message}`, 'error');
          throw error;
      }
  }

  /**
   * Process HTML content for plain PDF export, removing all formatting
   * @param {string} html - The HTML content to process
   * @param {object} pdf - The jsPDF instance
   * @param {number} x - The x position to start drawing
   * @param {number} y - The y position to start drawing
   * @param {number} maxWidth - The maximum width for text
   * @returns {number} - The new y position after drawing all content
   */
  function processPlainTextForPDF(html, pdf, x, y, maxWidth) {
      if (!html) return y;

      // Get plain text content only
      const plainText = getPlainText(html, 'txt');

      // Split text into lines that fit within the page width
      const textLines = pdf.splitTextToSize(plainText, maxWidth);

      // Set simple text style
      pdf.setFont("helvetica", "normal");
      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0); // Black text only

      // Draw the text without any formatting
      let currentY = y;
      const lineHeight = 6;

      for (let i = 0; i < textLines.length; i++) {
          const line = textLines[i];
          pdf.text(line, x, currentY);
          currentY += lineHeight;
      }

      return currentY;
  }

  /**
   * Process HTML content for PDF export, preserving formatting like highlights, bold, italic, etc.
   * @param {string} html - The HTML content to process
   * @param {object} pdf - The jsPDF instance
   * @param {number} x - The x position to start drawing
   * @param {number} y - The y position to start drawing
   * @param {number} maxWidth - The maximum width for text
   * @returns {number} - The new y position after drawing all content
   */
  function processFormattedTextForPDF(html, pdf, x, y, maxWidth) {
      if (!html) return y;

      // Create a temporary div to parse the HTML
      const tempDiv = document.createElement('div');

      // Replace HTML line breaks with newlines before setting innerHTML
      html = html.replace(/<br\s*\/?>/gi, '\n');
      html = html.replace(/<\/p>\s*<p/gi, '</p>\n<p');
      html = html.replace(/<\/div>\s*<div/gi, '</div>\n<div');
      html = html.replace(/<\/li>\s*<li/gi, '</li>\n<li');

      tempDiv.innerHTML = html;

      // Get the plain text content and preserve line breaks
      let plainText = tempDiv.textContent || tempDiv.innerText || '';

      // Ensure paragraphs have line breaks
      plainText = plainText.replace(/\n\s*\n/g, '\n\n'); // Normalize multiple line breaks

      // Create a smaller width to prevent overflow
      const adjustedMaxWidth = maxWidth * 0.9; // 90% of the original width

      // Split text into lines
      const textLines = pdf.splitTextToSize(plainText, adjustedMaxWidth);

      // Set default text style
      pdf.setFont("helvetica", "normal");
      pdf.setTextColor(0, 0, 0);

      // Draw the text
      let currentY = y;
      const lineHeight = 7;

      // First pass: Draw all text
      for (let i = 0; i < textLines.length; i++) {
          // Don't trim the line to preserve indentation
          const line = textLines[i];

          // Handle empty lines as paragraph breaks
          if (!line.trim()) {
              currentY += lineHeight;
              continue;
          }

          // Handle code blocks
          if (line.startsWith('    ') || line.startsWith('\t') || line.includes('`')) {
              // Draw code block background
              pdf.setFillColor(240, 240, 240);
              const textWidth = pdf.getStringUnitWidth(line) * pdf.getFontSize() / pdf.internal.scaleFactor;
              pdf.rect(x - 2, currentY - 4, textWidth + 4, lineHeight + 2, 'F');
              pdf.setDrawColor(200, 200, 200);
              pdf.rect(x - 2, currentY - 4, textWidth + 4, lineHeight + 2, 'S');

              // Use monospace font for code
              pdf.setFont("courier", "normal");
              pdf.text(line, x, currentY);
              pdf.setFont("helvetica", "normal");
          }
          // Handle checklist items
          else if (line.startsWith('☐') || line.startsWith('☑') ||
                  line.startsWith('□') || line.startsWith('✓')) {

              const isChecked = line.startsWith('☑') || line.startsWith('✓');

              // Draw checkbox
              pdf.setDrawColor(100, 100, 100);
              pdf.rect(x, currentY - 3, 5, 5, 'S');

              if (isChecked) {
                  // Draw checkmark
                  pdf.setDrawColor(0, 150, 0);
                  pdf.line(x + 1, currentY - 1, x + 2, currentY + 1);
                  pdf.line(x + 2, currentY + 1, x + 4, currentY - 2);
              }

              // Draw the text without the checkbox character
              const textWithoutCheckbox = line.substring(1).trim();
              pdf.text(textWithoutCheckbox, x + 8, currentY);
          }
          // Handle blockquotes
          else if (line.startsWith('>')) {
              // Draw blockquote
              pdf.setFillColor(245, 245, 245);
              const textWidth = pdf.getStringUnitWidth(line.substring(1).trim()) * pdf.getFontSize() / pdf.internal.scaleFactor;
              pdf.rect(x - 2, currentY - 4, textWidth + 4, lineHeight + 2, 'F');

              // Draw left border
              pdf.setDrawColor(180, 180, 180);
              pdf.setLineWidth(2);
              pdf.line(x - 2, currentY - 4, x - 2, currentY + 3);
              pdf.setLineWidth(0.2);

              // Draw the text without the blockquote marker
              pdf.setFont("helvetica", "italic");
              pdf.text(line.substring(1).trim(), x + 2, currentY);
              pdf.setFont("helvetica", "normal");
          }
          else {
              // Regular text
              pdf.text(line, x, currentY);
          }

          currentY += lineHeight;
      }

      // Reset Y position for second pass
      currentY = y;

      // Second pass: Apply highlights
      // Find all mark tags in the HTML
      const markRegex = /<mark[^>]*?data-color="([^"]*)"[^>]*>([\s\S]*?)<\/mark>|<mark[^>]*?class="[^"]*?color-([^"]*?)"[^>]*>([\s\S]*?)<\/mark>|<mark[^>]*>([\s\S]*?)<\/mark>/gi;

      let match;
      const highlights = [];

      // Extract all highlights
      while ((match = markRegex.exec(html)) !== null) {
          const color = match[1] || match[3] || "yellow"; // Get color from data-color or class
          const content = match[2] || match[4] || match[5]; // Get the highlighted content

          if (content) {
              // Create a temporary div to get the plain text
              const tempHighlightDiv = document.createElement('div');
              tempHighlightDiv.innerHTML = content;
              const highlightText = tempHighlightDiv.textContent || tempHighlightDiv.innerText || content;

              highlights.push({
                  text: highlightText.trim(),
                  color: color
              });
          }
      }

      // Also check for spans with Stickara-in-note-highlight class
      const spanRegex = /<[^>]*?class="[^"]*?Stickara-in-note-highlight[^"]*?color-([^"]*?)"[^>]*>([\s\S]*?)<\/[^>]*>/gi;

      while ((match = spanRegex.exec(html)) !== null) {
          const color = match[1] || "yellow";
          const content = match[2];

          if (content) {
              // Create a temporary div to get the plain text
              const tempHighlightDiv = document.createElement('div');
              tempHighlightDiv.innerHTML = content;
              const highlightText = tempHighlightDiv.textContent || tempHighlightDiv.innerText || content;

              highlights.push({
                  text: highlightText.trim(),
                  color: color
              });
          }
      }

      // Apply highlights to the text
      for (let i = 0; i < textLines.length; i++) {
          // Don't trim the line to preserve indentation and exact text matching
          const line = textLines[i];

          // Handle empty lines as paragraph breaks
          if (!line.trim()) {
              currentY += lineHeight;
              continue;
          }

          // Skip special formatting
          if (line.startsWith('    ') || line.startsWith('\t') || line.includes('`') ||
              line.startsWith('☐') || line.startsWith('☑') || line.startsWith('□') ||
              line.startsWith('✓') || line.startsWith('>')) {
              currentY += lineHeight;
              continue;
          }

          // Check if this line contains any highlighted text
          for (const highlight of highlights) {
              if (line.includes(highlight.text)) {
                  // Find the position of the highlight in the line
                  const startIndex = line.indexOf(highlight.text);
                  if (startIndex !== -1) {
                      // Calculate the width and position of the highlight
                      const beforeText = line.substring(0, startIndex);
                      const highlightWidth = pdf.getStringUnitWidth(highlight.text) * pdf.getFontSize() / pdf.internal.scaleFactor;
                      const highlightX = x + pdf.getStringUnitWidth(beforeText) * pdf.getFontSize() / pdf.internal.scaleFactor;

                      // Set the fill color based on the highlight color
                      switch (highlight.color.toLowerCase()) {
                          case "pink":
                              pdf.setFillColor(255, 192, 203); // Pink
                              break;
                          case "green":
                              pdf.setFillColor(144, 238, 144); // Light green
                              break;
                          case "blue":
                              pdf.setFillColor(173, 216, 230); // Light blue
                              break;
                          case "purple":
                              pdf.setFillColor(221, 160, 221); // Plum
                              break;
                          case "orange":
                              pdf.setFillColor(255, 215, 140); // Light orange
                              break;
                          default:
                              pdf.setFillColor(255, 255, 150); // Light yellow (default)
                              break;
                      }

                      // Draw the highlight background
                      pdf.rect(highlightX, currentY - 4, highlightWidth, lineHeight + 2, 'F');

                      // Redraw the text on top of the highlight to ensure it's visible
                      pdf.text(highlight.text, highlightX, currentY);
                  }
              }
          }

          currentY += lineHeight;
      }

      return currentY;
  }




   /**
    * Placeholder function for HTML to Markdown conversion.
    * In a real scenario, this would call the function from content-utils.js via messaging.
    * For simplicity in the popup, we might have to rely on a less accurate conversion here
    * or just export plain text for Markdown option from the popup.
    *
    * UPDATE: Since converters are in content script, we'll need messaging or
    * duplicate the logic. Let's duplicate simplified converters for popup use,
    * acknowledging potential differences from the content script version.
    */
   function convertToMarkdownPopup(htmlContent) {
       if (!htmlContent) return '';
       // Simplified converter for popup (less robust than content script version)
       let md = htmlContent;
        // Basic replacements - order matters!
        md = md.replace(/<style[^>]*>.*<\/style>/gi, ''); // Remove style blocks
        md = md.replace(/<script[^>]*>.*<\/script>/gi, ''); // Remove script blocks
        md = md.replace(/<!--.*?-->/gs, ''); // Remove comments

        // Block elements
        md = md.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n# $1\n\n');
        md = md.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n## $1\n\n');
        md = md.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n### $1\n\n');
        md = md.replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n#### $1\n\n');
        md = md.replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n##### $1\n\n');
        md = md.replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n###### $1\n\n');
        md = md.replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n\n');
        md = md.replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, (_, content) => '\n> ' + content.replace(/<br\s*\/?>/gi, '\n> ').replace(/<[^>]+>/g, '').trim() + '\n\n'); // Basic blockquote, strip inner tags
        md = md.replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gsi, '\n```\n$1\n```\n\n'); // Code blocks
        md = md.replace(/<hr[^>]*>/gi, '\n---\n\n');

        // Lists (simplified - doesn't handle nesting well)
        md = md.replace(/<ul[^>]*>(.*?)<\/ul>/gsi, (_, content) => content.replace(/<li[^>]*>(.*?)<\/li>/gi, (__, liContent) => '\n- ' + convertToMarkdownPopup(liContent).trim()).trim() + '\n\n');
        md = md.replace(/<ol[^>]*>(.*?)<\/ol>/gsi, (_, content) => {
            let itemCounter = 1;
            return content.replace(/<li[^>]*>(.*?)<\/li>/gi, (__, liContent) => `\n${itemCounter++}. ` + convertToMarkdownPopup(liContent).trim()).trim() + '\n\n';
        });

        // Inline elements
        md = md.replace(/<(strong|b)>(.*?)<\/(strong|b)>/gi, '**$2**');
        md = md.replace(/<(em|i)>(.*?)<\/(em|i)>/gi, '*$2*');
        md = md.replace(/<(del|s)>(.*?)<\/(del|s)>/gi, '~~$2~~');
        md = md.replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`');
        md = md.replace(/<mark[^>]*>(.*?)<\/mark>/gi, '==$1=='); // Highlight
        md = md.replace(/<a[^>]*href=["'](.*?)["'][^>]*>(.*?)<\/a>/gi, '[$2]($1)'); // Links
        md = md.replace(/<img[^>]*src=["'](.*?)["'][^>]*alt=["'](.*?)["'][^>]*>/gi, '![$2]($1)'); // Images

        // Cleanup <br> and other tags
        md = md.replace(/<br\s*\/?>/gi, '  \n'); // Line breaks
        md = md.replace(/<[^>]+>/g, ''); // Remove remaining HTML tags

        // Decode entities and clean up whitespace
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = md;
        md = tempDiv.textContent || tempDiv.innerText || "";
        md = md.replace(/(\n\s*){3,}/g, '\n\n').trim(); // Collapse blank lines
        return md;
   }

   /** Placeholder for basic HTML export wrapper (similar to content-utils) */
   function convertToBasicHTMLPopup(htmlContent, pageTitle = 'Stickara Note') {
        const basicCSS = `body { font-family: sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: auto; } h1, h2, h3 { margin-top: 1.5em; margin-bottom: 0.5em; } blockquote { border-left: 4px solid #ccc; padding-left: 1em; margin-left: 0; color: #555; font-style: italic; } pre { background-color: #f4f4f4; border: 1px solid #ddd; padding: 10px; border-radius: 4px; overflow-x: auto; } code { font-family: monospace; } mark { background-color: yellow; padding: 0.1em 0.2em; } img { max-width: 100%; height: auto; display: block; margin: 10px 0; } table { border-collapse: collapse; width: 100%; margin-bottom: 1em; } th, td { border: 1px solid #ccc; padding: 8px; text-align: left; } th { background-color: #f2f2f2; } .Stickara-equation { border: 1px dashed #ccc; padding: 2px 5px; display: inline-block; }`;
        return `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>${pageTitle.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</title><style>${basicCSS}</style></head><body>${htmlContent}</body></html>`;
   }

   /**
    * Parses the raw search query into filters and general query terms.
    * @param {string} rawQuery - The user's input query.
    * @returns {object} Object with { filters: { tag: [], url: [], color: [], reminder: null/true/false, date: null/string }, generalQuery: "..." }
    */
   function parseQuery(rawQuery) {
       const filters = {
           tag: [],
           url: [],
           color: [],
           reminder: null, // null = don't filter, true = has reminder, false = no reminder
           date: null // Store raw date filter string (e.g., ">2023-10-26")
       };
       let generalQueryParts = [];
       // Regex to find key:value pairs, handling quotes
       const operatorRegex = /(\w+):(?:"([^"]+)"|([^\s"]+))/g;
       let lastIndex = 0;
       let match;

       while ((match = operatorRegex.exec(rawQuery)) !== null) {
           // Add text between operators (or start) to general query
           const precedingText = rawQuery.substring(lastIndex, match.index).trim();
           if (precedingText) {
               generalQueryParts.push(precedingText);
           }

           const key = match[1].toLowerCase();
           const value = match[2] || match[3]; // match[2] is quoted, match[3] is non-quoted

           switch (key) {
               case 'tag':
               case 'tags':
                   filters.tag.push(value.toLowerCase());
                   break;
               case 'url':
                   filters.url.push(value.toLowerCase());
                   break;
               case 'color':
                   filters.color.push(value.toLowerCase());
                   break;
               case 'reminder':
                   if (value.toLowerCase() === 'true' || value.toLowerCase() === 'yes' || value.toLowerCase() === 'any') {
                       filters.reminder = true;
                   } else if (value.toLowerCase() === 'false' || value.toLowerCase() === 'no' || value.toLowerCase() === 'none') {
                       filters.reminder = false;
                   }
                   break;
               case 'date': // Handles >, <, or specific date
                   filters.date = value;
                   break;
           }
           lastIndex = operatorRegex.lastIndex;
       }

       // Add any remaining text after the last operator
       const remainingText = rawQuery.substring(lastIndex).trim();
       if (remainingText) {
           generalQueryParts.push(remainingText);
       }

       return {
           filters: filters,
           generalQuery: generalQueryParts.join(' ').trim().toLowerCase() // Lowercase general query for matching
       };
   }

   /**
    * Checks if a note's date matches the date filter string.
    * @param {Date|null} noteDate - The Date object from the note's lastSaved.
    * @param {string|null} dateFilter - The filter string (e.g., ">2023-10-26", "<2023-10-27", "2023-10-26").
    * @returns {boolean} True if the note matches the filter, false otherwise.
    */
   function matchDateFilter(noteDate, dateFilter) {
      if (!dateFilter || !noteDate || isNaN(noteDate.getTime())) return true; // No filter or invalid note date -> pass

      const noteTimestamp = noteDate.getTime();
      let filterTimestamp;
      let comparisonType = 'exact'; // 'exact', 'after', 'before', 'after_inclusive', 'before_inclusive'

      // Check for operators with inclusive first
      if (dateFilter.startsWith('>=')) {
          comparisonType = 'after_inclusive';
          dateFilter = dateFilter.substring(2);
      } else if (dateFilter.startsWith('<=')) {
          comparisonType = 'before_inclusive';
          dateFilter = dateFilter.substring(2);
      } else if (dateFilter.startsWith('>')) {
          comparisonType = 'after';
          dateFilter = dateFilter.substring(1);
      } else if (dateFilter.startsWith('<')) {
          comparisonType = 'before';
          dateFilter = dateFilter.substring(1);
      }

      try {
          // Try parsing as YYYY-MM-DD. Add time parts for comparisons.
          const datePart = dateFilter.split('T')[0]; // Handle potential time part, just use date
          // Create date in UTC to avoid timezone issues with comparisons
          const filterDate = new Date(datePart + 'T00:00:00Z');
          if (isNaN(filterDate.getTime())) return true; // Invalid filter date -> pass

          filterTimestamp = filterDate.getTime();
          // Use UTC day boundaries for comparison
          const dayInMillis = 24 * 60 * 60 * 1000;
          const endOfDayTimestamp = filterTimestamp + dayInMillis - 1;

          switch (comparisonType) {
              case 'exact':
                  // Match anywhere within that UTC day
                  return noteTimestamp >= filterTimestamp && noteTimestamp <= endOfDayTimestamp;
              case 'after':
                  // Strictly after the *end* of the specified UTC day
                  return noteTimestamp > endOfDayTimestamp;
               case 'after_inclusive':
                  // On or after the *start* of the specified UTC day
                  return noteTimestamp >= filterTimestamp;
              case 'before':
                  // Strictly before the *start* of the specified UTC day
                  return noteTimestamp < filterTimestamp;
               case 'before_inclusive':
                   // On or before the *end* of the specified UTC day
                  return noteTimestamp <= endOfDayTimestamp;
              default: return true; // Should not happen
          }
      } catch (e) {
          console.warn("Date filter parsing error:", e);
          return true; // Invalid filter -> pass
      }
   }

  /**
   * Creates a DOM element for a single search result (for the popup quick search).
   * @param {string} key - The storage key for the note.
   * @param {object} note - The note data object.
   * @param {string} query - The search query for highlighting (should be the general query part).
   * @returns {HTMLElement|null} - The result item div, or null if key is invalid.
   */
  function createSearchResultElement(key, note, query) {
       const keyPrefix = STORAGE_KEY_PREFIX;
       if (!key.startsWith(keyPrefix)) return null;

       // Extract URL and note index carefully
       const keySuffix = key.substring(keyPrefix.length);
       const lastNoteMarker = keySuffix.lastIndexOf('_note');
       if (lastNoteMarker === -1 || lastNoteMarker === 0) {
           console.warn("Could not parse key format:", key);
           return null; // Invalid format
       }

       let url;
       try {
            url = decodeURIComponent(keySuffix.substring(0, lastNoteMarker));
       } catch (e) {
            console.warn("Could not decode URL from key:", keySuffix.substring(0, lastNoteMarker), e);
            url = keySuffix.substring(0, lastNoteMarker); // Use raw if decode fails
       }
       const noteIndexStr = keySuffix.substring(lastNoteMarker + 5);
       const noteIndex = parseInt(noteIndexStr, 10);
       if (isNaN(noteIndex) || !url) {
            console.warn("Could not parse URL or index from key:", key);
            return null; // Invalid format
       }

       // Get plain text snippet and safely highlight matches based on the general query
       const plainText = getPlainText(note.text || '');

       // Enhanced safe highlighting using the new helper function
       let displaySnippet = '';
       try {
           // Use the new safe highlighting function
           displaySnippet = safeHighlightSearchTerms(plainText, query);
       } catch (e) {
           console.warn("Error during safe highlighting:", e);
           // Fallback to escaped text
           displaySnippet = escapeHtml(plainText);
       }

       // Limit snippet length for popup view
       const truncatedSnippet = displaySnippet.substring(0, 150) + (plainText.length > 150 ? '...' : '');

       // --- Create result element ---
       const resultDiv = document.createElement('div');
       resultDiv.className = 'result-item';

       const urlLink = document.createElement('a');
       urlLink.className = 'result-url';
       urlLink.href = '#'; // Prevent default navigation
       // With the new storage approach, URLs are stored in raw format
       // so we can display them directly without decoding
       urlLink.textContent = url;
       urlLink.title = `Click to open Note ${noteIndex} on this page (opens or switches to tab)`;
       urlLink.addEventListener('click', (e) => {
           e.preventDefault();
           openNoteInTab(url, noteIndex); // Use the helper function
       });

       const detailsSpan = document.createElement('span');
       detailsSpan.className = 'result-details';
       let detailsText = `Note ${noteIndex}`;
       if (note.globallyPinned) detailsText += ' ⭐'; // Add star icon if important note
       if (note.color) detailsText += ` | ${note.color.charAt(0).toUpperCase() + note.color.slice(1)}`;
       if (note.tags && note.tags.length > 0) detailsText += ` | Tags: ${note.tags.join(', ')}`;
       if (note.reminder) {
          try { detailsText += ` | Reminder: ${new Date(note.reminder).toLocaleDateString()}` } catch(e) {}
      }
      if (note.lastSaved) {
          // Ensure lastSaved is valid before formatting
          const savedDate = new Date(note.lastSaved);
          if (!isNaN(savedDate.getTime())) {
              try { detailsText += ` | Saved: ${savedDate.toLocaleDateString()}`; } catch (e) {}
          }
      }
       detailsSpan.textContent = detailsText;

       const snippetP = document.createElement('p');
       snippetP.className = 'result-snippet';
       snippetP.innerHTML = truncatedSnippet || '[Empty Note]'; // Use innerHTML for <mark> but with safe content

       resultDiv.appendChild(urlLink);
       resultDiv.appendChild(detailsSpan);
       resultDiv.appendChild(snippetP);

      return resultDiv;
  }

  // URL fixing functions removed - using direct URL handling

  /**
   * Opens the specific note's URL in a tab, focusing if exists, creating if not.
   * @param {string} url - The raw URL associated with the note.
   * @param {number} noteIndex - The index of the note (optional, for potential future use).
   */
  function openNoteInTab(url, noteIndex) {
       console.log(`--- openNoteInTab ---`);
       console.log(`Request to open URL: ${url} for Note ${noteIndex}`);

       // With the new storage approach, URLs are stored in raw format
       // so we can use them directly without complex processing
       let targetUrl = url;

       // Basic validation - ensure URL has a protocol
       if (!targetUrl.includes('://')) {
           targetUrl = 'http://' + targetUrl;
           console.log(`Added http:// prefix to URL: ${targetUrl}`);
       }

       console.log(`Using URL: ${targetUrl}`);
       chrome.tabs.query({ url: targetUrl }, (existingTabs) => {
           console.log(`Querying for existing tabs with URL: ${targetUrl}`);
           if (chrome.runtime.lastError) {
               console.error("Error during tabs.query:", chrome.runtime.lastError.message);
               showStatus(`Error finding tab: ${chrome.runtime.lastError.message}. Trying to create...`, 'error');
               chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                  if(chrome.runtime.lastError || !newTab) {
                      console.error("Fallback tab creation failed:", chrome.runtime.lastError?.message || "No new tab info");
                      showStatus(`Failed to open URL: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');

                      // Try a different approach as a last resort
                      try {
                          // Try to open in current window
                          window.open(targetUrl, '_blank');
                      } catch (windowErr) {
                          console.error("Window.open fallback also failed:", windowErr);
                      }
                  }
               });
               return;
           }
           console.log(`Found ${existingTabs ? existingTabs.length : 0} existing tabs.`);
           if (existingTabs && existingTabs.length > 0) {
               const targetTab = existingTabs[0];
               console.log(`Found existing tab: ${targetTab.id}. Attempting to update and focus.`);
               chrome.tabs.update(targetTab.id, { active: true }, (updatedTab) => {
                   if (chrome.runtime.lastError || !updatedTab) {
                       console.error(`Error updating tab ${targetTab.id}:`, chrome.runtime.lastError?.message || "No updated tab info received");
                       showStatus(`Error switching to tab. Trying to create...`, 'error');
                       chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                           if(chrome.runtime.lastError || !newTab) {
                               console.error("Fallback tab creation failed:", chrome.runtime.lastError?.message || "No new tab info");
                               showStatus(`Failed to open URL: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');

                               // Try a different approach as a last resort
                               try {
                                   // Try to open in current window
                                   window.open(targetUrl, '_blank');
                               } catch (windowErr) {
                                   console.error("Window.open fallback also failed:", windowErr);
                               }
                           }
                       }); // Fallback create
                   } else {
                       console.log(`Tab ${updatedTab.id} activated. Focusing window ${updatedTab.windowId}.`);
                       chrome.windows.update(updatedTab.windowId, { focused: true }, () => {
                           if (chrome.runtime.lastError) console.warn(`Could not focus window ${updatedTab.windowId}:`, chrome.runtime.lastError.message);
                           else console.log(`Window ${updatedTab.windowId} focused.`);
                       });
                   }
               });
           } else {
               console.log(`No existing tabs found. Creating new tab for: ${targetUrl}`);
               chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                   if (chrome.runtime.lastError || !newTab) {
                       console.error("New tab creation failed:", chrome.runtime.lastError?.message || "No new tab info received");
                       showStatus(`Failed to create tab: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');

                       // Try a different approach as a last resort
                       try {
                           // Try to open in current window
                           window.open(targetUrl, '_blank');
                       } catch (windowErr) {
                           console.error("Window.open fallback also failed:", windowErr);

                           // Final fallback - try to copy URL to clipboard
                           try {
                               navigator.clipboard.writeText(targetUrl).then(() => {
                                   showStatus('URL copied to clipboard. Please paste in a new tab.', 'info');
                               }).catch(err => {
                                   console.error("Clipboard copy failed:", err);
                               });
                           } catch (clipboardErr) {
                               console.error("Clipboard API not available:", clipboardErr);
                           }
                       }
                   } else {
                       console.log("New tab created successfully:", newTab.id);
                   }
               });
           }
       });
   }

   /**
    * Renders the important notes into their dedicated section.
    * @param {Array} allNotes - Array of all note objects {key, data}.
    */
   function renderGlobalNotes(allNotes) {
       if (!globalNotesResultsDiv) return; // Guard if element doesn't exist

       globalNotesResultsDiv.innerHTML = ''; // Clear previous important notes
       let globalNotesFragment = document.createDocumentFragment();
       let globalCount = 0;

       // Sort important notes by lastSaved descending before rendering
       const sortedGlobalNotes = allNotes
           .filter(({ data }) => data.globallyPinned === true)
           .sort((a, b) => {
               const timeA = a.data.lastSaved ? new Date(a.data.lastSaved).getTime() : 0;
               const timeB = b.data.lastSaved ? new Date(b.data.lastSaved).getTime() : 0;
               return timeB - timeA; // Descending order
           });

       sortedGlobalNotes.forEach(({ key, data }) => {
           // Pass empty query string "" for highlighting as we don't highlight important notes based on search
           const resultElement = createSearchResultElement(key, data, "");
           if (resultElement) {
               globalNotesFragment.appendChild(resultElement);
               globalCount++;
           }
       });

       if (globalCount === 0) {
           globalNotesResultsDiv.innerHTML = '<p class="no-results">No important notes found.</p>';
       } else {
           globalNotesResultsDiv.appendChild(globalNotesFragment);
       }
   }

  /**
   * Helper function to match date filters
   * @param {Date|null} noteDate - The note's date
   * @param {string} dateFilter - The date filter string (e.g., ">2023-10-26", "<2023-12-01")
   * @returns {boolean} - Whether the note matches the date filter
   */
  function matchDateFilter(noteDate, dateFilter) {
      if (!noteDate || !dateFilter) return false;

      try {
          const now = new Date();

          if (dateFilter.startsWith('>')) {
              const filterDate = new Date(dateFilter.substring(1));
              return noteDate > filterDate;
          } else if (dateFilter.startsWith('<')) {
              const filterDate = new Date(dateFilter.substring(1));
              return noteDate < filterDate;
          } else if (dateFilter.startsWith('=')) {
              const filterDate = new Date(dateFilter.substring(1));
              // Same day comparison
              return noteDate.toDateString() === filterDate.toDateString();
          } else {
              // Default to exact match
              const filterDate = new Date(dateFilter);
              return noteDate.toDateString() === filterDate.toDateString();
          }
      } catch (error) {
          console.warn("Invalid date filter:", dateFilter, error);
          return false;
      }
  }

  /**
   * Safely highlight search terms in text to prevent XSS
   * @param {string} text - The text to highlight
   * @param {string} query - The search query
   * @returns {string} - HTML with highlighted terms
   */
  function safeHighlightSearchTerms(text, query) {
      if (!text || !query) return escapeHtml(text);

      try {
          // First escape the text to prevent XSS
          const escapedText = escapeHtml(text);

          // Escape special regex characters in the query
          const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');

          // Create regex for case-insensitive matching
          const regex = new RegExp(`(${escapedQuery})`, 'gi');

          // Highlight matches with <mark> tags
          return escapedText.replace(regex, '<mark>$1</mark>');
      } catch (error) {
          console.warn("Error highlighting search terms:", error);
          return escapeHtml(text); // Fallback to escaped text
      }
  }

  /**
   * Escape HTML to prevent XSS
   * @param {string} text - Text to escape
   * @returns {string} - Escaped HTML
   */
  function escapeHtml(text) {
      const map = {
          '&': '&amp;',
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#039;'
      };
      return String(text || '').replace(/[&<>"']/g, function(m) { return map[m]; });
  }

  // --- Search Logic ---
  /**
   * Handles the 'Search' button click or Enter key press (for popup quick search).
   * Incorporates advanced search operators. Uses chrome.storage.local.
   * Fetches ALL notes first to render global notes separately.
   */
   function performSearch() {
      // Enhanced guard elements check with detailed logging
      if (!searchQueryInput) {
          console.error("Stickara Popup: Search input element not found");
          return;
      }
      if (!searchResultsDiv) {
          console.error("Stickara Popup: Search results div not found");
          return;
      }
      if (!searchButton) {
          console.error("Stickara Popup: Search button not found");
          return;
      }

      const rawQuery = searchQueryInput.value.trim();

      // Early exit for empty queries
      if (!rawQuery) {
          searchResultsDiv.innerHTML = '<p class="no-results">Enter text or use operators (tag:, url:, date:, color:, reminder:true/false) to search.</p>';
          return;
      }

      const { filters, generalQuery } = parseQuery(rawQuery);

      // Show searching indicator
      searchResultsDiv.innerHTML = '<p class="no-results">Searching...</p>';

      // Disable inputs during search to prevent multiple simultaneous searches
      searchButton.disabled = true;
      searchQueryInput.disabled = true;

      console.log(`Performing popup search for: Filters=`, filters, `General Query="${generalQuery}"`);
      showStatus('Searching all notes...', 'info');

      chrome.storage.local.get(null, function (items) { // <-- Use local storage
           searchButton.disabled = false; // Re-enable buttons regardless of outcome
           searchQueryInput.disabled = false;

           if (chrome.runtime.lastError) {
              console.error("Stickara Popup Error getting all local storage:", chrome.runtime.lastError.message);
              showStatus(`Error accessing storage: ${chrome.runtime.lastError.message}`, 'error');
              searchResultsDiv.innerHTML = '<p class="no-results">Error loading notes.</p>';
              return;
           }

           const allNotes = Object.entries(items)
               .filter(([key, value]) => key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value !== null)
               .map(([key, data]) => ({ key, data }));

           // Global notes are already rendered and are not affected by search.

           // Filter for regular search results, excluding global notes
           let searchMatchCount = 0;
           let searchResultsFragment = document.createDocumentFragment();

           // Sort notes by last saved date descending before filtering/searching
           allNotes.sort((a, b) => {
              const timeA = a.data.lastSaved ? new Date(a.data.lastSaved).getTime() : 0;
              const timeB = b.data.lastSaved ? new Date(b.data.lastSaved).getTime() : 0;
              return timeB - timeA; // Descending
           });

           allNotes.forEach(({ key, data }) => {
                // Include all notes in search results (including globally pinned notes)
                // This ensures consistency with dashboard search behavior

                // Apply Filters logic
                let passesFilters = true;
                // URL Filter
                if (filters.url.length > 0) {
                     let urlPart = '';
                     const keyPrefix = STORAGE_KEY_PREFIX;
                     if (key.startsWith(keyPrefix)) {
                         const keySuffix = key.substring(keyPrefix.length);
                         const lastNoteMarker = keySuffix.lastIndexOf('_note');
                         if (lastNoteMarker > 0) {
                             // With the new storage approach, URLs are stored in raw format
                             // so we can use them directly without decoding
                             urlPart = keySuffix.substring(0, lastNoteMarker).toLowerCase();
                         }
                     }
                     if (!filters.url.every(filterUrl => urlPart.includes(filterUrl))) {
                         passesFilters = false;
                     }
                }
                // Tag Filter
                if (passesFilters && filters.tag.length > 0) {
                    const noteTagsLower = data.tags ? data.tags.map(tag => String(tag).toLowerCase()) : [];
                    if (!filters.tag.every(filterTag => noteTagsLower.some(noteTag => noteTag.includes(filterTag)))) {
                        passesFilters = false;
                    }
                }
                 // Color Filter
                 if (passesFilters && filters.color.length > 0) {
                     const noteColorLower = (data.color || '').toLowerCase();
                     if (!filters.color.every(filterColor => noteColorLower.includes(filterColor))) {
                         passesFilters = false;
                     }
                 }
                 // Reminder Filter - Enhanced logic
                if (passesFilters && filters.reminder !== null) {
                     // Check for reminder in multiple possible formats
                     const hasReminder = !!(
                         (data.reminder && (
                             (typeof data.reminder === 'object' && data.reminder.enabled) ||
                             (typeof data.reminder === 'number' && data.reminder > 0) ||
                             (typeof data.reminder === 'string' && data.reminder.length > 0) ||
                             (typeof data.reminder === 'boolean' && data.reminder)
                         )) ||
                         data.hasReminder ||
                         data.reminderEnabled
                     );
                     if (filters.reminder !== hasReminder) {
                         passesFilters = false;
                     }
                 }
                 // Date Filter
                if (passesFilters && filters.date) {
                    const noteDate = data.lastSaved ? new Date(data.lastSaved) : null;
                    if (!matchDateFilter(noteDate, filters.date)) {
                        passesFilters = false;
                    }
                }

                // Apply General Query logic
                let matchesGeneralQuery = false;
                if (passesFilters) {
                    if (!generalQuery) {
                        matchesGeneralQuery = true; // Pass if no general query and filters passed
                    } else {
                        // Enhanced text extraction - consistent with dashboard logic
                        let plainTextLower = '';

                        // Try multiple data sources to match dashboard behavior
                        const textContent = data.text || data.content || '';
                        if (textContent) {
                            try {
                                // Use the same getPlainText function as dashboard for consistency
                                plainTextLower = (typeof getPlainText === 'function' ? getPlainText(textContent) : textContent).toLowerCase();
                            } catch (e) {
                                console.warn("Error extracting plain text:", e);
                                // Fallback to simple text extraction
                                if (data.content) {
                                    const tempDiv = document.createElement('div');
                                    tempDiv.innerHTML = data.content;
                                    plainTextLower = (tempDiv.textContent || tempDiv.innerText || '').toLowerCase();
                                } else {
                                    plainTextLower = String(textContent).toLowerCase();
                                }
                            }
                        }
                        const tagsLower = data.tags ? data.tags.map(tag => String(tag).toLowerCase()) : [];
                        let urlPartLower = '';
                        const keyPrefix = STORAGE_KEY_PREFIX;
                        if (key.startsWith(keyPrefix)) {
                            const keySuffix = key.substring(keyPrefix.length);
                            const lastNoteMarker = keySuffix.lastIndexOf('_note');
                            if (lastNoteMarker > 0) {
                                // With the new storage approach, URLs are stored in raw format
                                // so we can use them directly without decoding
                                urlPartLower = keySuffix.substring(0, lastNoteMarker).toLowerCase();
                            }
                        }
                        // Match general query against text, tags, URL, and title (consistent with dashboard)
                        const textMatch = plainTextLower.includes(generalQuery);
                        const tagMatch = tagsLower.some(tag => tag.includes(generalQuery));
                        const urlMatch = urlPartLower.includes(generalQuery);
                        const titleMatch = (data.title || '').toLowerCase().includes(generalQuery);
                        matchesGeneralQuery = textMatch || tagMatch || urlMatch || titleMatch;
                    }
                }

                // Add to search results if all criteria met
                if (passesFilters && matchesGeneralQuery) {
                    // Pass generalQuery for highlighting
                    const resultElement = createSearchResultElement(key, data, generalQuery);
                    if (resultElement) {
                        searchResultsFragment.appendChild(resultElement);
                        searchMatchCount++;
                    }
                }
            });

           // Update search results DOM
           searchResultsDiv.innerHTML = ''; // Clear "Searching..." message
           if (searchMatchCount === 0) {
               searchResultsDiv.innerHTML = '<p class="no-results">No matching notes found for your query.</p>';
           } else {
               searchResultsDiv.appendChild(searchResultsFragment);
           }
           // Log search results for debugging (can be removed in production)
           if (searchMatchCount === 0 && rawQuery) {
               console.log(`Popup Search: No results found for query: "${rawQuery}"`);
           }
           showStatus(`Search complete: ${searchMatchCount} result(s) found.`, 'success');
           searchQueryInput.focus();
           // searchQueryInput.select(); // Select sometimes feels jarring, focus might be enough
      });
  }


  // --- Google Drive Sync UI Logic ---
  /**
   * Updates the visibility of Drive Connect/Disconnect buttons and the indicator's
   * base connected/disconnected state and color.
   * @param {boolean} isConnected - Whether the extension believes it's connected.
   */
  function updateSyncButtonUI(isConnected) {
      if (!connectDriveButton || !disconnectDriveButton || !syncStatusIndicator) return;

      if (isConnected) {
          connectDriveButton.style.display = 'none';
          disconnectDriveButton.style.display = 'inline-flex';
          // Set default connected state (green) - remove others first for safety
          syncStatusIndicator.className = 'sync-indicator connected'; // Use base + state class
          syncStatusIndicator.title = "Google Drive Connected";

      } else {
          connectDriveButton.style.display = 'inline-flex';
          disconnectDriveButton.style.display = 'none';
          // Set disconnected state (grey/red)
          syncStatusIndicator.className = 'sync-indicator disconnected'; // Use base + state class
          syncStatusIndicator.title = "Google Drive Disconnected";
      }
  }

   /**
    * Exports highlights from the current page based on the common format selection.
    */
   function exportPageHighlights() {
       if (!exportHighlightsButton || !commonExportFormatSelect) return; // Guard elements

       exportHighlightsButton.disabled = true;
       const selectedFormat = commonExportFormatSelect.value; // *** READ FROM COMMON SELECT ***
       showStatus(`Exporting highlights (${selectedFormat.toUpperCase()})...`, 'info');

       chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
           if (chrome.runtime.lastError || !tabs || tabs.length === 0 || !tabs[0].url || !tabs[0].id) {
               showStatus('Could not get current page details.', 'error');
               exportHighlightsButton.disabled = false;
               return;
           }
           const url = tabs[0].url;
           // Allow http and https protocols
           if (!url || (!url.startsWith('http:') && !url.startsWith('https:'))) {
               showStatus('Cannot export highlights from this type of page.', 'error');
               exportHighlightsButton.disabled = false;
               return;
           }

           const highlightKey = `${HIGHLIGHT_KEY_PREFIX}${url}`;

           chrome.storage.local.get([highlightKey], async function (result) {
               if (chrome.runtime.lastError) {
                   showStatus(`Error getting highlights: ${chrome.runtime.lastError.message}`, 'error');
                   exportHighlightsButton.disabled = false;
                   return;
               }

               const highlights = result[highlightKey];

               if (!highlights || !Array.isArray(highlights) || highlights.length === 0) {
                   showStatus('No highlights found on this page to export.', 'info');
                   exportHighlightsButton.disabled = false;
                   return;
               }

               // Generate filename base
               let filenameBase = `Stickara_highlights_${new Date().toISOString().split('T')[0]}`;
               try {
                   const urlObj = new URL(url);
                   const hostname = urlObj.hostname.replace(/^www\./, '');
                   const safeHostname = hostname.replace(/[^a-z0-9\.]/gi, '_').substring(0, 50);
                   filenameBase = `Stickara_highlights_${safeHostname}`;
               } catch (e) {
                   console.warn("Error creating filename from URL:", e);
               }

               // --- Format Selection Logic ---
               let fileContent = '';
               let fileExtension = '.txt';
               let mimeType = 'text/plain;charset=utf-8';

               // *** Use selectedFormat read from commonExportFormatSelect ***
               if (selectedFormat === 'pdf') {
                  // Check if required libraries are available
                  if (typeof window.jspdf === 'undefined') {
                      showStatus('PDF library not loaded.', 'error');
                      console.error("jsPDF library is required for PDF export but not loaded.");
                      exportHighlightsButton.disabled = false;
                      return;
                  }

                  if (typeof html2canvas === 'undefined') {
                      showStatus('HTML2Canvas library not loaded.', 'error');
                      console.error("html2canvas library is required for styled PDF export but not loaded.");
                      exportHighlightsButton.disabled = false;
                      return;
                  }

                   // Generate the same beautiful HTML content as HTML export
                   let htmlBodyContent = `
<div class="header">
    <h1>Stickara Highlights</h1>
    <div class="export-date">Exported on: ${(() => {
        const exportDate = new Date();
        return !isNaN(exportDate.getTime())
            ? exportDate.toLocaleString()
            : new Date().toISOString().split('T')[0];
    })()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">📄</div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📊</div>
        <div class="meta-content">
            <div class="meta-label">Total Highlights</div>
            <div class="meta-value">${highlights.length}</div>
        </div>
    </div>
</div>`;

                   // Add highlights to the HTML content
                   highlights.forEach((h, index) => {
                       const style = h.style || 'color'; // Default to 'color' style if not specified
                       const color = (style === 'color') ? (h.color || DEFAULT_HIGHLIGHT_COLOR) : null;
                       const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                       // Get style emoji and display text
                       let styleEmoji = '🟨'; // Default yellow highlight
                       let styleText = '';
                       let styleCSS = '';

                       if (style === 'color' && color) {
                           // Color highlight
                           if (color === 'green') styleEmoji = '🟩';
                           else if (color === 'blue') styleEmoji = '🟦';
                           else if (color === 'red') styleEmoji = '🟥';
                           else if (color === 'purple') styleEmoji = '🟪';
                           styleText = `Color: ${colorName}`;
                           styleCSS = `background-color: ${color};`;
                       }
                       else if (style === 'underline') {
                           styleEmoji = '🔽'; // Down arrow for underline
                           styleText = 'Style: Underline';
                           styleCSS = 'text-decoration: underline; text-decoration-color: #2196F3;';
                       }
                       else if (style === 'wavy') {
                           styleEmoji = '〰️'; // Wavy dash
                           styleText = 'Style: Wavy Underline';
                           styleCSS = 'text-decoration: underline wavy; text-decoration-color: #9C27B0;';
                       }
                       else if (style === 'border-thick') {
                           styleEmoji = '🔲'; // Black square button
                           styleText = 'Style: Thick Border';
                           styleCSS = 'border: 2px solid #4CAF50; padding: 2px 4px;';
                       }
                       else if (style === 'strikethrough') {
                           styleEmoji = '❌'; // Cross mark
                           styleText = 'Style: Strikethrough';
                           styleCSS = 'text-decoration: line-through; text-decoration-color: #F44336;';
                       }
                       else {
                           styleEmoji = '📝'; // Memo for unknown styles
                           styleText = `Style: ${style}`;
                           styleCSS = '';
                       }

                       // Process the HTML content to preserve images but escape other HTML
                       let processedHtml = h.text || '[Highlight Text Missing]';

                       // Create a temporary div to extract images
                       const tempDiv = document.createElement('div');
                       tempDiv.innerHTML = processedHtml;

                       // Extract all images
                       const images = Array.from(tempDiv.querySelectorAll('img'));
                       const imageHtml = images.map(img => {
                           // Create a safe image tag with the original src
                           return `<img src="${img.src}" alt="${img.alt || 'Image'}" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 4px;">`;
                       }).join('');

                       // Remove images from the original content
                       images.forEach(img => img.parentNode.removeChild(img));

                       // Get the text content without images and escape it
                       const textContent = tempDiv.innerHTML
                           .replace(/&/g, "&amp;")
                           .replace(/</g, "&lt;")
                           .replace(/>/g, "&gt;")
                           .replace(/"/g, "&quot;")
                           .replace(/'/g, "&#039;")
                           .replace(/\n/g, "<br>"); // Convert newlines to <br>

                       // Combine escaped text with preserved images
                       const escapedText = textContent + (imageHtml ? `<div class="highlight-images">${imageHtml}</div>` : '');

                       // Add timestamp if available
                       const timestampHtml = h.timestamp
                           ? `<div class="highlight-timestamp">Highlighted on: ${new Date(h.timestamp).toLocaleString()}</div>`
                           : '';

                       htmlBodyContent += `
    <div class="highlight-card">
        <div class="highlight-header">
            <div class="highlight-title">${styleEmoji} Highlight ${index + 1}</div>
            <div class="highlight-style">${styleText}</div>
        </div>
        ${timestampHtml}
        <blockquote class="highlight-content" style="${styleCSS} ${style === 'color' ? `border-left: 4px solid ${color};` : ''}">
            ${escapedText}
        </blockquote>
    </div>`;
                   });

                   // Add CSS styling (same as HTML export)
                   const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        min-height: 100vh;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 3px;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .highlight-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 25px 30px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid var(--border-color);
    }

    .highlight-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .highlight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .highlight-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .highlight-style {
        padding: 6px 12px;
        border-radius: 20px;
        background-color: var(--secondary-color);
        color: white;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .highlight-timestamp {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 20px;
        font-style: italic;
        padding: 8px 12px;
        background-color: #f0f0f0;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .highlight-content {
        background-color: var(--light-bg);
        padding: 1.8rem;
        border-radius: 8px;
        margin: 0;
        line-height: 1.8;
        border-left: 5px solid var(--accent-color);
        border: 1px solid #e8e8e8;
        font-size: 1.05rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .highlight-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
    }

    .highlight-images {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e0e0e0;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .highlight-card {
            margin: 10px;
        }
    }
</style>`;

                   // Add footer
                   const footer = `
<footer>
    <p>Generated by Stickara on ${new Date().toLocaleDateString()}</p>
</footer>`;

                   // Combine all parts into complete HTML document
                   const completeHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Highlights</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

                   // Convert styled HTML to PDF
                   const filename = `${filenameBase}.pdf`;
                   try {
                       await convertStyledHtmlToPdf(completeHtmlContent, filename, 'Generating beautiful PDF...');
                       exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button
                   } catch(pdfError) {
                       console.error("Error generating styled PDF:", pdfError);
                       showStatus('PDF generation failed.', 'error');
                       exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button on error
                   }
                   return; // PDF handles its own download and re-enabling

               } else if (selectedFormat === 'md') {
                   // Markdown export
                   fileExtension = '.md';
                   mimeType = 'text/markdown;charset=utf-8';

                   // Create a more visually appealing Markdown header
                   fileContent = `# 🔍 Stickara Highlights\n\n`;
                   fileContent += `> *Exported on: ${new Date().toLocaleString()}*\n\n`;
                   fileContent += `**📄 Page URL:** ${url}\n`;
                   fileContent += `**📊 Total Highlights:** ${highlights.length}\n\n`;
                   fileContent += `---\n\n`;

                   // Initialize markdown content
                   let markdownContent = '';

                   highlights.forEach((h, index) => {
                       const style = h.style || 'color'; // Default to 'color' style if not specified
                       const color = (style === 'color') ? (h.color || DEFAULT_HIGHLIGHT_COLOR) : null;
                       const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                       // Get style emoji and display text
                       let styleEmoji = '🟨'; // Default yellow highlight
                       let styleText = '';

                       if (style === 'color' && color) {
                           // Color highlight
                           if (color === 'green') styleEmoji = '🟩';
                           else if (color === 'blue') styleEmoji = '🟦';
                           else if (color === 'red') styleEmoji = '🟥';
                           else if (color === 'purple') styleEmoji = '🟪';
                           styleText = `Color: ${colorName}`;
                       }
                       else if (style === 'underline') {
                           styleEmoji = '🔽'; // Down arrow for underline
                           styleText = 'Style: Underline';
                       }
                       else if (style === 'wavy') {
                           styleEmoji = '〰️'; // Wavy dash
                           styleText = 'Style: Wavy Underline';
                       }
                       else if (style === 'border-thick') {
                           styleEmoji = '🔲'; // Black square button
                           styleText = 'Style: Thick Border';
                       }
                       else if (style === 'strikethrough') {
                           styleEmoji = '❌'; // Cross mark
                           styleText = 'Style: Strikethrough';
                       }
                       else {
                           styleEmoji = '📝'; // Memo for unknown styles
                           styleText = `Style: ${style}`;
                       }

                       markdownContent += `## ${styleEmoji} Highlight ${index + 1} (${styleText})\n\n`;

                       // Add timestamp if available
                       if (h.timestamp) {
                           markdownContent += `*Highlighted on: ${new Date(h.timestamp).toLocaleString()}*\n\n`;
                       }

                       // Process the HTML content to preserve images in Markdown
                       let processedHtml = h.text || '[Highlight Text Missing]';

                       // Create a temporary div to extract images
                       const tempDiv = document.createElement('div');
                       tempDiv.innerHTML = processedHtml;

                       // Extract all images
                       const images = Array.from(tempDiv.querySelectorAll('img'));
                       let imageMarkdown = '';

                       // Create Markdown for each image
                       images.forEach((img, idx) => {
                           const alt = img.alt || `Image ${idx + 1}`;
                           const src = img.src || '#';
                           imageMarkdown += `\n\n![${alt}](${src})\n\n`;
                       });

                       // Remove images from the original content
                       images.forEach(img => img.parentNode.removeChild(img));

                       // Get the text content without images
                       const textContent = getPlainText(tempDiv.innerHTML, 'md');

                       // Quote the text content
                       const quotedText = textContent
                           .split('\n')
                           .map(line => `> ${line}`)
                           .join('\n');

                       // Combine quoted text with image markdown
                       markdownContent += `${quotedText}\n\n${imageMarkdown}`;

                       if (index < highlights.length - 1) {
                            markdownContent += `---\n\n`;
                       }
                   });

                   // Add footer
                   markdownContent += `\n\n---\n\n*Generated by Stickara on ${new Date().toLocaleDateString()}*`;

                   // Add the markdown content to the file content
                   fileContent += markdownContent;

               } else if (selectedFormat === 'html') {
                   // HTML export with enhanced styling
                   fileExtension = '.html';
                   mimeType = 'text/html;charset=utf-8';
                   let htmlBodyContent = `
<div class="header">
    <h1>Stickara Highlights</h1>
    <div class="export-date">Exported on: ${(() => {
        const exportDate = new Date();
        return !isNaN(exportDate.getTime())
            ? exportDate.toLocaleString()
            : new Date().toISOString().split('T')[0];
    })()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">📄</div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📊</div>
        <div class="meta-content">
            <div class="meta-label">Total Highlights</div>
            <div class="meta-value">${highlights.length}</div>
        </div>
    </div>
</div>`;

                   highlights.forEach((h, index) => {
                       const style = h.style || 'color'; // Default to 'color' style if not specified
                       const color = (style === 'color') ? (h.color || DEFAULT_HIGHLIGHT_COLOR) : null;
                       const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                       // Get style emoji and display text
                       let styleEmoji = '🟨'; // Default yellow highlight
                       let styleText = '';
                       let styleCSS = '';

                       if (style === 'color' && color) {
                           // Color highlight
                           if (color === 'green') styleEmoji = '🟩';
                           else if (color === 'blue') styleEmoji = '🟦';
                           else if (color === 'red') styleEmoji = '🟥';
                           else if (color === 'purple') styleEmoji = '🟪';
                           styleText = `Color: ${colorName}`;
                           styleCSS = `background-color: ${color};`;
                       }
                       else if (style === 'underline') {
                           styleEmoji = '🔽'; // Down arrow for underline
                           styleText = 'Style: Underline';
                           styleCSS = 'text-decoration: underline; text-decoration-color: #2196F3;';
                       }
                       else if (style === 'wavy') {
                           styleEmoji = '〰️'; // Wavy dash
                           styleText = 'Style: Wavy Underline';
                           styleCSS = 'text-decoration: underline wavy; text-decoration-color: #9C27B0;';
                       }
                       else if (style === 'border-thick') {
                           styleEmoji = '🔲'; // Black square button
                           styleText = 'Style: Thick Border';
                           styleCSS = 'border: 2px solid #4CAF50; padding: 2px 4px;';
                       }
                       else if (style === 'strikethrough') {
                           styleEmoji = '❌'; // Cross mark
                           styleText = 'Style: Strikethrough';
                           styleCSS = 'text-decoration: line-through; text-decoration-color: #F44336;';
                       }
                       else {
                           styleEmoji = '📝'; // Memo for unknown styles
                           styleText = `Style: ${style}`;
                           styleCSS = '';
                       }

                       // Process the HTML content to preserve images but escape other HTML
                       let processedHtml = h.text || '[Highlight Text Missing]';

                       // Create a temporary div to extract images
                       const tempDiv = document.createElement('div');
                       tempDiv.innerHTML = processedHtml;

                       // Extract all images
                       const images = Array.from(tempDiv.querySelectorAll('img'));
                       const imageHtml = images.map(img => {
                           // Create a safe image tag with the original src
                           return `<img src="${img.src}" alt="${img.alt || 'Image'}" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 4px;">`;
                       }).join('');

                       // Remove images from the original content
                       images.forEach(img => img.parentNode.removeChild(img));

                       // Get the text content without images and escape it
                       const textContent = tempDiv.innerHTML
                           .replace(/&/g, "&amp;")
                           .replace(/</g, "&lt;")
                           .replace(/>/g, "&gt;")
                           .replace(/"/g, "&quot;")
                           .replace(/'/g, "&#039;")
                           .replace(/\n/g, "<br>"); // Convert newlines to <br>

                       // Combine escaped text with preserved images
                       const escapedText = textContent + (imageHtml ? `<div class="highlight-images">${imageHtml}</div>` : '');

                       // Add timestamp if available
                       const timestampHtml = h.timestamp
                           ? `<div class="highlight-timestamp">Highlighted on: ${new Date(h.timestamp).toLocaleString()}</div>`
                           : '';

                       htmlBodyContent += `
    <div class="highlight-card">
        <div class="highlight-header">
            <div class="highlight-title">${styleEmoji} Highlight ${index + 1}</div>
            <div class="highlight-style">${styleText}</div>
        </div>
        ${timestampHtml}
        <blockquote class="highlight-content" style="${styleCSS} ${style === 'color' ? `border-left: 4px solid ${color};` : ''}">
            ${escapedText}
        </blockquote>
    </div>`;
                   });

                   // Add CSS styling
                   const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        min-height: 100vh;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 3px;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .highlight-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 25px 30px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid var(--border-color);
    }

    .highlight-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .highlight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .highlight-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .highlight-style {
        padding: 6px 12px;
        border-radius: 20px;
        background-color: var(--secondary-color);
        color: white;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .highlight-timestamp {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 20px;
        font-style: italic;
        padding: 8px 12px;
        background-color: #f0f0f0;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .highlight-content {
        background-color: var(--light-bg);
        padding: 1.8rem;
        border-radius: 8px;
        margin: 0;
        line-height: 1.8;
        border-left: 5px solid var(--accent-color);
        border: 1px solid #e8e8e8;
        font-size: 1.05rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .highlight-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
    }

    .highlight-images {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e0e0e0;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .highlight-card {
            margin: 10px;
        }
    }
</style>`;

                   // Add footer
                   const footer = `
<footer>
    <p>Generated by Stickara on ${new Date().toLocaleDateString()}</p>
</footer>`;

                   // Combine all parts
                   fileContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Highlights</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

               } else { // Default to TXT export
                   fileExtension = '.txt';
                   mimeType = 'text/plain;charset=utf-8';

                   // Create a visually appealing separator between highlights
                   const highlightSeparator = '\n' +
                       '+=======================================================================+\n' +
                       '|                                                                       |\n' +
                       '+=======================================================================+\n\n';

                   // Create a decorative header
                   fileContent =
                       '+=======================================================================+\n' +
                       '|                      Stickara HIGHLIGHTS                          |\n' +
                       '+=======================================================================+\n\n' +
                       `Page URL: ${url}\n` +
                       `Exported on: ${new Date().toLocaleString()}\n` +
                       `Total Highlights: ${highlights.length}\n\n` +
                       '-----------------------------------------------------------------------\n\n';

                   highlights.forEach((h, index) => {
                       const color = h.color || DEFAULT_HIGHLIGHT_COLOR; // Use default if missing
                       const colorName = color.charAt(0).toUpperCase() + color.slice(1);

                       // Create a more structured highlight header
                       const highlightHeader =
                           `+----------------------[ HIGHLIGHT ${index + 1} ]------------------------+\n\n` +
                           `COLOR:    ${colorName}\n`;

                       // Add timestamp if available
                       const timestampText = h.timestamp
                           ? `DATE:     ${new Date(h.timestamp).toLocaleString()}\n`
                           : '';

                       const contentHeader =
                           `\nCONTENT:\n` +
                           `-----------------------------------------------------------------------\n`;

                       // Use 'txt' format to properly handle images in plain text
                       fileContent += highlightHeader + timestampText + contentHeader +
                           `${getPlainText(h.text || '[Highlight Text Missing]', 'txt')}\n\n`;

                       // Add separator between highlights (except after the last one)
                       if (index < highlights.length - 1) {
                           fileContent += highlightSeparator;
                       }
                   });

                   // Add footer
                   fileContent += '\n' +
                       '+=======================================================================+\n' +
                       '|                    Generated by Stickara                          |\n' +
                       '+=======================================================================+\n';
               }
               // --- End Format Selection ---


               // --- Download Logic (for non-PDF formats) ---
               try {
                    const blob = new Blob([fileContent], { type: mimeType });
                    const objectUrl = URL.createObjectURL(blob);
                    const filename = `${filenameBase}${fileExtension}`;

                    chrome.downloads.download({ url: objectUrl, filename: filename, saveAs: true }, (id) => {
                        URL.revokeObjectURL(objectUrl); // Clean up
                        if (chrome.runtime.lastError) {
                            showStatus(`Export failed: ${chrome.runtime.lastError.message}`, 'error');
                        } else if (!id) {
                            showStatus('Export may have been cancelled or blocked.', 'info');
                        } else {
                            showStatus('Highlight export started!', 'success');
                        }
                        exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button
                    });
                } catch (error) {
                    console.error("Error creating blob or initiating download:", error);
                    showStatus(`Export failed: ${error.message}`, 'error');
                    exportHighlightsButton.disabled = false; // Re-enable HIGHLIGHT button on error
                }
                // --- End Download Logic ---
           });
       });
   }


  // --- Event Listeners ---

  // Enhanced Clear Button Listener
  if (clearButton) {
      clearButton.addEventListener('click', function () {
          clearButton.disabled = true; // Disable button immediately
          showStatus('Checking current page...', 'info'); // Give feedback

          chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
              if (chrome.runtime.lastError || !tabs || tabs.length === 0 || !tabs[0].id || !tabs[0].url) {
                  console.error("Popup Error querying tabs:", chrome.runtime.lastError?.message || "No active tab");
                  showStatus('Error finding current tab.', 'error');
                  clearButton.disabled = false; // Re-enable on error
                  return;
              }

              const tabId = tabs[0].id;
              const url = tabs[0].url;
              // With the new storage approach, URLs are stored in raw format
              // so we can display them directly without decoding
              let displayUrl = url;
              displayUrl = displayUrl.length > 60 ? displayUrl.substring(0, 57) + '...' : displayUrl; // Truncate long URLs

              // Allow http and https protocols
              if (!url || (!url.startsWith('http:') && !url.startsWith('https:'))) {
                  showStatus('Cannot clear notes on this type of page.', 'error');
                  clearButton.disabled = false; // Re-enable
                  return;
              }

              // --- Generate keys for notes 1 to 10 for checking using raw URL like highlights do ---
              const noteKeysToCheck = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${url}_note${i + 1}`);
              // --- End key generation ---

              chrome.storage.local.get(noteKeysToCheck, function(existingNotesResult) {
                  if (chrome.runtime.lastError) {
                       console.warn("Could not check which notes exist for clear confirmation:", chrome.runtime.lastError.message);
                       const fallbackMsg = `Clear ALL notes and highlights for the current page?\n\nPage: ${displayUrl}\n\nThis will also reset toolbar customization to default settings.\n\nThis cannot be undone.`;
                       if (!confirm(fallbackMsg)) {
                           clearButton.disabled = false; // Re-enable if cancelled
                           showStatus('Clear cancelled.', 'info');
                           return;
                       }
                       proceedWithClearing(tabId, url);
                       return;
                  }

                  const existingIndices = noteKeysToCheck
                      .map((key, index) => existingNotesResult[key] ? index + 1 : null)
                      .filter(index => index !== null);

                  let confirmMsg = `Clear all Stickara data for the current page?\n\nPage: ${displayUrl}\n\n`;
                  if (existingIndices.length > 0) {
                      confirmMsg += `This will permanently delete Note(s) ${existingIndices.join(', ')} and all highlights on this page.`;
                  } else {
                      confirmMsg += `This will permanently delete any highlights on this page (no note content found).`;
                  }
                  confirmMsg += `\n\nThis will also reset toolbar customization to default settings.`;
                  confirmMsg += `\n\nThis action cannot be undone.`;

                  if (!confirm(confirmMsg)) {
                      clearButton.disabled = false; // Re-enable if cancelled
                      showStatus('Clear cancelled.', 'info');
                      return;
                  }

                  proceedWithClearing(tabId, url);
              });
          });
      });
  }

  // Helper function to perform the actual clearing after confirmation
  function proceedWithClearing(tabId, url) {
       showStatus('Clearing page notes & highlights...', 'info');
       // --- Generate keys for notes 1 to 10 for removal using raw URL like highlights do ---
       const noteKeysToRemove = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${url}_note${i + 1}`);
       // --- End key generation ---
       const stateKey = `${STATE_KEY_PREFIX}${url}`;
       const highlightKey = `${HIGHLIGHT_KEY_PREFIX}${url}`;

       const keysToRemove = [...noteKeysToRemove, stateKey, highlightKey];

       chrome.storage.local.remove(keysToRemove, function () { // <-- Uses local
           if (chrome.runtime.lastError) {
               showStatus(`Failed to clear data: ${chrome.runtime.lastError.message}`, 'error');
           } else {
               showStatus('Page data cleared!', 'success');
               // Send message to content script to update its state/UI
               if (tabId) {
                   chrome.tabs.sendMessage(tabId, {
                       action: 'notesCleared'
                   }).catch(e => console.warn("Could not send notesCleared message to content script:", e?.message));
               } else {
                   console.warn("Cannot send notesCleared message, tabId is missing.");
               }

               // Re-load global notes in case the cleared page had a global note
               chrome.storage.local.get(null, (items) => {
                   if (chrome.runtime.lastError) {
                       console.error("Error getting storage after clear:", chrome.runtime.lastError);
                       return;
                   }
                  const allNotes = Object.entries(items)
                      .filter(([key, value]) => key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value !== null)
                      .map(([key, data]) => ({ key, data }));
                  renderGlobalNotes(allNotes);
              });
           }
           if(clearButton) clearButton.disabled = false; // Re-enable button after operation
       });
  }

  // *** MODIFIED *** Export Notes Listener
  if (exportNotesButton && commonExportFormatSelect) {
      exportNotesButton.addEventListener('click', function () {
          exportNotesButton.disabled = true;
          const selectedFormat = commonExportFormatSelect.value; // *** READ FROM COMMON SELECT ***
          showStatus(`Exporting page notes (${selectedFormat.toUpperCase()})...`, 'info');

          chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
               if (chrome.runtime.lastError || !tabs || tabs.length === 0 || !tabs[0].url || !tabs[0].id) {
                   showStatus('Could not get current page details.', 'error');
                   exportNotesButton.disabled = false;
                   return;
               }
               const url = tabs[0].url;
               if (!url || (!url.startsWith('http:') && !url.startsWith('https:'))) {
                   showStatus('Cannot export notes from this type of page.', 'error');
                   exportNotesButton.disabled = false;
                   return;
               }
               // Generate note keys using raw URL like highlights do
               // This ensures URLs are preserved exactly as they appear in the browser
               const noteKeys = Array.from({ length: 10 }, (_, i) => `${STORAGE_KEY_PREFIX}${url}_note${i + 1}`);

               chrome.storage.local.get(noteKeys, async function (results) { // <-- Uses local
                   if (chrome.runtime.lastError) {
                      showStatus(`Error retrieving notes: ${chrome.runtime.lastError.message}`, 'error');
                      exportNotesButton.disabled = false;
                      return;
                  }
                  // Process results based on the `noteKeys` array (which now includes 1-10)
                  const notes = noteKeys.map((k, i) => ({ data: results[k], index: i + 1 }))
                                     .filter(item => item.data && (item.data.text || item.data.tags?.length || item.data.globallyPinned));
                  if (notes.length === 0) {
                      showStatus('No notes found with content or tags to export.', 'info');
                      exportNotesButton.disabled = false;
                      return;
                  }

                  // --- Format Selection Logic ---
                  let fileContent = '';
                  let fileExtension = '.txt';
                  let mimeType = 'text/plain;charset=utf-8';
                  let pageTitle = `Stickara Export: ${url}`;

                  // *** Use selectedFormat read from commonExportFormatSelect ***
                  if (selectedFormat === 'md') {
                      fileExtension = '.md';
                      mimeType = 'text/markdown;charset=utf-8';

                      // Create a more visually appealing Markdown header
                      fileContent = `# 📝 Stickara Notes\n\n`;
                      fileContent += `> *Exported on: ${new Date().toLocaleString()}*\n\n`;
                      fileContent += `**📄 Page URL:** ${url}\n`;
                      fileContent += `**📊 Total Notes:** ${notes.length}\n\n`;
                      fileContent += `---\n\n`;

                      // Process each note
                      fileContent += notes.map((noteItem) => {
                           const n = noteItem.data;
                           const idx = noteItem.index;

                           // Format important note status
                           const globalStatus = n.globallyPinned ? "⭐ **IMPORTANT NOTE**\n\n" : "";

                           // Format metadata
                           const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                           const savedText = savedDate && !isNaN(savedDate.getTime()) ? `*Last Saved:* ${savedDate.toLocaleString()}` : "";

                           const reminderDate = n.reminder ? new Date(n.reminder) : null;
                           const reminderText = reminderDate && !isNaN(reminderDate.getTime()) ? `*Reminder:* ${reminderDate.toLocaleString()}` : "";

                           const tagsText = n.tags?.length ? `*Tags:* ${n.tags.join(', ')}` : "";
                           const metadata = [tagsText, reminderText, savedText].filter(Boolean).join('\n');

                           // Process the note content to preserve images
                           let noteContent = n.text || '';

                           // Create a temporary div to extract images
                           const tempDiv = document.createElement('div');
                           tempDiv.innerHTML = noteContent;

                           // Extract all images
                           const images = Array.from(tempDiv.querySelectorAll('img'));
                           let imageMarkdown = '';

                           // Create Markdown for each image
                           images.forEach((img, idx) => {
                               const alt = img.alt || `Image ${idx + 1}`;
                               const src = img.src || '#';
                               imageMarkdown += `\n\n![${alt}](${src})\n\n`;
                           });

                           // Remove images from the original content
                           images.forEach(img => img.parentNode.removeChild(img));

                           // Convert the remaining HTML to Markdown
                           const textContent = convertToMarkdownPopup(tempDiv.innerHTML);

                           // Combine everything
                           return `## 📌 Note ${idx}\n\n${globalStatus}${textContent}\n\n${imageMarkdown}${metadata ? '\n\n' + metadata + '\n\n' : ''}\n---\n\n`;
                      }).join('');

                  } else if (selectedFormat === 'html') {
                      fileExtension = '.html';
                      mimeType = 'text/html;charset=utf-8';

                      // Create a more visually appealing HTML export
                      let htmlBodyContent = `
<div class="header">
    <h1>Stickara Notes</h1>
    <div class="export-date">Exported on: ${new Date().toLocaleString()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">📄</div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📝</div>
        <div class="meta-content">
            <div class="meta-label">Total Notes</div>
            <div class="meta-value">${notes.length}</div>
        </div>
    </div>
</div>`;

                      notes.forEach((noteItem) => {
                          const n = noteItem.data;
                          const idx = noteItem.index;

                          // Handle important note status
                          const globalStatus = n.globallyPinned
                              ? `<div class="note-global-pin">IMPORTANT NOTE</div>`
                              : "";

                          // Format metadata
                          const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                          const savedText = savedDate && !isNaN(savedDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Last Saved:</span> ${savedDate.toLocaleString()}</div>`
                              : "";

                          const reminderDate = n.reminder ? new Date(n.reminder) : null;
                          const reminderText = reminderDate && !isNaN(reminderDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Reminder:</span> ${reminderDate.toLocaleString()}</div>`
                              : "";

                          const tagsText = n.tags?.length
                              ? `<div class="meta-item"><span class="meta-label">Tags:</span> ${n.tags.join(', ')}</div>`
                              : "";

                          const metadata = `<div class="note-metadata">${[tagsText, reminderText, savedText].filter(Boolean).join('')}</div>`;

                          // Process the note content to ensure images are preserved
                          let noteContent = n.text || '';

                          // Create a note card with the content
                          htmlBodyContent += `
<div class="note-card">
    <div class="note-header">
        <div class="note-title">Note ${idx}</div>
        ${globalStatus}
    </div>
    <div class="note-content">
        ${noteContent}
    </div>
    ${metadata}
</div>`;
                      });

                      // Add CSS styling
                      const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-right: 5px;
        font-weight: bold;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .note-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        margin: 20px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .note-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 10px;
    }

    .note-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .note-global-pin {
        background-color: #dc3545;
        color: white;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .note-content {
        background-color: var(--light-bg);
        padding: 1.2rem;
        border-radius: 6px;
        margin-bottom: 15px;
        line-height: 1.7;
        border-left: 4px solid var(--accent-color);
    }

    .note-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: block;
        page-break-inside: avoid;
    }

    .note-content .Stickara-video-screenshot {
        border: 2px solid #007bff;
        border-radius: 8px;
        margin: 20px 0;
        max-width: 100%;
        height: auto;
        display: block;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
    }

    .note-metadata {
        font-size: 0.85rem;
        color: #666;
        padding: 10px;
        background-color: var(--light-bg);
        border-radius: 4px;
    }

    .note-metadata .meta-item {
        margin-bottom: 5px;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .note-card {
            margin: 10px;
        }
    }
</style>`;

                      // Add footer
                      const footer = `
<footer>
    <p>Generated by Stickara on ${(() => {
        const footerDate = new Date();
        return !isNaN(footerDate.getTime())
            ? footerDate.toLocaleDateString()
            : new Date().toISOString().split('T')[0];
    })()}</p>
</footer>`;

                      // Combine all parts
                      fileContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

                  } else if (selectedFormat === 'pdf') {
                      fileExtension = '.pdf';
                      mimeType = 'application/pdf';

                      // Check if required libraries are available
                      if (typeof window.jspdf === 'undefined') {
                            showStatus('PDF library not loaded.', 'error');
                            console.error("jsPDF library is required for PDF export but not loaded.");
                            exportNotesButton.disabled = false;
                            return;
                      }

                      if (typeof html2canvas === 'undefined') {
                          showStatus('HTML2Canvas library not loaded.', 'error');
                          console.error("html2canvas library is required for styled PDF export but not loaded.");
                          exportNotesButton.disabled = false;
                          return;
                      }

                      // Generate the same beautiful HTML content as HTML export
                      let htmlBodyContent = `
<div class="header">
    <h1>Stickara Notes</h1>
    <div class="export-date">Exported on: ${new Date().toLocaleString()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">📄</div>
        <div class="meta-content">
            <div class="meta-label">Page URL</div>
            <div class="meta-value"><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📝</div>
        <div class="meta-content">
            <div class="meta-label">Total Notes</div>
            <div class="meta-value">${notes.length}</div>
        </div>
    </div>
</div>`;

                      // Add notes to the HTML content
                      notes.forEach((noteItem) => {
                          const n = noteItem.data;
                          const idx = noteItem.index;

                          // Handle important note status
                          const globalStatus = n.globallyPinned
                              ? `<div class="note-global-pin">IMPORTANT NOTE</div>`
                              : "";

                          // Format metadata
                          const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                          const savedText = savedDate && !isNaN(savedDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Last Saved:</span> ${savedDate.toLocaleString()}</div>`
                              : "";

                          const reminderDate = n.reminder ? new Date(n.reminder) : null;
                          const reminderText = reminderDate && !isNaN(reminderDate.getTime())
                              ? `<div class="meta-item"><span class="meta-label">Reminder:</span> ${reminderDate.toLocaleString()}</div>`
                              : "";

                          const tagsText = n.tags?.length
                              ? `<div class="meta-item"><span class="meta-label">Tags:</span> ${n.tags.join(', ')}</div>`
                              : "";

                          const metadata = `<div class="note-metadata">${[tagsText, reminderText, savedText].filter(Boolean).join('')}</div>`;

                          // Process the note content to ensure images are preserved
                          let noteContent = n.text || '';

                          // Create a note card with the content
                          htmlBodyContent += `
<div class="note-card">
    <div class="note-header">
        <div class="note-title">Note ${idx}</div>
        ${globalStatus}
    </div>
    <div class="note-content">
        ${noteContent}
    </div>
    ${metadata}
</div>`;
                      });

                      // Add CSS styling (same as HTML export)
                      const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 0;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-right: 5px;
        font-weight: bold;
    }

    .meta-value {
        font-weight: 500;
    }

    .meta-value a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .meta-value a:hover {
        text-decoration: underline;
    }

    .note-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        margin: 20px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .note-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 10px;
    }

    .note-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .note-global-pin {
        background-color: #dc3545;
        color: white;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .note-content {
        background-color: var(--light-bg);
        padding: 1.2rem;
        border-radius: 6px;
        margin-bottom: 15px;
        line-height: 1.7;
        border-left: 4px solid var(--accent-color);
    }

    .note-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: block;
        page-break-inside: avoid;
    }

    .note-content .Stickara-video-screenshot {
        border: 2px solid #007bff;
        border-radius: 8px;
        margin: 20px 0;
        max-width: 100%;
        height: auto;
        display: block;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        page-break-inside: avoid;
    }

    .note-metadata {
        font-size: 0.85rem;
        color: #666;
        padding: 10px;
        background-color: var(--light-bg);
        border-radius: 4px;
    }

    .note-metadata .meta-item {
        margin-bottom: 5px;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .note-card {
            margin: 10px;
        }
    }
</style>`;

                      // Add footer
                      const footer = `
<footer>
    <p>Generated by Stickara on ${(() => {
        const footerDate = new Date();
        return !isNaN(footerDate.getTime())
            ? footerDate.toLocaleDateString()
            : new Date().toISOString().split('T')[0];
    })()}</p>
</footer>`;

                      // Combine all parts into complete HTML document
                      const completeHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Notes</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

                      // Generate filename
                      let filename = `Stickara_notes_${new Date().toISOString().split('T')[0]}.pdf`;
                      try {
                          const urlObj = new URL(url);
                          const hostname = urlObj.hostname.replace(/^www\./, '');
                          const safeHostname = hostname.replace(/[^a-z0-9\.]/gi, '_').substring(0, 50);
                          const indicesString = notes.map(n => n.index).join('_');
                          filename = `Stickara_${safeHostname}_notes_${indicesString}.pdf`;
                      } catch (e) {
                          console.warn("Error creating filename from URL:", e);
                      }

                      // Convert styled HTML to PDF
                      try {
                          await convertStyledHtmlToPdf(completeHtmlContent, filename, 'Generating beautiful PDF...');
                          exportNotesButton.disabled = false; // Re-enable NOTE export button
                      } catch (pdfError) {
                          console.error("Error generating styled PDF:", pdfError);
                          showStatus('PDF generation failed.', 'error');
                          exportNotesButton.disabled = false; // Re-enable NOTE export button on error
                      }

                      // Return early since PDF handles its own download disabling/enabling
                      return;

                  } else { // Default to Plain Text
                      fileExtension = '.txt';
                      mimeType = 'text/plain;charset=utf-8';

                      // Create a visually appealing separator between notes
                      const noteSeparator = '\n' +
                          '+=======================================================================+\n' +
                          '|                                                                       |\n' +
                          '+=======================================================================+\n\n';

                      // Create a decorative header
                      fileContent =
                          '+=======================================================================+\n' +
                          '|                        Stickara NOTES                             |\n' +
                          '+=======================================================================+\n\n' +
                          `Page URL: ${url}\n` +
                          `Exported on: ${new Date().toLocaleString()}\n` +
                          `Total Notes: ${notes.length}\n\n` +
                          '-----------------------------------------------------------------------\n\n';

                      notes.forEach((noteItem, index) => {
                          const n = noteItem.data;
                          const idx = noteItem.index;

                          // Create a more structured note header
                          const noteHeader =
                              `+----------------------[ NOTE ${idx} ]---------------------------+\n\n`;

                          // Format important note status
                          const globalStatus = n.globallyPinned ? "IMPORTANT NOTE: Yes\n" : "";

                          // Format metadata
                          const savedDate = n.lastSaved ? new Date(n.lastSaved) : null;
                          const savedText = savedDate && !isNaN(savedDate.getTime()) ?
                              `Last Saved: ${savedDate.toLocaleString()}` : "Last Saved: N/A";

                          const reminderDate = n.reminder ? new Date(n.reminder) : null;
                          const reminderText = reminderDate && !isNaN(reminderDate.getTime()) ?
                              `Reminder: ${reminderDate.toLocaleString()}` : "Reminder: None";

                          const tagsText = n.tags?.length ?
                              `Tags: ${n.tags.join(', ')}` : "Tags: None";

                          // Combine metadata
                          const metadataHeader =
                              `METADATA:\n-----------------------------------------------------------------------\n`;

                          const metadataContent = [globalStatus, tagsText, reminderText, savedText].filter(Boolean).join('\n');

                          // Process the note content to handle images
                          let noteContent = n.text || '';

                          // Create a temporary div to extract images
                          const tempDiv = document.createElement('div');
                          tempDiv.innerHTML = noteContent;

                          // Extract all images
                          const images = Array.from(tempDiv.querySelectorAll('img'));
                          let imageText = '';

                          // Create text representation for each image
                          if (images.length > 0) {
                              imageText = `\nIMAGES (${images.length}):\n-----------------------------------------------------------------------\n`;
                              images.forEach((img, imgIdx) => {
                                  const alt = img.alt || `Image ${imgIdx + 1}`;
                                  const src = img.src || '#';
                                  imageText += `[IMAGE ${imgIdx + 1}: ${alt}] Source: ${src.substring(0, 50)}...\n`;
                              });
                              imageText += '\n';
                          }

                          // Get plain text content
                          const plainText = getPlainText(noteContent, 'txt');

                          // Content header
                          const contentHeader =
                              `CONTENT:\n-----------------------------------------------------------------------\n`;

                          // Combine all parts
                          fileContent += noteHeader +
                              metadataHeader + metadataContent + '\n\n' +
                              contentHeader + plainText + '\n\n' +
                              imageText;

                          // Add separator between notes (except after the last one)
                          if (index < notes.length - 1) {
                              fileContent += noteSeparator;
                          }
                      });

                      // Add footer
                      fileContent += '\n' +
                          '+=======================================================================+\n' +
                          '|                    Generated by Stickara                          |\n' +
                          '+=======================================================================+\n';
                  }
                  // --- End Format Selection ---

                  // --- Download Logic (for non-PDF formats) ---
                  try {
                      const blob = new Blob([fileContent], { type: mimeType });
                      const objectUrl = URL.createObjectURL(blob);
                      let filename = `Stickara_notes_${new Date().toISOString().split('T')[0]}${fileExtension}`;
                      try {
                          const urlObj = new URL(url);
                          const hostname = urlObj.hostname.replace(/^www\./, '');
                          const safeHostname = hostname.replace(/[^a-z0-9\.]/gi, '_').substring(0, 50);
                          // Include all note indices in filename if multiple exist
                          const indicesString = notes.map(n=>n.index).join('_');
                          filename = `Stickara_${safeHostname}_notes_${indicesString}${fileExtension}`;
                      } catch(e) { console.warn("Error creating filename from URL:", e); }

                      chrome.downloads.download({ url: objectUrl, filename: filename, saveAs: true }, (id) => {
                            URL.revokeObjectURL(objectUrl); // Clean up
                          if (chrome.runtime.lastError) {
                              showStatus(`Export failed: ${chrome.runtime.lastError.message}`, 'error');
                          } else if (!id) {
                              showStatus('Export may have been cancelled or blocked.', 'info');
                          } else {
                              showStatus('Export download started!', 'success');
                          }
                          exportNotesButton.disabled = false; // Re-enable NOTE export button
                       });
                  } catch(error) {
                       console.error("Error creating blob or initiating download:", error);
                       showStatus(`Export failed: ${error.message}`, 'error');
                       exportNotesButton.disabled = false; // Re-enable NOTE export button on error
                  }
                   // --- End Download Logic ---
               });
          });
      });
  }

  // *** MODIFIED *** Export Highlights Listener
  if (exportHighlightsButton && commonExportFormatSelect) {
        // Use the previously defined exportPageHighlights function
        exportHighlightsButton.addEventListener('click', exportPageHighlights);
  } else if (!exportHighlightsButton) {
      console.warn("Export highlights button not found in popup.");
  } else if (!commonExportFormatSelect) {
       console.warn("Common export format select not found in popup.");
  }


  if (searchButton && searchQueryInput) {
      searchButton.addEventListener('click', performSearch);
      searchQueryInput.addEventListener('keypress', function (e) {
          if (e.key === 'Enter') {
              e.preventDefault(); // Prevent form submission if inside a form
              performSearch();
          }
      });
  }

  if (viewDashboardButton) {
      viewDashboardButton.addEventListener('click', function() {
          chrome.tabs.create({ url: chrome.runtime.getURL('dashboard.html') });
          window.close(); // Close the popup after opening the dashboard
      });
  }

  // Google Drive Sync Button Listeners
  if (connectDriveButton && disconnectDriveButton && syncStatusIndicator) {
      connectDriveButton.addEventListener('click', () => {
          showStatus('Requesting Google Drive access...', 'info');
          connectDriveButton.disabled = true;
          disconnectDriveButton.disabled = true;
          chrome.runtime.sendMessage({ action: 'connectGoogleDrive' }, (response) => {
              // Always re-enable buttons after response or error
              connectDriveButton.disabled = false;
              disconnectDriveButton.disabled = false; // It might be visible now or not
              if (chrome.runtime.lastError) {
                   showStatus(`Error: ${chrome.runtime.lastError.message}`, 'error');
                   updateSyncButtonUI(false); // Assume failed connection
                   if(syncStatusIndicator) syncStatusIndicator.className = 'sync-indicator error'; // Base + state
                   if(syncStatusIndicator) syncStatusIndicator.title = `Sync Status: Error - ${chrome.runtime.lastError.message}`;
              } else if (response?.status === 'success') {
                  showStatus('Google Drive connected! Initial sync may take time.', 'success'); // Update status
                  updateSyncButtonUI(true);
                  // Background script should trigger sync automatically on connect
              } else {
                  showStatus(`Connection failed: ${response?.message || 'Unknown error'}`, 'error');
                  updateSyncButtonUI(false);
                  if(syncStatusIndicator) syncStatusIndicator.className = 'sync-indicator error'; // Base + state
                  if(syncStatusIndicator) syncStatusIndicator.title = `Sync Status: Error - ${response?.message || 'Unknown error'}`;
              }
          });
      });

      disconnectDriveButton.addEventListener('click', () => {
          if (!confirm("Disconnect from Google Drive? Notes will no longer sync.")) return;
          showStatus('Disconnecting Google Drive...', 'info');
          connectDriveButton.disabled = true;
          disconnectDriveButton.disabled = true;

          chrome.runtime.sendMessage({ action: 'disconnectGoogleDrive' }, (response) => {
              // Always re-enable relevant button after response or error
              connectDriveButton.disabled = false; // Re-enable connect in case disconnect fails
              disconnectDriveButton.disabled = false; // Re-enable disconnect in case disconnect fails (though UI might hide it)

              if (chrome.runtime.lastError) {
                  showStatus(`Error disconnecting: ${chrome.runtime.lastError.message}`, 'error');
                  updateSyncButtonUI(true); // Assume still connected on error
              } else if (response && response.success) {
                  showStatus('Disconnected from Google Drive.', 'success');
                  updateSyncButtonUI(false); // Update UI to disconnected state
              } else {
                  showStatus('Failed to disconnect from Google Drive.', 'error');
                  if(syncStatusIndicator) syncStatusIndicator.className = 'sync-indicator error'; // Base + state
                  updateSyncButtonUI(true); // Assume still connected if disconnect failed
              }
          });
      });
  }

  // Close buttons for settings panels
  if (closeVoiceSettingsButton) {
    closeVoiceSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('voice-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  if (closeNoteSettingsButton) {
    closeNoteSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('note-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  // Close UI Customization Panel
  const closeUICustomizationButton = document.getElementById('close-ui-customization');
  if (closeUICustomizationButton) {
    closeUICustomizationButton.addEventListener('click', () => {
      const panel = document.getElementById('ui-customization-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }



  const closeScreenshotSettingsButton = document.getElementById('close-screenshot-settings');
  if (closeScreenshotSettingsButton) {
    closeScreenshotSettingsButton.addEventListener('click', () => {
      const panel = document.getElementById('screenshot-settings-panel');
      if (panel) {
        // Add a fade-out animation before hiding
        panel.style.opacity = '0';
        setTimeout(() => {
          panel.style.display = 'none';
          // Reset opacity for next time
          panel.style.opacity = '1';
        }, 300); // Match the CSS transition duration
      }
    });
  }

  // ===================================================
  // == Voice Settings Initialization and Handling =====
  // ===================================================
  // NOTE: initVoiceSettings function has been moved outside DOMContentLoaded
  // to make it globally accessible. See function definition after DOMContentLoaded.













  // Listener for Background Updates (Sync Status, Connection Status)
   chrome.runtime.onMessage.addListener((message) => {
       if (message.action === 'updateSyncStatus') {
           if (syncStatusIndicator) {
               let statusClass = 'connected'; // Default green
               let statusText = 'Connected'; // Default text

               switch(message.status) {
                   case 'syncing':
                       statusClass = 'syncing';
                       statusText = 'Syncing';
                       break;
                   case 'error':
                       statusClass = 'error';
                       statusText = 'Sync Error';
                       break;
                   case 'disconnected':
                       statusClass = 'disconnected';
                       statusText = 'Disconnected';
                       break;
                   case 'connected': // Explicitly handle 'connected' state from background
                   default:
                        statusClass = 'connected';
                        statusText = 'Connected';
                        break;
               }
               syncStatusIndicator.className = `sync-indicator ${statusClass}`; // Update class
               if (message.details) statusText += `: ${message.details}`;
               else if (statusClass !== 'disconnected' && statusClass !== 'error') {
                   statusText += ` (${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })})`; // Add timestamp if not disconnected/error
               }
               syncStatusIndicator.title = `Sync Status: ${statusText}`;
           }
       } else if (message.action === 'updateConnectionStatus') {
           updateSyncButtonUI(message.isConnected);
       }
       // No async response needed from popup for these messages
       return false;
   });

  // --- Initialize Search Interface ---
  if (searchResultsDiv && searchQueryInput) {
    searchResultsDiv.innerHTML = '<p class="no-results">Enter text or use operators (tag:, url:, date:, color:, reminder:true/false) to search.</p>';
    searchQueryInput.focus(); // Focus search input on popup open
  } else {
    console.error("Stickara Popup: Search initialization failed - missing elements:", {
      searchResultsDiv: !!searchResultsDiv,
      searchQueryInput: !!searchQueryInput
    });
  }

  // Load global notes on popup open
  chrome.storage.local.get(null, (items) => {
       if (chrome.runtime.lastError) {
          console.error("Error getting storage for global notes:", chrome.runtime.lastError);
          if (globalNotesResultsDiv) globalNotesResultsDiv.innerHTML = '<p class="no-results">Error loading global notes.</p>';
          showStatus('Error loading notes.', 'error');
          return;
       }
       const allNotes = Object.entries(items)
           .filter(([key, value]) => key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value !== null)
           .map(([key, data]) => ({ key, data }));
       renderGlobalNotes(allNotes); // Render global notes
  });

  // Check initial Drive connection state and last known sync status
  chrome.storage.local.get(['driveSyncEnabled', 'lastSyncStatus', 'lastSyncDetails', 'lastSyncTime'], (result) => {
        const isConnected = result.driveSyncEnabled === true;
        updateSyncButtonUI(isConnected);

        if (isConnected && syncStatusIndicator) {
            let initialStatusClass = 'connected';
            let initialStatusText = 'Connected';
            const lastStatus = result.lastSyncStatus;
            const lastDetails = result.lastSyncDetails;
            const lastTime = result.lastSyncTime ? new Date(result.lastSyncTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : null;

            switch(lastStatus) {
                case 'syncing': // Unlikely on load, but possible if popup closed mid-sync
                    initialStatusClass = 'syncing';
                    initialStatusText = 'Syncing...';
                    break;
                case 'error':
                    initialStatusClass = 'error';
                    initialStatusText = 'Sync Error';
                    if (lastDetails) initialStatusText += `: ${lastDetails}`;
                    break;
                case 'success': // Treat success as connected
                case 'connected':
                default:
                    initialStatusClass = 'connected';
                    initialStatusText = 'Connected';
                    if (lastTime) initialStatusText += ` (Last sync: ${lastTime})`;
                    break;
            }
            syncStatusIndicator.className = `sync-indicator ${initialStatusClass}`;
            syncStatusIndicator.title = `Sync Status: ${initialStatusText}`;

        } else if (!isConnected && syncStatusIndicator) {
            syncStatusIndicator.className = 'sync-indicator disconnected';
            syncStatusIndicator.title = 'Sync Status: Disconnected';
        }
    });
});

// ===================================================
// == Note Settings Initialization and Handling =======
// ===================================================
function initNoteSettings(panel) {
  // Store the panel reference
  const noteSettingsPanel = panel;

  // DOM Elements within the note settings panel
  const defaultWidthInput = document.getElementById('default-width');
  const defaultHeightInput = document.getElementById('default-height');
  const defaultTopInput = document.getElementById('default-top');
  const defaultRightInput = document.getElementById('default-right');
  const saveButton = document.getElementById('save-note-settings');
  const resetButton = document.getElementById('reset-note-settings');
  const statusMessage = document.getElementById('note-status-text');


  // Check if essential elements exist before proceeding
  if (!defaultWidthInput || !defaultHeightInput || !saveButton) {
    console.error("Note settings panel elements not found. Cannot initialize.");
    if (noteSettingsPanel) noteSettingsPanel.innerHTML = "<p style='color: red;'>Error loading settings UI.</p>";
    return;
  }

  // Load settings and populate form
  loadNoteSettings();

  // Event listeners
  saveButton.addEventListener('click', saveNoteSettings);
  resetButton.addEventListener('click', resetNoteSettings);



  /**
   * Loads settings from storage and populates the form
   */
  function loadNoteSettings() {
    // Use the settings module to get current settings
    if (window.StickaraSettings && typeof window.StickaraSettings.loadSettings === 'function') {
      window.StickaraSettings.loadSettings().then(settings => {
        // Remove 'px' from size values and convert to numbers
        defaultWidthInput.value = parseInt(settings.defaultNoteSize.width);
        defaultHeightInput.value = parseInt(settings.defaultNoteSize.height);
        defaultTopInput.value = parseInt(settings.defaultNotePosition.top);
        defaultRightInput.value = parseInt(settings.defaultNotePosition.right);
      });
    } else {
      // Fallback to direct storage access if settings module is not available
      chrome.storage.local.get(['Stickara_settings'], (result) => {
        const settings = result.Stickara_settings || {
          defaultNoteSize: { width: '340px', height: '380px' },
          defaultNotePosition: { top: '100px', right: '24px' }
        };

        // Remove 'px' from size values and convert to numbers
        defaultWidthInput.value = parseInt(settings.defaultNoteSize.width);
        defaultHeightInput.value = parseInt(settings.defaultNoteSize.height);
        defaultTopInput.value = parseInt(settings.defaultNotePosition.top);
        defaultRightInput.value = parseInt(settings.defaultNotePosition.right);
      });
    }
  }

  /**
   * Saves the form settings to storage
   */
  function saveNoteSettings() {
    // Get values from form
    const defaultWidth = defaultWidthInput.value + 'px';
    const defaultHeight = defaultHeightInput.value + 'px';
    const defaultTop = defaultTopInput.value + 'px';
    const defaultRight = defaultRightInput.value + 'px';

    // Create settings object
    const settings = {
      defaultNoteSize: {
        width: defaultWidth,
        height: defaultHeight
      },
      defaultNotePosition: {
        top: defaultTop,
        right: defaultRight
      },
      version: '1.0'
    };



    // Save to storage
    chrome.storage.local.set({ 'Stickara_settings': settings }, () => {
      if (chrome.runtime.lastError) {
        showNoteSettingsStatus('Error saving settings: ' + chrome.runtime.lastError.message, 'error');
      } else {
        showNoteSettingsStatus('Settings saved successfully!', 'success');
      }
    });
  }

  /**
   * Resets settings to defaults
   */
  function resetNoteSettings() {
    if (confirm('Reset all settings to default values?')) {
      const defaultSettings = {
        defaultNoteSize: {
          width: '340px',
          height: '380px'
        },
        defaultNotePosition: {
          top: '100px',
          right: '24px'
        },
        version: '1.0'
      };

      // Save defaults to storage
      chrome.storage.local.set({ 'Stickara_settings': defaultSettings }, () => {
        if (chrome.runtime.lastError) {
          showNoteSettingsStatus('Error resetting settings: ' + chrome.runtime.lastError.message, 'error');
        } else {
          // Reload form with default values
          defaultWidthInput.value = parseInt(defaultSettings.defaultNoteSize.width);
          defaultHeightInput.value = parseInt(defaultSettings.defaultNoteSize.height);
          defaultTopInput.value = parseInt(defaultSettings.defaultNotePosition.top);
          defaultRightInput.value = parseInt(defaultSettings.defaultNotePosition.right);

          showNoteSettingsStatus('Settings reset to defaults', 'success');
        }
      });
    }
  }

  /**
   * Shows a status message in the note settings panel
   * @param {string} message - The message to display
   * @param {string} type - The type of message ('success', 'error', 'info')
   */
  function showNoteSettingsStatus(message, type = 'info') {
    if (!statusMessage) return;

    statusMessage.textContent = message;
    statusMessage.parentElement.className = 'status-message ' + type;
    statusMessage.parentElement.style.display = 'block';

    // Hide after 3 seconds
    setTimeout(() => {
      statusMessage.parentElement.style.display = 'none';
    }, 3000);
  }
}







// Add event listeners for settings dropdown
document.addEventListener('DOMContentLoaded', function() {
  // Get settings dropdown elements
  const mainSettingsButton = document.getElementById('main-settings-button');
  const settingsDropdownMenu = document.getElementById('settings-dropdown-menu');

  // Get settings options
  const voiceSettingsOption = document.getElementById('voice-settings-option');
  const noteSettingsOption = document.getElementById('note-settings-option');

  const screenshotSettingsOption = document.getElementById('screenshot-settings-option');

  // Get settings panels
  const voiceSettingsPanel = document.getElementById('voice-settings-panel');
  const noteSettingsPanel = document.getElementById('note-settings-panel');

  const screenshotSettingsPanel = document.getElementById('screenshot-settings-panel');

  // Enhanced Settings Dropdown Toggle with visual feedback
  if (mainSettingsButton && settingsDropdownMenu) {
    mainSettingsButton.addEventListener('click', (event) => {
      event.stopPropagation(); // Prevent click from bubbling to document

      // Toggle dropdown visibility
      const isShowing = settingsDropdownMenu.classList.contains('show');
      settingsDropdownMenu.classList.toggle('show');

      // Toggle active state on button for visual feedback
      if (isShowing) {
        mainSettingsButton.classList.remove('active');
      } else {
        mainSettingsButton.classList.add('active');
      }
    });

    // Enhanced close dropdown when clicking outside
    document.addEventListener('click', (event) => {
      if (!mainSettingsButton.contains(event.target) && !settingsDropdownMenu.contains(event.target)) {
        settingsDropdownMenu.classList.remove('show');
        mainSettingsButton.classList.remove('active'); // Remove active state
      }
    });

    // Close dropdown when pressing Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && settingsDropdownMenu.classList.contains('show')) {
        settingsDropdownMenu.classList.remove('show');
        mainSettingsButton.classList.remove('active');
        mainSettingsButton.focus(); // Return focus to button
      }
    });
  }

  // Voice Settings Option
  if (voiceSettingsOption && voiceSettingsPanel) {
    voiceSettingsOption.addEventListener('click', () => {
      console.log('Popup: Voice settings panel opened');

      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide other panels
      if (noteSettingsPanel) noteSettingsPanel.style.display = 'none';
      if (screenshotSettingsPanel) screenshotSettingsPanel.style.display = 'none';
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) uiCustomizationPanel.style.display = 'none';

      // Show voice settings panel
      voiceSettingsPanel.style.display = 'flex';

      // Check if enhanced voice functions are available
      console.log('Popup: Enhanced voice functions available:', {
        getDefaultLanguagesForProvider: typeof window.getDefaultLanguagesForProvider,
        getLanguageDisplayName: typeof window.getLanguageDisplayName
      });

      // Call initVoiceSettings if it exists
      if (typeof initVoiceSettings === 'function') {
        console.log('Popup: Calling initVoiceSettings');
        initVoiceSettings(voiceSettingsPanel); // Pass the panel reference
      } else {
        console.error('Popup: initVoiceSettings function not found');
      }
    });
  }

  // Enhanced Note Settings Option
  if (noteSettingsOption && noteSettingsPanel) {
    noteSettingsOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide other panels
      if (voiceSettingsPanel) voiceSettingsPanel.style.display = 'none';
      if (screenshotSettingsPanel) screenshotSettingsPanel.style.display = 'none';
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) uiCustomizationPanel.style.display = 'none';

      // Show note settings panel
      noteSettingsPanel.style.display = 'flex';

      // Call initNoteSettings if it exists
      if (typeof initNoteSettings === 'function') {
        initNoteSettings(noteSettingsPanel); // Pass the panel reference
      }
    });
  }

  // UI Customization Settings Option
  const uiCustomizationOption = document.getElementById('ui-customization-option');
  const uiCustomizationPanel = document.getElementById('ui-customization-panel');

  if (uiCustomizationOption && uiCustomizationPanel) {
    uiCustomizationOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide other panels
      if (voiceSettingsPanel) voiceSettingsPanel.style.display = 'none';
      if (noteSettingsPanel) noteSettingsPanel.style.display = 'none';
      if (screenshotSettingsPanel) screenshotSettingsPanel.style.display = 'none';

      // Show UI customization panel
      uiCustomizationPanel.style.display = 'flex';

      // Initialize UI customization if it exists
      if (typeof initUICustomization === 'function') {
        initUICustomization(uiCustomizationPanel);
      }
    });
  }



  // Enhanced Screenshot Settings Option
  if (screenshotSettingsOption) {
    screenshotSettingsOption.addEventListener('click', () => {
      // Hide dropdown and remove active state
      settingsDropdownMenu.classList.remove('show');
      mainSettingsButton.classList.remove('active');

      // Hide all panels
      if (voiceSettingsPanel) voiceSettingsPanel.style.display = 'none';
      if (noteSettingsPanel) noteSettingsPanel.style.display = 'none';
      const uiCustomizationPanel = document.getElementById('ui-customization-panel');
      if (uiCustomizationPanel) uiCustomizationPanel.style.display = 'none';

      // Show screenshot settings panel
      if (screenshotSettingsPanel) {
        // Set display to flex to enable the flex-direction: column
        screenshotSettingsPanel.style.display = 'flex';

        // Panel is positioned via CSS

        // Initialize screenshot settings if the function exists
        if (typeof initScreenshotSettings === 'function') {
          initScreenshotSettings();
        }
      }
    });
  }


});

console.log("Stickara: Popup Script Loaded (v1.9 - Settings Dropdown Support)"); // Updated version

// Initialize voice settings function
function initVoiceSettings(panel) {
  // Store the panel reference
  const voiceSettingsPanel = panel;

  // DOM Elements within the voice settings panel
  const providerSelect = document.getElementById('voice-provider'); // Assuming ID inside panel
  const apiKeyContainer = document.getElementById('api-key-container'); // Assuming ID inside panel
  const apiKeyInput = document.getElementById('voice-api-key'); // Assuming ID inside panel
  const togglePasswordButton = document.getElementById('toggle-password'); // Assuming ID inside panel
  const languageSelect = document.getElementById('voice-language'); // Assuming ID inside panel
  const silenceThresholdInput = document.getElementById('voice-silence-threshold'); // Assuming ID inside panel
  const silenceValue = document.getElementById('silence-value'); // Assuming ID inside panel
  const saveButton = document.getElementById('save-voice-settings'); // Assuming ID inside panel
  const resetButton = document.getElementById('reset-settings'); // Assuming ID inside panel
  const providerInfo = document.getElementById('provider-info'); // Assuming ID inside panel

  // Check if essential elements exist before proceeding
  if (!providerSelect || !apiKeyContainer || !apiKeyInput || !languageSelect || !saveButton) {
      console.error("Voice settings panel elements not found. Cannot initialize.");
      // Optionally hide the panel or show an error within it
      if (voiceSettingsPanel) voiceSettingsPanel.innerHTML = "<p style='color: red;'>Error loading settings UI.</p>";
      return;
  }

  // Provider language maps - Comprehensive language support
  const providerLanguages = {
    // Browser API - Common languages that work well with browser API
    browser: [
      'en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU',
      'zh-CN', 'ja-JP', 'ko-KR', 'nl-NL', 'pl-PL', 'sv-SE', 'tr-TR', 'ar-SA',
      'cs-CZ', 'da-DK', 'fi-FI', 'hi-IN', 'hu-HU', 'id-ID', 'nb-NO', 'pt-PT',
      'th-TH', 'vi-VN', 'el-GR', 'he-IL', 'ro-RO', 'sk-SK', 'uk-UA'
    ].sort(), // Sort alphabetically

    // Google Speech API - Comprehensive language support
    google: [
      // English variants
      'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-SG', 'en-ZA',
      // Spanish variants
      'es', 'es-ES', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN',
      'es-MX', 'es-NI', 'es-PA', 'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US', 'es-UY', 'es-VE',
      // French variants
      'fr', 'fr-FR', 'fr-BE', 'fr-CA', 'fr-CH',
      // German variants
      'de', 'de-DE', 'de-AT', 'de-CH',
      // Italian variants
      'it', 'it-IT', 'it-CH',
      // Portuguese variants
      'pt', 'pt-BR', 'pt-PT',
      // Russian
      'ru', 'ru-RU',
      // Chinese variants
      'zh', 'zh-CN', 'zh-HK', 'zh-TW',
      // Japanese
      'ja', 'ja-JP',
      // Korean
      'ko', 'ko-KR',
      // Dutch variants
      'nl', 'nl-NL', 'nl-BE',
      // Polish
      'pl', 'pl-PL',
      // Swedish
      'sv', 'sv-SE',
      // Turkish
      'tr', 'tr-TR',
      // Arabic variants
      'ar', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB',
      'ar-MA', 'ar-OM', 'ar-QA', 'ar-SA', 'ar-TN',
      // Other European languages
      'cs', 'cs-CZ', 'da', 'da-DK', 'fi', 'fi-FI', 'el', 'el-GR', 'hu', 'hu-HU',
      'no', 'nb-NO', 'ro', 'ro-RO', 'sk', 'sk-SK', 'sl', 'sl-SI', 'bg', 'bg-BG',
      'hr', 'hr-HR', 'lt', 'lt-LT', 'lv', 'lv-LV', 'sr', 'sr-RS', // Added base codes
      // Asian languages
      'hi', 'hi-IN', 'id', 'id-ID', 'ms', 'ms-MY', 'fil', 'fil-PH', 'vi', 'vi-VN',
      'th', 'th-TH', 'bn', 'bn-IN', 'ta', 'ta-IN', 'te', 'te-IN', 'ml', 'ml-IN', 'kn', 'kn-IN', // Added base codes
      'mr', 'mr-IN', 'gu', 'gu-IN', 'pa', 'pa-IN', // Added base codes
      // Middle Eastern languages
      'fa', 'fa-IR', 'he', 'he-IL', 'ur', 'ur-PK'
    ].sort(), // Sort alphabetically

    // Azure Speech Service - Most comprehensive language support
    azure: [
      // English variants
      'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-SG', 'en-ZA',
      // Spanish variants
      'es', 'es-ES', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN',
      'es-MX', 'es-NI', 'es-PA', 'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US', 'es-UY', 'es-VE',
      // French variants
      'fr', 'fr-FR', 'fr-BE', 'fr-CA', 'fr-CH',
      // German variants
      'de', 'de-DE', 'de-AT', 'de-CH',
      // Italian variants
      'it', 'it-IT', 'it-CH',
      // Portuguese variants
      'pt', 'pt-BR', 'pt-PT',
      // Russian
      'ru', 'ru-RU',
      // Chinese variants
      'zh', 'zh-CN', 'zh-HK', 'zh-TW',
      // Japanese
      'ja', 'ja-JP',
      // Korean
      'ko', 'ko-KR',
      // Dutch variants
      'nl', 'nl-NL', 'nl-BE',
      // Polish
      'pl', 'pl-PL',
      // Swedish
      'sv', 'sv-SE',
      // Turkish
      'tr', 'tr-TR',
      // Arabic variants
      'ar', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB',
      'ar-MA', 'ar-OM', 'ar-QA', 'ar-SA', 'ar-TN',
      // Other European languages
      'cs', 'cs-CZ', 'da', 'da-DK', 'fi', 'fi-FI', 'el', 'el-GR', 'hu', 'hu-HU',
      'no', 'nb-NO', 'ro', 'ro-RO', 'sk', 'sk-SK', 'sl', 'sl-SI', 'bg', 'bg-BG',
      'hr', 'hr-HR', 'lt', 'lt-LT', 'lv', 'lv-LV', 'sr', 'sr-RS', 'et', 'et-EE', 'gl', 'gl-ES', 'is', 'is-IS', // Added base codes
      // Asian languages
      'hi', 'hi-IN', 'id', 'id-ID', 'ms', 'ms-MY', 'fil', 'fil-PH', 'vi', 'vi-VN',
      'th', 'th-TH', 'bn', 'bn-IN', 'ta', 'ta-IN', 'te', 'te-IN', 'ml', 'ml-IN', 'kn', 'kn-IN', // Added base codes
      'mr', 'mr-IN', 'gu', 'gu-IN', 'pa', 'pa-IN', 'jv', 'jv-ID', // Added base codes
      // Middle Eastern languages
      'fa', 'fa-IR', 'he', 'he-IL', 'ur', 'ur-PK',
      // Other languages
      'uk', 'uk-UA', 'af', 'af-ZA', 'sq', 'sq-AL', 'hy', 'hy-AM', 'az', 'az-AZ', 'eu', 'eu-ES', // Added base codes
      'be', 'be-BY', 'bs', 'bs-BA', 'ca', 'ca-ES', 'cy', 'cy-GB', 'kk', 'kk-KZ', 'ky', 'ky-KG', 'lb', 'lb-LU', 'mk', 'mk-MK', // Added base codes
      'mt', 'mt-MT', 'mn', 'mn-MN' // Added base codes
    ].sort(), // Sort alphabetically

    // AssemblyAI - Focused on high-quality transcription for major languages
    assembly: [
      // English variants
      'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ',
      // Spanish variants
      'es', 'es-ES', 'es-MX', 'es-AR',
      // French variants
      'fr', 'fr-FR', 'fr-CA',
      // German variants
      'de', 'de-DE', 'de-AT', 'de-CH',
      // Italian variants
      'it', 'it-IT',
      // Portuguese variants
      'pt', 'pt-BR', 'pt-PT',
      // Dutch variants
      'nl', 'nl-NL', 'nl-BE',
      // Other major languages
      'hi', 'hi-IN', 'ja', 'ja-JP', 'zh', 'zh-CN', 'zh-TW', 'fi', 'fi-FI',
      'ko', 'ko-KR', 'pl', 'pl-PL', 'ru', 'ru-RU', 'tr', 'tr-TR', 'uk', 'uk-UA',
      'vi', 'vi-VN', 'ar', 'ar-SA', 'ar-EG', 'cs', 'cs-CZ', 'da', 'da-DK',
      'el', 'el-GR', 'he', 'he-IL', 'hu', 'hu-HU', 'id', 'id-ID', 'no', 'nb-NO',
      'ro', 'ro-RO', 'sv', 'sv-SE', 'th', 'th-TH', 'bg', 'bg-BG', 'hr', 'hr-HR',
      'sk', 'sk-SK', 'sl', 'sl-SI', 'sr', 'sr-RS', 'fa', 'fa-IR', 'ms', 'ms-MY',
      'fil', 'fil-PH', 'ta', 'ta-IN', 'bn', 'bn-IN', 'ur', 'ur-PK'
    ].sort() // Sort alphabetically
  };

  // Language display names - Comprehensive list
  const languageDisplayNames = {
    // English variants
    'en': 'English', 'en-US': 'English (US)', 'en-GB': 'English (UK)', 'en-AU': 'English (Australia)', 'en-CA': 'English (Canada)', 'en-IN': 'English (India)',
    'en-IE': 'English (Ireland)', 'en-NZ': 'English (New Zealand)', 'en-PH': 'English (Philippines)', 'en-SG': 'English (Singapore)', 'en-ZA': 'English (South Africa)',
    // Spanish variants
    'es': 'Spanish', 'es-ES': 'Spanish (Spain)', 'es-MX': 'Spanish (Mexico)', 'es-AR': 'Spanish (Argentina)', 'es-BO': 'Spanish (Bolivia)', 'es-CL': 'Spanish (Chile)',
    'es-CO': 'Spanish (Colombia)', 'es-CR': 'Spanish (Costa Rica)', 'es-DO': 'Spanish (Dominican Republic)', 'es-EC': 'Spanish (Ecuador)', 'es-GT': 'Spanish (Guatemala)',
    'es-HN': 'Spanish (Honduras)', 'es-NI': 'Spanish (Nicaragua)', 'es-PA': 'Spanish (Panama)', 'es-PE': 'Spanish (Peru)', 'es-PR': 'Spanish (Puerto Rico)',
    'es-PY': 'Spanish (Paraguay)', 'es-SV': 'Spanish (El Salvador)', 'es-US': 'Spanish (United States)', 'es-UY': 'Spanish (Uruguay)', 'es-VE': 'Spanish (Venezuela)',
    // French variants
    'fr': 'French', 'fr-FR': 'French (France)', 'fr-CA': 'French (Canada)', 'fr-BE': 'French (Belgium)', 'fr-CH': 'French (Switzerland)',
    // German variants
    'de': 'German', 'de-DE': 'German (Germany)', 'de-AT': 'German (Austria)', 'de-CH': 'German (Switzerland)',
    // Italian variants
    'it': 'Italian', 'it-IT': 'Italian (Italy)', 'it-CH': 'Italian (Switzerland)',
    // Portuguese variants
    'pt': 'Portuguese', 'pt-BR': 'Portuguese (Brazil)', 'pt-PT': 'Portuguese (Portugal)',
    // Russian
    'ru': 'Russian', 'ru-RU': 'Russian (Russia)',
    // Chinese variants
    'zh': 'Chinese', 'zh-CN': 'Chinese (Simplified)', 'zh-HK': 'Chinese (Hong Kong)', 'zh-TW': 'Chinese (Traditional)',
    // Japanese
    'ja': 'Japanese', 'ja-JP': 'Japanese (Japan)',
    // Korean
    'ko': 'Korean', 'ko-KR': 'Korean (South Korea)',
    // Dutch variants
    'nl': 'Dutch', 'nl-NL': 'Dutch (Netherlands)', 'nl-BE': 'Dutch (Belgium)',
    // Polish
    'pl': 'Polish', 'pl-PL': 'Polish (Poland)',
    // Swedish
    'sv': 'Swedish', 'sv-SE': 'Swedish (Sweden)',
    // Turkish
    'tr': 'Turkish', 'tr-TR': 'Turkish (Turkey)',
    // Arabic variants
    'ar': 'Arabic', 'ar-SA': 'Arabic (Saudi Arabia)', 'ar-EG': 'Arabic (Egypt)', 'ar-AE': 'Arabic (UAE)', 'ar-BH': 'Arabic (Bahrain)',
    'ar-DZ': 'Arabic (Algeria)', 'ar-IQ': 'Arabic (Iraq)', 'ar-JO': 'Arabic (Jordan)', 'ar-KW': 'Arabic (Kuwait)', 'ar-LB': 'Arabic (Lebanon)',
    'ar-MA': 'Arabic (Morocco)', 'ar-OM': 'Arabic (Oman)', 'ar-QA': 'Arabic (Qatar)', 'ar-TN': 'Arabic (Tunisia)',
    // Hindi
    'hi': 'Hindi', 'hi-IN': 'Hindi (India)',
    // Other European languages
    'cs': 'Czech', 'cs-CZ': 'Czech (Czech Republic)', 'da': 'Danish', 'da-DK': 'Danish (Denmark)', 'fi': 'Finnish', 'fi-FI': 'Finnish (Finland)',
    'el': 'Greek', 'el-GR': 'Greek (Greece)', 'hu': 'Hungarian', 'hu-HU': 'Hungarian (Hungary)', 'no': 'Norwegian', 'nb-NO': 'Norwegian (Norway)',
    'ro': 'Romanian', 'ro-RO': 'Romanian (Romania)', 'sk': 'Slovak', 'sk-SK': 'Slovak (Slovakia)', 'sl': 'Slovenian', 'sl-SI': 'Slovenian (Slovenia)',
    'bg': 'Bulgarian', 'bg-BG': 'Bulgarian (Bulgaria)', 'hr': 'Croatian', 'hr-HR': 'Croatian (Croatia)', 'lt': 'Lithuanian', 'lt-LT': 'Lithuanian (Lithuania)',
    'lv': 'Latvian', 'lv-LV': 'Latvian (Latvia)', 'sr': 'Serbian', 'sr-RS': 'Serbian (Serbia)', 'et': 'Estonian', 'et-EE': 'Estonian (Estonia)',
    'gl': 'Galician', 'gl-ES': 'Galician (Spain)', 'is': 'Icelandic', 'is-IS': 'Icelandic (Iceland)',
    // Asian languages
    'id': 'Indonesian', 'id-ID': 'Indonesian (Indonesia)', 'ms': 'Malay', 'ms-MY': 'Malay (Malaysia)', 'fil': 'Filipino', 'fil-PH': 'Filipino (Philippines)',
    'vi': 'Vietnamese', 'vi-VN': 'Vietnamese (Vietnam)', 'th': 'Thai', 'th-TH': 'Thai (Thailand)', 'bn': 'Bengali', 'bn-IN': 'Bengali (India)',
    'ta': 'Tamil', 'ta-IN': 'Tamil (India)', 'te': 'Telugu', 'te-IN': 'Telugu (India)', 'ml': 'Malayalam', 'ml-IN': 'Malayalam (India)',
    'kn': 'Kannada', 'kn-IN': 'Kannada (India)', 'mr': 'Marathi', 'mr-IN': 'Marathi (India)', 'gu': 'Gujarati', 'gu-IN': 'Gujarati (India)',
    'pa': 'Punjabi', 'pa-IN': 'Punjabi (India)', 'jv': 'Javanese', 'jv-ID': 'Javanese (Indonesia)',
    // Middle Eastern languages
    'fa': 'Persian', 'fa-IR': 'Persian (Iran)', 'he': 'Hebrew', 'he-IL': 'Hebrew (Israel)', 'ur': 'Urdu', 'ur-PK': 'Urdu (Pakistan)',
    // Other languages
    'uk': 'Ukrainian', 'uk-UA': 'Ukrainian (Ukraine)', 'af': 'Afrikaans', 'af-ZA': 'Afrikaans (South Africa)', 'sq': 'Albanian', 'sq-AL': 'Albanian (Albania)',
    'hy': 'Armenian', 'hy-AM': 'Armenian (Armenia)', 'az': 'Azerbaijani', 'az-AZ': 'Azerbaijani (Azerbaijan)', 'eu': 'Basque', 'eu-ES': 'Basque (Spain)',
    'be': 'Belarusian', 'be-BY': 'Belarusian (Belarus)', 'bs': 'Bosnian', 'bs-BA': 'Bosnian (Bosnia and Herzegovina)', 'ca': 'Catalan', 'ca-ES': 'Catalan (Spain)',
    'cy': 'Welsh', 'cy-GB': 'Welsh (United Kingdom)', 'kk': 'Kazakh', 'kk-KZ': 'Kazakh (Kazakhstan)', 'ky': 'Kyrgyz', 'ky-KG': 'Kyrgyz (Kyrgyzstan)',
    'lb': 'Luxembourgish', 'lb-LU': 'Luxembourgish (Luxembourg)', 'mk': 'Macedonian', 'mk-MK': 'Macedonian (North Macedonia)', 'mt': 'Maltese', 'mt-MT': 'Maltese (Malta)',
    'mn': 'Mongolian', 'mn-MN': 'Mongolian (Mongolia)',
    // Simplified auto-generation fallback
    'auto': (code) => {
        try {
            if (code.includes('-')) {
                const [langPart, regionPart] = code.split('-');
                const langName = (new Intl.DisplayNames(['en'], { type: 'language' })).of(langPart) || langPart;
                const regionName = (new Intl.DisplayNames(['en'], { type: 'region' })).of(regionPart.toUpperCase()) || regionPart;
                return `${langName} (${regionName})`;
            } else {
                return (new Intl.DisplayNames(['en'], { type: 'language' })).of(code) || code;
            }
        } catch (e) {
            // Fallback for environments without Intl.DisplayNames or invalid codes
            return code;
        }
    }
  };

  // Function to get display name, using map first, then auto-generation
  function getDisplayName(code) {
      if (languageDisplayNames[code] && typeof languageDisplayNames[code] === 'string') {
          return languageDisplayNames[code];
      }
      return languageDisplayNames['auto'](code); // Use the auto-generator
  }

  // Provider info text
  const providerInfoText = {
    browser: 'Uses the browser\'s built-in speech recognition. No API key needed. Quality varies by browser.',
    google: 'Uses Google Cloud Speech-to-Text API. Requires an API key for full usage. Offers high accuracy.',
    azure: 'Uses Microsoft Azure Cognitive Services Speech API. Requires an API key and region. Highly accurate and feature-rich.',
    assembly: 'Uses AssemblyAI Speech-to-Text API. Requires an API key. Known for advanced features like summarization.'
  };

  // === Functions defined within initVoiceSettings ===

  function loadVoiceSettings() {
      chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], function(result) {
          const prefs = result.voiceApiPreferences || {}; // Default to empty object
          const keys = result.voiceApiKeys || {}; // Default to empty object

          // Set provider (default to browser if not set)
          const currentProvider = prefs.provider || 'browser';
          providerSelect.value = currentProvider;
          updateProviderUI(currentProvider, keys); // Pass keys to potentially fill API input

           // Set quality (default to standard)
          const qualitySelect = document.getElementById('voice-quality');
           if (qualitySelect) {
               qualitySelect.value = prefs.quality || 'standard';
           }

          // Set language AFTER updating options for the loaded provider
          updateLanguageOptions(currentProvider); // Populate language options first

          // Set the saved language after a small delay to ensure options are populated
          setTimeout(() => {
              if (prefs.language && languageSelect.querySelector(`option[value="${prefs.language}"]`)) {
                  languageSelect.value = prefs.language;
                  console.log(`Popup: Set language to saved value: ${prefs.language}`);
              } else if (languageSelect.options.length > 0) {
                  // If saved language is invalid for the provider, select the first option
                  languageSelect.selectedIndex = 0;
                  console.log(`Popup: Saved language ${prefs.language} not available, using: ${languageSelect.value}`);
              }
          }, 50);

          // Set checkboxes (provide defaults)
          setCheckbox('voice-punctuation', prefs.enablePunctuation !== false); // Default true
          setCheckbox('voice-capitalization', prefs.enableAutomaticCapitalization !== false); // Default true
          setCheckbox('voice-profanity-filter', prefs.enableProfanityFilter === true); // Default false
          setCheckbox('voice-interim-results', prefs.enableInterimResults !== false); // Default true
          setCheckbox('voice-auto-detect', prefs.autoDetectLanguage === true); // Default false
          setCheckbox('voice-background-noise-reduction', prefs.backgroundNoiseReduction === true); // Default false
          setCheckbox('voice-speaker-diarization', prefs.speakerDiarization === true); // Default false

          // Set silence threshold (default 15 seconds)
          if (silenceThresholdInput) {
              const defaultSeconds = 15;
              const savedMillis = prefs.silenceThreshold;
              // Use saved value if it's a valid number, otherwise default
              const seconds = (typeof savedMillis === 'number' && savedMillis >= 1000) ? Math.floor(savedMillis / 1000) : defaultSeconds;
              silenceThresholdInput.value = seconds;
              if (silenceValue) silenceValue.textContent = seconds + ' seconds';
          }
      });
  }

  function updateProviderUI(provider, apiKeys = {}) {
      // Show/hide API key input
      apiKeyContainer.style.display = provider === 'browser' ? 'none' : 'block';

      // Update provider info text
      if (providerInfo) {
          providerInfo.textContent = providerInfoText[provider] || 'Select a provider.';
      }

      // Fill API key input if needed and available
      apiKeyInput.value = ''; // Clear first
      if (provider === 'google' && apiKeys.googleSpeechApiKey) {
          apiKeyInput.value = apiKeys.googleSpeechApiKey;
      } else if (provider === 'azure' && apiKeys.azureSpeechApiKey) {
          apiKeyInput.value = apiKeys.azureSpeechApiKey;
      } else if (provider === 'assembly' && apiKeys.assemblyAiApiKey) {
          apiKeyInput.value = apiKeys.assemblyAiApiKey;
      }

      // Ensure password visibility toggle is correct
      if (togglePasswordButton) {
          apiKeyInput.setAttribute('type', 'password');
          togglePasswordButton.innerHTML = '<span class="popup-icon">👁️</span>'; // Show icon
      }

      // Note: Language options are updated separately by calling updateLanguageOptions
      // Note: Feature availability (checkboxes etc.) is currently assumed to be universal,
      // but could be adjusted here based on the provider if needed in the future.
  }

  function updateLanguageOptions(provider) {
      console.log(`Popup: updateLanguageOptions called with provider: ${provider}`);
      const currentLang = languageSelect.value; // Save current selection

      // Clear existing options
      languageSelect.innerHTML = '';

      // Get languages for the provider (default to browser)
      const languages = providerLanguages[provider] || providerLanguages.browser;
      console.log(`Popup: Found ${languages.length} languages for provider ${provider}`);

      // Add new options sorted by display name
      languages
          .map(code => ({ code: code, name: getDisplayName(code) })) // Create objects with code and display name
          .sort((a, b) => a.name.localeCompare(b.name)) // Sort by display name
          .forEach(lang => {
              const option = document.createElement('option');
              option.value = lang.code;
              option.textContent = lang.name; // Use display name
              languageSelect.appendChild(option);
          });

      // Restore selection if it exists in new options
      if (languageSelect.querySelector(`option[value="${currentLang}"]`)) {
          languageSelect.value = currentLang;
      } else if (languageSelect.options.length > 0) {
          // If previous selection invalid, select the first available option (which is now sorted)
          languageSelect.selectedIndex = 0;
      }

      // Update the language count hint (optional)
      const formGroup = languageSelect.closest('.form-group');
      if (formGroup) {
          formGroup.setAttribute('data-count', `${languages.length} languages`);
      }

      // Show a status message about loaded languages (optional)
      // Use the main popup status bar for simplicity
      const providerName = provider.charAt(0).toUpperCase() + provider.slice(1);
      if (typeof showStatus === 'function') {
          showStatus(`${providerName} provider: ${languages.length} languages loaded.`, 'info');
      }
  }

  // This is the actual implementation, defined inside initVoiceSettings
  function saveVoiceSettings() {
      const provider = providerSelect.value;
      const language = languageSelect.value; // Value should be valid now
      const apiKey = apiKeyInput.value.trim();

      // Prepare preferences object
      const preferences = {
          provider: provider,
          language: language,
          enablePunctuation: getCheckboxValue('voice-punctuation', true),
          enableAutomaticCapitalization: getCheckboxValue('voice-capitalization', true),
          enableProfanityFilter: getCheckboxValue('voice-profanity-filter', false),
          enableInterimResults: getCheckboxValue('voice-interim-results', true),
          silenceThreshold: parseInt(silenceThresholdInput ? silenceThresholdInput.value : 15, 10) * 1000, // Base 10

          // Enhanced options
          autoDetectLanguage: getCheckboxValue('voice-auto-detect', false),
          backgroundNoiseReduction: getCheckboxValue('voice-background-noise-reduction', false),
          speakerDiarization: getCheckboxValue('voice-speaker-diarization', false),
          quality: document.getElementById('voice-quality') ? document.getElementById('voice-quality').value : 'standard'
      };

      // Get existing keys first to preserve other keys
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          let apiKeys = result.voiceApiKeys || {}; // Start with existing or empty

          // Update key for selected provider if provided
          if (provider !== 'browser') {
              // Store the key even if empty, allowing user to clear it
              if (provider === 'google') {
                  apiKeys.googleSpeechApiKey = apiKey;
              } else if (provider === 'azure') {
                  apiKeys.azureSpeechApiKey = apiKey;
              } else if (provider === 'assembly') {
                  apiKeys.assemblyAiApiKey = apiKey;
              }
          }

          // Save to storage
          chrome.storage.local.set({
              voiceApiPreferences: preferences,
              voiceApiKeys: apiKeys
          }, function() {
               if (chrome.runtime.lastError) {
                   console.error("Error saving voice settings:", chrome.runtime.lastError);
                   if (typeof showStatus === 'function') {
                       showStatus(`Error saving: ${chrome.runtime.lastError.message}`, 'error');
                   }
               } else {
                  if (typeof showStatus === 'function') {
                      showStatus('Voice settings saved!', 'success');
                  }
                  console.log('Voice settings saved successfully');
               }
          });
      });
  }

  // Helper functions used by initVoiceSettings
  function setCheckbox(id, checked) {
      const checkbox = document.getElementById(id);
      if (checkbox) checkbox.checked = !!checked; // Ensure boolean
  }

  function getCheckboxValue(id, defaultValue) {
      const checkbox = document.getElementById(id);
      // Ensure defaultValue is returned if element doesn't exist
      return checkbox ? checkbox.checked : defaultValue;
  }

  // === Event Listeners Setup inside initVoiceSettings ===
  providerSelect.addEventListener('change', function() {
      const provider = this.value;
      // Get current keys to pass to updateProviderUI
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          updateProviderUI(provider, result.voiceApiKeys || {});
          updateLanguageOptions(provider); // Update languages when provider changes
      });
  });

  if (silenceThresholdInput) {
      silenceThresholdInput.addEventListener('input', function() {
          if(silenceValue) silenceValue.textContent = this.value + ' seconds';
      });
  }

  if (togglePasswordButton) {
      togglePasswordButton.addEventListener('click', function() {
          const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
          apiKeyInput.setAttribute('type', type);
          // Toggle icon based on type (using simple text fallback if icons fail)
          togglePasswordButton.innerHTML = type === 'password' ?
              '<span class="popup-icon">👁️</span><span class="sr-only">Show API Key</span>' :
              '<span class="popup-icon">🔒</span><span class="sr-only">Hide API Key</span>';
      });
  }

  saveButton.addEventListener('click', saveVoiceSettings);

  if (resetButton) {
      resetButton.addEventListener('click', function() {
          if (confirm('Reset all voice settings to default values? This will clear any entered API keys.')) {
              // Reset logic would go here
              console.log('Voice settings reset requested');
          }
      });
  }

  // === Initial Load Call inside initVoiceSettings ===
  loadVoiceSettings();

} // End of initVoiceSettings function

// Initialize screenshot settings function
function initScreenshotSettings() {
  console.log("Initializing screenshot settings");

  // Get DOM elements
  const storageLocalRadio = document.getElementById('storage-local');
  const storageDriveRadio = document.getElementById('storage-drive');
  const storageBothRadio = document.getElementById('storage-both');
  const driveFolderNameInput = document.getElementById('drive-folder-name');
  const privacyPrivateRadio = document.getElementById('privacy-private');
  const privacyVisibleRadio = document.getElementById('privacy-visible');
  const autoDeleteAfterInput = document.getElementById('auto-delete-after');
  const autoDeleteDisplay = document.getElementById('auto-delete-display');
  const autoDeleteTimeline = document.getElementById('auto-delete-timeline');
  const timelineMarker = document.querySelector('.timeline-marker');
  const askBeforeUploadCheckbox = document.getElementById('ask-before-upload');
  const imageQualitySelect = document.getElementById('image-quality');
  const enableHighDpiCheckbox = document.getElementById('enable-high-dpi');
  const enableAntiAliasingCheckbox = document.getElementById('enable-anti-aliasing');

  const driveSettingsSection = document.getElementById('drive-settings-section');
  const resetButton = document.getElementById('reset-screenshot-settings');
  const saveButton = document.getElementById('save-screenshot-settings');
  const statusMessage = document.getElementById('screenshot-status-message');

  // Check if essential elements exist
  if (!storageLocalRadio || !storageDriveRadio || !storageBothRadio || !saveButton) {
    console.error("Screenshot settings panel elements not found");
    return;
  }

  // Load settings
  loadScreenshotSettings();

  // Add event listeners
  storageLocalRadio.addEventListener('change', toggleDriveSettings);
  storageDriveRadio.addEventListener('change', toggleDriveSettings);
  storageBothRadio.addEventListener('change', toggleDriveSettings);

  autoDeleteAfterInput.addEventListener('input', updateAutoDeleteDisplay);

  resetButton.addEventListener('click', resetScreenshotSettings);
  saveButton.addEventListener('click', saveScreenshotSettings);

  /**
   * Toggles the visibility of Drive settings based on storage location
   */
  function toggleDriveSettings() {
    const showDriveSettings = storageDriveRadio.checked || storageBothRadio.checked;
    driveSettingsSection.style.display = showDriveSettings ? 'block' : 'none';
  }

  /**
   * Updates the auto-delete display and timeline marker
   */
  function updateAutoDeleteDisplay() {
    const days = parseInt(autoDeleteAfterInput.value);

    if (days === 0) {
      autoDeleteDisplay.textContent = 'Never';
      timelineMarker.style.display = 'none';
    } else {
      autoDeleteDisplay.textContent = days === 1 ? '1 day' : `${days} days`;
      timelineMarker.style.display = 'block';

      // Calculate position (0-100%)
      const maxDays = parseInt(autoDeleteAfterInput.max);
      const position = (days / maxDays) * 100;
      timelineMarker.style.left = `${position}%`;
    }
  }

  /**
   * Loads screenshot settings from storage
   */
  function loadScreenshotSettings() {
    chrome.storage.local.get(['screenshotStoragePrefs', 'screenshotQualityPrefs'], (result) => {
      const storagePrefs = result.screenshotStoragePrefs || getDefaultStorageSettings();
      const qualityPrefs = result.screenshotQualityPrefs || getDefaultQualitySettings();

      // Apply storage settings to form
      switch (storagePrefs.storageLocation) {
        case 'local':
          storageLocalRadio.checked = true;
          break;
        case 'drive':
          storageDriveRadio.checked = true;
          break;
        case 'both':
          storageBothRadio.checked = true;
          break;
      }

      driveFolderNameInput.value = storagePrefs.driveFolderName;

      if (storagePrefs.useAppDataFolder) {
        privacyPrivateRadio.checked = true;
      } else {
        privacyVisibleRadio.checked = true;
      }

      autoDeleteAfterInput.value = storagePrefs.autoDeleteAfter;
      askBeforeUploadCheckbox.checked = storagePrefs.askBeforeUpload;

      // Apply quality settings to form
      imageQualitySelect.value = qualityPrefs.quality;
      enableHighDpiCheckbox.checked = qualityPrefs.enableHighDpi;
      enableAntiAliasingCheckbox.checked = qualityPrefs.enableAntiAliasing;


      // Update UI state
      toggleDriveSettings();
      updateAutoDeleteDisplay();
    });
  }

  /**
   * Saves screenshot settings to storage
   */
  function saveScreenshotSettings() {
    // Get storage location
    let storageLocation = 'local';
    if (storageDriveRadio.checked) storageLocation = 'drive';
    if (storageBothRadio.checked) storageLocation = 'both';

    // Create storage preferences object
    const storagePrefs = {
      storageLocation: storageLocation,
      driveFolderName: driveFolderNameInput.value.trim() || 'Stickara Screenshots',
      useAppDataFolder: privacyPrivateRadio.checked,
      autoDeleteAfter: parseInt(autoDeleteAfterInput.value) || 0,
      askBeforeUpload: askBeforeUploadCheckbox.checked
    };

    // Create quality preferences object
    const qualityPrefs = {
      quality: imageQualitySelect.value,
      enableHighDpi: enableHighDpiCheckbox.checked,
      enableAntiAliasing: enableAntiAliasingCheckbox.checked,

    };

    // Save both preferences objects
    chrome.storage.local.set({
      screenshotStoragePrefs: storagePrefs,
      screenshotQualityPrefs: qualityPrefs
    }, () => {
      showStatusMessage(statusMessage, 'Screenshot settings saved successfully!', 'success');
    });
  }

  /**
   * Resets screenshot settings to defaults
   */
  function resetScreenshotSettings() {
    const defaultStorageSettings = getDefaultStorageSettings();
    const defaultQualitySettings = getDefaultQualitySettings();

    // Apply default storage settings to form
    storageLocalRadio.checked = defaultStorageSettings.storageLocation === 'local';
    storageDriveRadio.checked = defaultStorageSettings.storageLocation === 'drive';
    storageBothRadio.checked = defaultStorageSettings.storageLocation === 'both';

    driveFolderNameInput.value = defaultStorageSettings.driveFolderName;

    privacyPrivateRadio.checked = defaultStorageSettings.useAppDataFolder;
    privacyVisibleRadio.checked = !defaultStorageSettings.useAppDataFolder;

    autoDeleteAfterInput.value = defaultStorageSettings.autoDeleteAfter;
    askBeforeUploadCheckbox.checked = defaultStorageSettings.askBeforeUpload;

    // Apply default quality settings to form
    imageQualitySelect.value = defaultQualitySettings.quality;
    enableHighDpiCheckbox.checked = defaultQualitySettings.enableHighDpi;
    enableAntiAliasingCheckbox.checked = defaultQualitySettings.enableAntiAliasing;


    // Update UI state
    toggleDriveSettings();
    updateAutoDeleteDisplay();

    showStatusMessage(statusMessage, 'Settings reset to defaults', 'info');
  }

  /**
   * Returns default storage settings
   */
  function getDefaultStorageSettings() {
    return {
      storageLocation: 'local',
      driveFolderName: 'Stickara Screenshots',
      useAppDataFolder: true,
      autoDeleteAfter: 0,
      askBeforeUpload: true
    };
  }

  /**
   * Returns default quality settings
   */
  function getDefaultQualitySettings() {
    return {
      quality: 'standard',
      enableHighDpi: true,
      enableAntiAliasing: true,

    };
  }

  /**
   * Shows a status message in the screenshot settings panel
   * @param {HTMLElement} element - The status message element
   * @param {string} message - The message to display
   * @param {string} type - The type of message (success, error, info)
   */
  function showStatusMessage(element, message, type) {
    if (!element) return;

    const statusText = element.querySelector('#screenshot-status-text');
    if (!statusText) return;

    statusText.textContent = message;
    element.className = `status-message ${type}`;
    element.style.display = 'block';

    // Hide after 3 seconds
    setTimeout(() => {
      element.style.opacity = '0';
      setTimeout(() => {
        element.style.display = 'none';
        element.style.opacity = '1';
      }, 300);
    }, 3000);
  }
}

// Initialize voice settings function
function initVoiceSettings(panel) {
  console.log('Initializing voice settings...');

  // Store the panel reference
  const voiceSettingsPanel = panel;

  // DOM Elements within the voice settings panel
  const providerSelect = document.getElementById('voice-provider');
  const apiKeyContainer = document.getElementById('api-key-container');
  const apiKeyInput = document.getElementById('voice-api-key');
  const togglePasswordButton = document.getElementById('toggle-password');
  const languageSelect = document.getElementById('voice-language');
  const silenceThresholdInput = document.getElementById('voice-silence-threshold');
  const silenceValue = document.getElementById('silence-value');
  const saveButton = document.getElementById('save-voice-settings');
  const resetButton = document.getElementById('reset-settings');
  const providerInfo = document.getElementById('provider-info');

  // Check if essential elements exist before proceeding
  if (!providerSelect || !apiKeyContainer || !apiKeyInput || !languageSelect || !saveButton) {
      console.error("Voice settings panel elements not found. Cannot initialize.");
      console.error("Missing elements:", {
          providerSelect: !!providerSelect,
          apiKeyContainer: !!apiKeyContainer,
          apiKeyInput: !!apiKeyInput,
          languageSelect: !!languageSelect,
          saveButton: !!saveButton
      });
      if (voiceSettingsPanel) voiceSettingsPanel.innerHTML = "<p style='color: red;'>Error loading settings UI.</p>";
      return;
  }

  console.log('All voice settings elements found successfully');

  // Provider info text
  const providerInfoText = {
    browser: 'Uses the browser\'s built-in speech recognition. No API key needed. Quality varies by browser.',
    google: 'Uses Google Cloud Speech-to-Text API. Requires an API key for full usage. Offers high accuracy.',
    azure: 'Uses Microsoft Azure Cognitive Services Speech API. Requires an API key and region. Highly accurate and feature-rich.',
    assembly: 'Uses AssemblyAI Speech-to-Text API. Requires an API key. Known for advanced features like summarization.'
  };

  // Update provider UI based on selection
  function updateProviderUI(provider, apiKeys = {}) {
      console.log(`Updating provider UI for: ${provider}`);

      // Show/hide API key input
      apiKeyContainer.style.display = provider === 'browser' ? 'none' : 'block';

      // Update provider info text
      if (providerInfo) {
          providerInfo.textContent = providerInfoText[provider] || 'Select a provider to see details.';
      }

      // Fill API key input if needed and available
      apiKeyInput.value = ''; // Clear first
      if (provider === 'google' && apiKeys.googleSpeechApiKey) {
          apiKeyInput.value = apiKeys.googleSpeechApiKey;
      } else if (provider === 'azure' && apiKeys.azureSpeechApiKey) {
          apiKeyInput.value = apiKeys.azureSpeechApiKey;
      } else if (provider === 'assembly' && apiKeys.assemblyAiApiKey) {
          apiKeyInput.value = apiKeys.assemblyAiApiKey;
      }

      // Ensure password visibility toggle is correct
      if (togglePasswordButton) {
          apiKeyInput.setAttribute('type', 'password');
          togglePasswordButton.innerHTML = '<span class="popup-icon">👁️</span>';
      }
  }

  // Update language options for the selected provider
  function updateLanguageOptionsForProvider(provider) {
      console.log(`Updating language options for provider: ${provider}`);

      // Clear existing options first
      languageSelect.innerHTML = '';

      // Call the global updateLanguageOptions function
      updateLanguageOptions(provider);
  }

  // Load voice settings from storage
  function loadVoiceSettings() {
      console.log('Loading voice settings from storage...');

      chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], function(result) {
          const prefs = result.voiceApiPreferences || {};
          const keys = result.voiceApiKeys || {};

          console.log('Loaded preferences:', prefs);
          console.log('Loaded API keys:', Object.keys(keys));

          // Set provider (default to browser if not set)
          const currentProvider = prefs.provider || 'browser';
          providerSelect.value = currentProvider;
          updateProviderUI(currentProvider, keys);

          // Update language options for the current provider
          updateLanguageOptionsForProvider(currentProvider);

          // Set the saved language after a small delay to ensure options are populated
          setTimeout(() => {
              if (prefs.language && languageSelect.querySelector(`option[value="${prefs.language}"]`)) {
                  languageSelect.value = prefs.language;
                  console.log(`Set language to saved value: ${prefs.language}`);
              } else if (languageSelect.options.length > 0) {
                  languageSelect.selectedIndex = 0;
                  console.log(`Saved language ${prefs.language} not available, using: ${languageSelect.value}`);
              }
          }, 100);

          // Set quality (default to standard)
          const qualitySelect = document.getElementById('voice-quality');
          if (qualitySelect) {
              qualitySelect.value = prefs.quality || 'standard';
          }

          // Set checkboxes (provide defaults)
          setCheckbox('voice-punctuation', prefs.enablePunctuation !== false);
          setCheckbox('voice-capitalization', prefs.enableAutomaticCapitalization !== false);
          setCheckbox('voice-profanity-filter', prefs.enableProfanityFilter === true);
          setCheckbox('voice-interim-results', prefs.enableInterimResults !== false);
          setCheckbox('voice-auto-detect', prefs.autoDetectLanguage === true);
          setCheckbox('voice-background-noise-reduction', prefs.backgroundNoiseReduction === true);
          setCheckbox('voice-speaker-diarization', prefs.speakerDiarization === true);

          // Set silence threshold (default 15 seconds)
          if (silenceThresholdInput && silenceValue) {
              const defaultSeconds = 15;
              const savedMillis = prefs.silenceThreshold;
              const seconds = (typeof savedMillis === 'number' && savedMillis >= 1000) ? Math.floor(savedMillis / 1000) : defaultSeconds;
              silenceThresholdInput.value = seconds;
              silenceValue.textContent = seconds + ' seconds';
              console.log(`Set silence threshold to: ${seconds} seconds`);
          }
      });
  }

  // Helper function to set checkbox values
  function setCheckbox(id, checked) {
      const checkbox = document.getElementById(id);
      if (checkbox) checkbox.checked = !!checked;
  }

  // Helper function to get checkbox values
  function getCheckboxValue(id, defaultValue) {
      const checkbox = document.getElementById(id);
      return checkbox ? checkbox.checked : defaultValue;
  }

  // Save voice settings to storage
  function saveVoiceSettings() {
      console.log('Saving voice settings...');

      const provider = providerSelect.value;
      const language = languageSelect.value;
      const apiKey = apiKeyInput.value.trim();

      // Prepare preferences object
      const preferences = {
          provider: provider,
          language: language,
          enablePunctuation: getCheckboxValue('voice-punctuation', true),
          enableAutomaticCapitalization: getCheckboxValue('voice-capitalization', true),
          enableProfanityFilter: getCheckboxValue('voice-profanity-filter', false),
          enableInterimResults: getCheckboxValue('voice-interim-results', true),
          silenceThreshold: parseInt(silenceThresholdInput ? silenceThresholdInput.value : 15, 10) * 1000,
          autoDetectLanguage: getCheckboxValue('voice-auto-detect', false),
          backgroundNoiseReduction: getCheckboxValue('voice-background-noise-reduction', false),
          speakerDiarization: getCheckboxValue('voice-speaker-diarization', false),
          quality: document.getElementById('voice-quality') ? document.getElementById('voice-quality').value : 'standard'
      };

      // Get existing keys first to preserve other keys
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          let apiKeys = result.voiceApiKeys || {};

          // Update key for selected provider if provided
          if (provider !== 'browser') {
              if (provider === 'google') {
                  apiKeys.googleSpeechApiKey = apiKey;
              } else if (provider === 'azure') {
                  apiKeys.azureSpeechApiKey = apiKey;
              } else if (provider === 'assembly') {
                  apiKeys.assemblyAiApiKey = apiKey;
              }
          }

          // Save to storage
          chrome.storage.local.set({
              voiceApiPreferences: preferences,
              voiceApiKeys: apiKeys
          }, function() {
               if (chrome.runtime.lastError) {
                   console.error("Error saving voice settings:", chrome.runtime.lastError);
                   if (typeof showStatus === 'function') {
                       showStatus(`Error saving: ${chrome.runtime.lastError.message}`, 'error');
                   }
               } else {
                  console.log('Voice settings saved successfully');
                  if (typeof showStatus === 'function') {
                      showStatus('Voice settings saved!', 'success');
                  }
               }
          });
      });
  }

  // Set up event listeners
  console.log('Setting up voice settings event listeners...');

  // Provider change event
  providerSelect.addEventListener('change', function() {
      const provider = this.value;
      console.log(`Provider changed to: ${provider}`);

      // Get current keys to pass to updateProviderUI
      chrome.storage.local.get(['voiceApiKeys'], function(result) {
          updateProviderUI(provider, result.voiceApiKeys || {});
          updateLanguageOptionsForProvider(provider);
      });
  });

  // Silence threshold slider event
  if (silenceThresholdInput && silenceValue) {
      silenceThresholdInput.addEventListener('input', function() {
          silenceValue.textContent = this.value + ' seconds';
          console.log(`Silence threshold changed to: ${this.value} seconds`);
      });
  }

  // Password toggle event
  if (togglePasswordButton) {
      togglePasswordButton.addEventListener('click', function() {
          const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
          apiKeyInput.setAttribute('type', type);
          togglePasswordButton.innerHTML = type === 'password' ?
              '<span class="popup-icon">👁️</span>' :
              '<span class="popup-icon">🔒</span>';
      });
  }

  // Save button event
  saveButton.addEventListener('click', saveVoiceSettings);

  // Reset button event (if exists)
  if (resetButton) {
      resetButton.addEventListener('click', function() {
          if (confirm('Reset all voice settings to default values? This will clear any entered API keys.')) {
              console.log('Resetting voice settings to defaults...');
              // Reset logic would go here - for now just reload defaults
              loadVoiceSettings();
          }
      });
  }

  // Load initial settings
  console.log('Loading initial voice settings...');
  loadVoiceSettings();

  console.log('Voice settings initialization completed successfully');
}

/**
 * Initialize UI Customization Panel
 */
function initUICustomization(panel) {

  // Get all the form elements
  const saveButton = document.getElementById('save-ui-customization');
  const resetButton = document.getElementById('reset-ui-customization');
  const statusMessage = document.getElementById('ui-customization-status-text');

  // Check if essential elements exist
  if (!saveButton || !resetButton) {
    console.error("UI Customization panel elements not found. Cannot initialize.");
    if (panel) panel.innerHTML = "<p style='color: red;'>Error loading UI customization.</p>";
    return;
  }

  // Load current settings
  loadUICustomizationSettings();

  // Set up event listeners
  saveButton.addEventListener('click', saveUICustomizationSettings);
  resetButton.addEventListener('click', resetUICustomizationSettings);

  // Set up real-time checkbox listeners
  setupRealTimeUpdates();

  // Set up reorder button handlers
  setupReorderControls();

  // Set up drag and drop functionality
  setupDragAndDrop();

  /**
   * Load UI customization settings from storage
   */
  function loadUICustomizationSettings() {
    chrome.storage.local.get(['Stickara_ui_customization'], (result) => {
      const settings = result.Stickara_ui_customization || getDefaultUICustomizationSettings();

      // Apply settings to checkboxes
      applySettingsToUI(settings);
    });
  }

  /**
   * Get default UI customization settings
   */
  function getDefaultUICustomizationSettings() {
    return {
      toolbarDropdowns: {
        order: ['format-dropdown', 'style-dropdown', 'insert-dropdown', 'tools-dropdown', 'view-dropdown'],
        visibility: {
          'format-dropdown': true,
          'style-dropdown': true,
          'insert-dropdown': true,
          'tools-dropdown': true,
          'view-dropdown': true
        }
      },
      uiElements: {
        visibility: {
          'note-title': true,
          'tags-input': true,
          'notebook-selector': true,
          'reminder-input': true,
          'note-switcher': true,
          'timestamp-display': true
        }
      },
      snippetButtons: {
        order: ['datetime-button', 'pageinfo-button', 'highlight-button', 'timestamp-button'],
        visibility: {
          'datetime-button': true,
          'pageinfo-button': true,
          'highlight-button': true,
          'timestamp-button': true
        }
      }
    };
  }

  /**
   * Apply settings to the UI elements
   */
  function applySettingsToUI(settings) {
    // Apply toolbar dropdown settings
    const toolbarList = document.getElementById('toolbar-dropdowns-list');
    if (toolbarList && settings.toolbarDropdowns) {
      reorderListItems(toolbarList, settings.toolbarDropdowns.order);
      applyVisibilitySettings(settings.toolbarDropdowns.visibility);
    }

    // Apply UI elements settings
    if (settings.uiElements) {
      applyVisibilitySettings(settings.uiElements.visibility);
    }

    // Apply snippet buttons settings
    const snippetList = document.getElementById('snippet-buttons-list');
    if (snippetList && settings.snippetButtons) {
      reorderListItems(snippetList, settings.snippetButtons.order);
      applyVisibilitySettings(settings.snippetButtons.visibility);
    }
  }

  /**
   * Apply visibility settings to checkboxes
   */
  function applyVisibilitySettings(visibilitySettings) {
    Object.entries(visibilitySettings).forEach(([element, isVisible]) => {
      const checkboxId = element + '-toggle';
      const checkbox = document.getElementById(checkboxId);
      if (checkbox) {
        checkbox.checked = isVisible;
      }
    });
  }

  /**
   * Reorder list items based on saved order
   */
  function reorderListItems(container, order) {
    const items = Array.from(container.children);
    const itemMap = new Map();

    // Create a map of data-element to DOM element
    items.forEach(item => {
      const element = item.dataset.element;
      if (element) {
        itemMap.set(element, item);
      }
    });

    // Clear container and re-add items in the specified order
    container.innerHTML = '';
    order.forEach(elementName => {
      const item = itemMap.get(elementName);
      if (item) {
        container.appendChild(item);
      }
    });
  }

  /**
   * Save UI customization settings
   */
  function saveUICustomizationSettings() {
    try {
      const settings = {
        toolbarDropdowns: {
          order: getListOrder('toolbar-dropdowns-list'),
          visibility: getVisibilitySettings('toolbar-dropdowns-list')
        },
        uiElements: {
          visibility: getVisibilitySettings('ui-elements-list')
        },
        snippetButtons: {
          order: getListOrder('snippet-buttons-list'),
          visibility: getVisibilitySettings('snippet-buttons-list')
        }
      };

      // Save to storage
      chrome.storage.local.set({ 'Stickara_ui_customization': settings }, () => {
        if (chrome.runtime.lastError) {
          showUICustomizationStatus('Error saving settings: ' + chrome.runtime.lastError.message, 'error');
        } else {
          showUICustomizationStatus('Settings saved successfully!', 'success');

          // Send message to content scripts to apply changes
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
              chrome.tabs.sendMessage(tabs[0].id, {
                action: 'applyUICustomization',
                settings: settings
              }).catch(() => {
                // Ignore errors if content script is not loaded
              });
            }
          });
        }
      });
    } catch (error) {
      showUICustomizationStatus('Error saving settings: ' + error.message, 'error');
    }
  }

  /**
   * Get the current order of items in a list
   */
  function getListOrder(listId) {
    const list = document.getElementById(listId);
    if (!list) return [];

    return Array.from(list.children).map(item => item.dataset.element).filter(Boolean);
  }

  /**
   * Get visibility settings from checkboxes
   */
  function getVisibilitySettings(listId) {
    const list = document.getElementById(listId);
    if (!list) return {};

    const settings = {};
    Array.from(list.children).forEach(item => {
      const element = item.dataset.element;
      if (element) {
        // All checkboxes follow the pattern: element-toggle
        const checkboxId = element + '-toggle';

        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
          settings[element] = checkbox.checked;
        }
      }
    });

    return settings;
  }

  /**
   * Reset to default settings
   */
  function resetUICustomizationSettings() {
    if (confirm('Reset all UI customization settings to default values?')) {
      const defaultSettings = getDefaultUICustomizationSettings();

      // Apply default settings to UI
      applySettingsToUI(defaultSettings);

      // Save default settings
      chrome.storage.local.set({ 'Stickara_ui_customization': defaultSettings }, () => {
        if (chrome.runtime.lastError) {
          showUICustomizationStatus('Error resetting settings: ' + chrome.runtime.lastError.message, 'error');
        } else {
          showUICustomizationStatus('Settings reset to defaults!', 'success');
        }
      });
    }
  }

  /**
   * Show status message
   */
  function showUICustomizationStatus(message, type = 'info') {
    if (!statusMessage) return;

    const statusContainer = statusMessage.parentElement;
    if (!statusContainer) return;

    statusMessage.textContent = message;
    statusContainer.className = `status-message visible ${type}`;

    // Hide after 3 seconds
    setTimeout(() => {
      statusContainer.classList.remove('visible');
    }, 3000);
  }

  /**
   * Set up reorder button controls
   */
  function setupReorderControls() {
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('reorder-btn')) {
        const direction = e.target.dataset.direction;
        const item = e.target.closest('.customization-item');
        const container = item.parentElement;

        if (direction === 'up') {
          const prevItem = item.previousElementSibling;
          if (prevItem) {
            container.insertBefore(item, prevItem);
          }
        } else if (direction === 'down') {
          const nextItem = item.nextElementSibling;
          if (nextItem) {
            container.insertBefore(nextItem, item);
          }
        }

        // Update button states
        updateReorderButtonStates(container);

        // Apply changes immediately
        const settings = {
          toolbarDropdowns: {
            order: getListOrder('toolbar-dropdowns-list'),
            visibility: getVisibilitySettings('toolbar-dropdowns-list')
          },
          uiElements: {
            visibility: getVisibilitySettings('ui-elements-list')
          },
          snippetButtons: {
            order: getListOrder('snippet-buttons-list'),
            visibility: getVisibilitySettings('snippet-buttons-list')
          }
        };

        // Send message to content scripts to apply changes immediately
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'applyUICustomization',
              settings: settings
            }).catch(() => {
              // Ignore errors if content script is not loaded
            });
          }
        });
      }
    });
  }

  /**
   * Update reorder button states (disable up/down when at edges)
   */
  function updateReorderButtonStates(container) {
    const items = Array.from(container.children);

    items.forEach((item, index) => {
      const upBtn = item.querySelector('.reorder-btn[data-direction="up"]');
      const downBtn = item.querySelector('.reorder-btn[data-direction="down"]');

      if (upBtn) upBtn.disabled = index === 0;
      if (downBtn) downBtn.disabled = index === items.length - 1;
    });
  }

  /**
   * Set up real-time updates for checkboxes
   */
  function setupRealTimeUpdates() {
    // Get all checkboxes in the UI customization panel
    const checkboxes = document.querySelectorAll('#ui-customization-panel .ui-checkbox');

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        // Apply changes immediately without saving
        const settings = {
          toolbarDropdowns: {
            order: getListOrder('toolbar-dropdowns-list'),
            visibility: getVisibilitySettings('toolbar-dropdowns-list')
          },
          uiElements: {
            visibility: getVisibilitySettings('ui-elements-list')
          },
          snippetButtons: {
            order: getListOrder('snippet-buttons-list'),
            visibility: getVisibilitySettings('snippet-buttons-list')
          }
        };

        // Send message to content scripts to apply changes immediately
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'applyUICustomization',
              settings: settings
            }).catch(() => {
              // Ignore errors if content script is not loaded
            });
          }
        });
      });
    });
  }

  /**
   * Set up drag and drop functionality
   */
  function setupDragAndDrop() {
    // Get all draggable items
    const draggableItems = document.querySelectorAll('#ui-customization-panel .customization-item');

    draggableItems.forEach(item => {
      const dragHandle = item.querySelector('.drag-handle');
      if (dragHandle) {
        // Make the item draggable when drag handle is used
        dragHandle.addEventListener('mousedown', (e) => {
          e.preventDefault();
          startDrag(item, e);
        });

        // Add visual feedback
        dragHandle.style.cursor = 'grab';
        dragHandle.addEventListener('mousedown', () => {
          dragHandle.style.cursor = 'grabbing';
        });

        document.addEventListener('mouseup', () => {
          dragHandle.style.cursor = 'grab';
        });
      }
    });
  }

  /**
   * Start drag operation
   */
  function startDrag(draggedItem, startEvent) {
    const container = draggedItem.parentElement;
    const items = Array.from(container.children);
    const draggedIndex = items.indexOf(draggedItem);

    // Create visual feedback
    draggedItem.classList.add('dragging');
    draggedItem.style.opacity = '0.5';
    draggedItem.style.transform = 'scale(0.95)';

    let placeholder = null;

    function onMouseMove(e) {
      e.preventDefault();

      // Find the item we're hovering over
      const afterElement = getDragAfterElement(container, e.clientY);

      if (afterElement == null) {
        container.appendChild(draggedItem);
      } else {
        container.insertBefore(draggedItem, afterElement);
      }
    }

    function onMouseUp(e) {
      e.preventDefault();

      // Remove visual feedback
      draggedItem.classList.remove('dragging');
      draggedItem.style.opacity = '';
      draggedItem.style.transform = '';

      // Clean up event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);

      // Check if order changed
      const newItems = Array.from(container.children);
      const newIndex = newItems.indexOf(draggedItem);

      if (newIndex !== draggedIndex) {
        // Update button states
        updateReorderButtonStates(container);

        // Apply changes immediately
        const settings = {
          toolbarDropdowns: {
            order: getListOrder('toolbar-dropdowns-list'),
            visibility: getVisibilitySettings('toolbar-dropdowns-list')
          },
          uiElements: {
            visibility: getVisibilitySettings('ui-elements-list')
          },
          snippetButtons: {
            order: getListOrder('snippet-buttons-list'),
            visibility: getVisibilitySettings('snippet-buttons-list')
          }
        };

        // Send message to content scripts to apply changes immediately
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'applyUICustomization',
              settings: settings
            }).catch(() => {
              // Ignore errors if content script is not loaded
            });
          }
        });
      }
    }

    // Add event listeners
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  /**
   * Get the element that should come after the dragged element
   */
  function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.customization-item:not(.dragging)')];

    return draggableElements.reduce((closest, child) => {
      const box = child.getBoundingClientRect();
      const offset = y - box.top - box.height / 2;

      if (offset < 0 && offset > closest.offset) {
        return { offset: offset, element: child };
      } else {
        return closest;
      }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
  }

  // Initial setup
  updateReorderButtonStates(document.getElementById('toolbar-dropdowns-list'));
  updateReorderButtonStates(document.getElementById('snippet-buttons-list'));
}

// --- END OF FILE popup.js ---
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Permissions Explanation</title>
    <link rel="stylesheet" href="permissions-explanation.css">
</head>
<body>
    <h1>Stickara Permissions Explanation</h1>
    
    <p>This page explains why <PERSON><PERSON> needs each permission it requests. We're committed to transparency and only request permissions that are necessary for the features you use.</p>
    
    <h2>Core Permissions</h2>
    
    <div class="permission">
        <h3><span class="permission-icon">💾</span> storage & unlimitedStorage <span class="badge required">Required</span></h3>
        <p>These permissions allow <PERSON><PERSON> to save your notes, highlights, and settings to your browser's local storage.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Without these permissions, your notes and highlights would be lost when you close your browser.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">📄</span> activeTab <span class="badge required">Required</span></h3>
        <p>This permission allows <PERSON><PERSON> to interact with the current tab when you explicitly invoke it (e.g., by clicking the extension icon or using a keyboard shortcut).</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is required to create notes and highlights on the current webpage you're viewing.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">🔍</span> tabs <span class="badge required">Required</span></h3>
        <p>This permission allows Stickara to access tab information like URLs and titles.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is required to associate notes and highlights with specific webpages and to display page information in your notes.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">⏰</span> alarms <span class="badge required">Required</span></h3>
        <p>This permission allows Stickara to set timers and schedule actions.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is used for reminder functionality and periodic sync operations.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">🔔</span> notifications <span class="badge required">Required</span></h3>
        <p>This permission allows Stickara to show desktop notifications.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is used for reminder alerts and sync status notifications.</p>
        </div>
    </div>
    
    <h2>Feature-Specific Permissions</h2>
    
    <div class="permission">
        <h3><span class="permission-icon">⬇️</span> downloads <span class="badge optional-badge">Optional</span></h3>
        <p>This permission allows Stickara to download files to your computer.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is used when you export notes, highlights, or screenshots to your computer.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">🖼️</span> offscreen <span class="badge optional-badge">Optional</span></h3>
        <p>This permission allows Stickara to create invisible documents for processing tasks.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is used for PDF generation, image processing, and secure API operations.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">📋</span> contextMenus <span class="badge optional-badge">Optional</span></h3>
        <p>This permission allows Stickara to add items to the right-click context menu.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This enables you to quickly create notes or highlights from selected text using the right-click menu.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">🔑</span> identity <span class="badge optional-badge">Optional</span></h3>
        <p>This permission allows Stickara to use OAuth2 for authentication with Google services.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is required for Google Drive sync and Google Calendar integration. Stickara only accesses the specific Google services you authorize.</p>
        </div>
    </div>
    
    <h2>Host Permissions</h2>
    
    <div class="permission">
        <h3><span class="permission-icon">🌐</span> *://*.googleapis.com/* <span class="badge optional-badge">Optional</span></h3>
        <p>This permission allows Stickara to connect to Google APIs.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is required for Google Drive sync and Google Calendar integration.</p>
        </div>
    </div>
    
    <div class="permission">
        <h3><span class="permission-icon">🔐</span> *://*.google.com/oauth2/* <span class="badge optional-badge">Optional</span></h3>
        <p>This permission allows Stickara to authenticate with Google services.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> This is required for the OAuth2 authentication flow with Google services.</p>
        </div>
    </div>
    
    <h2>Data Usage</h2>
    
    <p>Stickara is designed with privacy in mind:</p>
    <ul>
        <li>All your notes and highlights are stored locally on your device by default.</li>
        <li>Google Drive sync is completely optional and only enabled if you explicitly connect your account.</li>
        <li>API keys for speech recognition services are encrypted before storage.</li>
        <li>We do not collect or transmit your data to our servers.</li>
    </ul>
    
    <p>For more detailed information about data handling, please see our <a href="privacy-policy.html">Privacy Policy</a>.</p>
    
    <p class="last-updated">Last updated: June 2023</p>
</body>
</html>

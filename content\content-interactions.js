// --- START OF FILE content-interactions.js ---

// Assume necessary variables (noteContainer, noteText, notes, currentNoteIndex, saveTimeout, resizeObserver,
// lastTimestampedVideoSrc, NOTE_ID, TEXTAREA_ID, TAGS_ID, REMIN<PERSON>R_ID, HEADER_ID, HEADER_CONTROLS_ID, TITLE_ID, NOTE_TITLE_INPUT_ID,
// TIMESTAMP_ID, NOTE_SWITCHER_ID, IMAGE_INPUT_ID, FLASHCARD_MODAL_ID, DEFAULT_FONT_SIZE, TIMESTAMP_LINK_CLASS, isDragging,
// dragStartX, dragStartY, initialLeft, initialTop, noteHeader, isRecording, inNoteSearchActive, noteTitleInput, DEFAULT_HIGHLIGHT_COLOR) // Added noteTitleInput, DEFAULT_HIGHLIGHT_COLOR
// and functions (scheduleSave, createDefaultNoteData, loadCurrentNote, showNote, hideNote,
// applyFormatting, changeTextSize, toggleDropdown, closeAllDropdowns, update*, saveState, copyNoteText,
// insertHtmlAtCursor, insertNodeAtCursor, formatSecondsToMMSS, showCopyFeedback, saveCurrentNote,
// openEquationEditor, handleFormattingState, changeColor, changeOpacity, handleImageUpload,
// updateNoteSwitcher, handlePotentialActionClick, stopRecording?, closeFlashcardModal?, hideInNoteSearchBar?,
// handleAddPriceSnippet, handleAddProSnippet, handleAddConSnippet, handleAddFeatureSnippet,
// handleConvertSelectionToChecklist, togglePin, toggleGlobalPin, toggleMinimize,
// insertEquation, openEquationEditor, calculateTextAreaHeight, updatePinButtonState, updateGlobalPinButtonState,
// updateMinimizeButtonState, handleFindShortcut, showInNoteSearchBar, hideInNoteSearchBar, navigateInNoteHighlight,
// handleInNoteSearchInput, handleInNoteSearchKeydown, clearInNoteHighlights, katex?, getPlainText?,
// openScreenshotAnnotationModal? // Added openScreenshotAnnotationModal dependency
// etc.)
// are defined elsewhere (in state.js, utils.js, storage.js, ui.js, voice.js, flashcards.js, search.js, katex.min.js)
// or imported/available in the scope.

// --- State for Resizing ---
let isResizing = false;
let resizeStartX, resizeStartY;
let initialWidth, initialHeight, initialRectLeft, initialRectTop;
let resizeDirection;
// ------------------------

/**
 * Initializes the ResizeObserver to watch the note container for size changes.
 */
function initResizeObserver() {
    if (!noteContainer) {
        console.warn("Stickara: Cannot init ResizeObserver, noteContainer not found.");
        return;
    }
    if (resizeObserver) { // Avoid creating multiple observers
        resizeObserver.disconnect();
    }

    try {
        resizeObserver = new ResizeObserver(entries => {
            // Use requestAnimationFrame to avoid layout thrashing and ensure calculations happen after render
            window.requestAnimationFrame(() => {
                if (!entries || !entries.length) return;
                if (noteContainer && noteContainer.classList.contains('visible')) {
                    calculateTextAreaHeight(); // Recalculate on resize (defined in ui.js)
                    scheduleSave(); // Save new size after resize stops (debounced via scheduleSave)
                }
            });
        });
        resizeObserver.observe(noteContainer);
        noteContainer.setAttribute('data-observed', 'true'); // Mark as observed
    } catch (e) {
        console.error("Stickara: Failed to create or observe with ResizeObserver:", e);
        resizeObserver = null; // Ensure observer is nullified on error
    }
}


// --- Dragging Handlers ---

/**
 * Starts the dragging operation for the note container.
 * **MODIFIED: Prevents drag on title input.**
 * @param {MouseEvent} e - The mousedown event.
 */
function startDrag(e) {
    // Prevent drag on interactive elements within the header
    // <<< ADD NOTE_TITLE_INPUT_ID check >>>
    if (e.target.closest(`#${HEADER_CONTROLS_ID} button, #${HEADER_CONTROLS_ID} select, #${NOTE_TITLE_INPUT_ID}, #${TIMESTAMP_ID}, #${NOTE_SWITCHER_ID}`)) {
        return;
    }
    // Also prevent drag if clicking on the text area, tags, or reminder inputs through event bubbling
    if (e.target.closest(`#${TEXTAREA_ID}, #${TAGS_ID}, #${REMINDER_ID}`)) {
         return;
     }

    isDragging = true; // Assumes isDragging is defined in state.js
    dragStartX = e.clientX; // Assumes dragStartX defined in state.js
    dragStartY = e.clientY; // Assumes dragStartY defined in state.js
    const rect = noteContainer.getBoundingClientRect();
    const isPinned = noteContainer.classList.contains('pinned');

    // Store initial position correctly based on pinned state
    initialLeft = isPinned ? rect.left : (rect.left + window.scrollX); // Assumes initialLeft defined in state.js
    initialTop = isPinned ? rect.top : (rect.top + window.scrollY);     // Assumes initialTop defined in state.js

    // Change cursor to grabbing hand during drag
    noteContainer.style.cursor = 'grabbing';
    document.body.style.cursor = 'grabbing'; // Change cursor for entire page during drag

    // Add a class to the body to ensure the cursor is applied globally
    document.body.classList.add('Stickara-dragging');

    // Also set the cursor on the header specifically
    if (noteHeader) {
        noteHeader.style.cursor = 'grabbing';
        noteHeader.style.userSelect = 'none'; // Prevent text selection in header during drag
    }

    document.body.style.userSelect = 'none'; // Prevent text selection on page during drag
    e.preventDefault(); // Prevent default text selection behavior
}

/**
 * Handles the note container dragging movement.
 * @param {MouseEvent} e - The mousemove event.
 */
function drag(e) {
    if (!isDragging || !noteContainer) return;
    e.preventDefault(); // Prevent page scrolling or other default actions

    // Ensure cursor remains as grabbing during the drag
    document.body.style.cursor = 'grabbing';

    // Make sure the dragging class is still applied
    if (!document.body.classList.contains('Stickara-dragging')) {
        document.body.classList.add('Stickara-dragging');
    }

    const dx = e.clientX - dragStartX;
    const dy = e.clientY - dragStartY;
    let newTop = initialTop + dy;
    let newLeft = initialLeft + dx;

    const isPinned = noteContainer.classList.contains('pinned');
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const scrollX = isPinned ? 0 : window.scrollX;
    const scrollY = isPinned ? 0 : window.scrollY;
    const containerHeight = noteContainer.offsetHeight;
    const containerWidth = noteContainer.offsetWidth;

    // Clamp position within viewport boundaries (relative to document or viewport)
    const minTop = scrollY;
    const maxTop = scrollY + viewportHeight - containerHeight;
    const minLeft = scrollX;
    const maxLeft = scrollX + viewportWidth - containerWidth;

    newTop = Math.max(minTop, Math.min(newTop, maxTop));
    newLeft = Math.max(minLeft, Math.min(newLeft, maxLeft));

    // Apply style relative to the viewport (subtract scroll offset if not pinned)
    noteContainer.style.top = (newTop - (isPinned ? 0 : scrollY)) + 'px';
    noteContainer.style.left = (newLeft - (isPinned ? 0 : scrollX)) + 'px';
    noteContainer.style.right = 'auto'; // Ensure left/top positioning takes precedence
    noteContainer.style.bottom = 'auto';
}


/**
 * Stops the dragging operation.
 */
function stopDrag() {
    if (!isDragging || !noteContainer) return;
    isDragging = false;

    // Restore cursors
    noteContainer.style.cursor = 'auto'; // Restore default cursor
    document.body.style.cursor = 'auto'; // Restore default cursor for body

    // Remove the dragging class from the body
    document.body.classList.remove('Stickara-dragging');

    // Restore header cursor to grab (not auto)
    if (noteHeader) {
        noteHeader.style.cursor = 'grab';
        noteHeader.style.userSelect = ''; // Restore text selection
    }

    document.body.style.userSelect = '';
    scheduleSave(); // Save the final position
}

// --- Visibility and Interaction Handlers ---

/**
 * Saves the current note scope preference (global vs URL-specific) to localStorage.
 * @param {number} noteIndex - The note index to save preference for
 * @param {boolean} isGlobal - Whether the note is in global mode
 */
function saveNoteScopePreference(noteIndex, isGlobal) {
    try {
        localStorage.setItem(`Stickara_prefer_global_${noteIndex}`, isGlobal.toString());
    } catch (error) {
        console.error(`Stickara: Error saving note scope preference for note ${noteIndex}:`, error);
    }
}

/**
 * Applies the user's last selected note scope preference (global vs URL-specific)
 * for the current note when showing the note interface.
 */
function applyLastUsedNoteScope() {
    if (!noteContainer || !currentNoteIndex) {
        return;
    }

    // Get the stored preference for the current note
    const preferGlobal = localStorage.getItem(`Stickara_prefer_global_${currentNoteIndex}`) === 'true';

    // Check if we need to apply the preference
    const isCurrentlyGlobal = noteContainer.classList.contains('global-note');

    // Only apply the preference if it differs from the current state
    if (preferGlobal && !isCurrentlyGlobal) {
        // User prefers global notes, switch to global mode
        showGlobalNote();
    } else if (!preferGlobal && isCurrentlyGlobal) {
        // User prefers URL-specific notes, switch to URL-specific mode
        showUrlNote();
    }
}



/**
 * Toggles the visibility of the note container. Creates UI if needed.
 */
function toggleNoteVisibility() {
    // Ensure UI elements exist, create if necessary
    if (!document.getElementById(NOTE_ID)) createNoteUI(); // Defined in ui.js
    if (!document.getElementById(FLASHCARD_MODAL_ID)) createFlashcardModal(); // Defined in ui.js

    if (!noteContainer) { // Re-check after potential creation
        console.error("Stickara: Failed to find or create noteContainer. Toggle failed.");
        return;
    }

    // Toggle visibility
    if (noteContainer.classList.contains('visible')) {
        hideNote();
    } else {
        showNote();
    }
}

/**
 * Makes the note container visible and focuses the text area.
 */
function showNote() {
    if (!noteContainer) return;

    noteContainer.classList.add('visible');
    noteContainer.setAttribute('aria-hidden', 'false');
    // Defer focus slightly to ensure element is fully visible and interactable
    requestAnimationFrame(() => {
        // Check if we're using iframe-based note UI (YouTube)
        const iframe = noteContainer.querySelector('#Stickara-note-iframe');
        if (iframe && iframe.contentWindow) {
            // For iframe-based notes, send focus command to iframe
            iframe.contentWindow.postMessage({
                type: 'stickara-command',
                command: 'focus'
            }, '*');
        } else if (noteText) {
            // For standard notes, focus directly
            noteText.focus({ preventScroll: true }); // Prevent page scroll on focus
        }

        calculateTextAreaHeight(); // Ensure height is correct when shown (ui.js)
        saveState(); // Save visibility state immediately (storage.js)

        // Check for Pro/Con snippets when the note is shown
        if (typeof checkForProConSnippets === 'function') {
            setTimeout(checkForProConSnippets, 300); // Small delay to ensure the DOM is updated
        }
    });
}

/**
 * Hides the note container.
 */
function hideNote() {
    if (!noteContainer) return;
    if (noteContainer.classList.contains('visible')) {
        noteContainer.classList.remove('visible');
        noteContainer.setAttribute('aria-hidden', 'true');
        saveState(); // Save visibility state immediately
    }
    closeAllDropdowns(); // Close any open dropdowns
    if (typeof isRecording !== 'undefined' && isRecording) stopRecording(); // Stop recording if active (defined in voice.js)
    if (typeof closeFlashcardModal !== 'undefined') closeFlashcardModal(); // Close study mode if open (defined in flashcards.js)
    if (typeof hideInNoteSearchBar !== 'undefined') hideInNoteSearchBar(); // Hide search bar if open (defined in ui.js)
}


/**
 * Toggles a specific dropdown menu's visibility.
 * @param {HTMLDivElement} dropdown - The dropdown container element.
 */
function toggleDropdown(dropdown) {
    const content = dropdown.querySelector('.Stickara-dropdown-content');
    if (!content) return;
    const isVisible = content.classList.contains('show');
    closeAllDropdowns(); // Close others first
    if (!isVisible) {
        content.classList.add('show');
        content.setAttribute('aria-hidden', 'false');
        // Focus first interactive item (button, select, input) in the dropdown for accessibility
        const firstFocusable = content.querySelector('button, select, input');
        firstFocusable?.focus();
    }
}

/**
 * Closes all open dropdown menus.
 * **MODIFIED: Includes note title input in exception.**
 * @param {MouseEvent} [e] - Optional click event to check if the click was inside a dropdown.
 */
function closeAllDropdowns(e) {
    // Check if the click target is inside an element that should *not* close dropdowns
    const shouldKeepOpen = e?.target.closest(
        `.Stickara-dropdown-content, #${TAGS_ID}, #${REMINDER_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}, #${NOTE_TITLE_INPUT_ID}` // <<< ADD title input ID
    );

    if (shouldKeepOpen) {
        return; // Don't close if click is inside dropdown or specific inputs/modals
    }

    document.querySelectorAll('.Stickara-dropdown-content.show').forEach(content => {
        content.classList.remove('show');
        content.setAttribute('aria-hidden', 'true');
    });
}


/**
 * Toggles the pinned state of the note container (for the current page).
 */
function togglePin() {
    if (!noteContainer) return;
    const isPinned = noteContainer.classList.toggle('pinned');
    const rect = noteContainer.getBoundingClientRect();

    // Set position style based on new state
    noteContainer.style.position = isPinned ? 'fixed' : 'absolute';

    // Adjust top/left based on scroll offset ONLY when changing state
    if (isPinned) {
        // Pinning: Position is now relative to viewport
        noteContainer.style.top = rect.top + 'px';
        noteContainer.style.left = rect.left + 'px';
    } else {
        // Unpinning: Position needs to become relative to document (add scroll)
        noteContainer.style.top = (rect.top + window.scrollY) + 'px';
        noteContainer.style.left = (rect.left + window.scrollX) + 'px';
    }

    updatePinButtonState(isPinned); // Update button appearance (defined in ui.js)
    scheduleSave(); // Save state change
}

/**
 * Toggles the important note state (formerly globally pinned) of the note container.
 */
function toggleGlobalPin() {
    if (!noteContainer) return;
    const isGloballyPinned = noteContainer.classList.toggle('globally-pinned');
    updateGlobalPinButtonState(isGloballyPinned); // Update button appearance (defined in ui.js)
    scheduleSave(); // Save state change (saveCurrentNote now reads the class)
    console.log(`Stickara: Note ${currentNoteIndex} important note status set to ${isGloballyPinned}`);
}

/**
 * Shows the URL-specific note for the current page.
 * Loads the URL-specific note from storage if it exists.
 */
function showUrlNote() {
    if (!noteContainer) return;

    // Update button states
    const urlNoteBtn = document.getElementById('Stickara-url-note-btn');
    const globalNoteBtn = document.getElementById('Stickara-global-note-btn');

    if (urlNoteBtn) {
        urlNoteBtn.classList.add('active');
        urlNoteBtn.title = 'URL-specific note active';
        urlNoteBtn.setAttribute('aria-label', 'URL-specific note active');
    }

    if (globalNoteBtn) {
        globalNoteBtn.classList.remove('active');
        globalNoteBtn.title = 'Show global note (works across all URLs)';
        globalNoteBtn.setAttribute('aria-label', 'Show global note (works across all URLs)');
    }

    // If already in URL-specific mode, do nothing
    if (!noteContainer.classList.contains('global-note')) {
        console.log(`Stickara: Already in URL-specific note mode`);
        return;
    }

    // Save the current note content before switching
    if (notes && notes[currentNoteIndex]) {
        // Create a copy of the current note data
        const currentNoteData = { ...notes[currentNoteIndex] };

        // If we're switching from global to URL-specific, save both versions
        if (typeof saveBothNoteVersions === 'function' && currentNoteData.isGlobal) {
            // Save the global version with current content
            const globalNoteData = { ...currentNoteData, isGlobal: true };

            // For URL-specific, we'll check if one exists first using raw URL like highlights do
            const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
            const urlSpecificKey = `${STORAGE_KEY_PREFIX}${rawUrl}_note${currentNoteIndex}`;

            chrome.storage.local.get(urlSpecificKey, (result) => {
                const existingUrlNote = result[urlSpecificKey];

                if (existingUrlNote) {
                    // If URL-specific note exists, save both versions without changing URL note
                    saveBothNoteVersions(currentNoteIndex, globalNoteData, existingUrlNote, (success) => {
                        if (success) {
                            console.log(`Stickara: Saved both versions of note ${currentNoteIndex} before switching`);
                        }
                    });
                } else {
                    // If no URL-specific note exists, only save the global version
                    const globalNoteKey = `${STORAGE_KEY_PREFIX}global_note${currentNoteIndex}`;
                    chrome.storage.local.set({ [globalNoteKey]: globalNoteData }, () => {
                        if (chrome.runtime.lastError) {
                            console.error(`Stickara: Error saving global note ${currentNoteIndex}:`, chrome.runtime.lastError.message);
                        } else {
                            console.log(`Stickara: Saved global note ${currentNoteIndex} before switching to URL-specific mode`);
                        }
                    });
                }
            });
        }
    }

    // Remove global-note class
    noteContainer.classList.remove('global-note');

    // Store the preference for this note using the centralized function
    saveNoteScopePreference(currentNoteIndex, false);

    // Load the URL-specific note using raw URL like highlights do
    const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
    const urlSpecificKey = `${STORAGE_KEY_PREFIX}${rawUrl}_note${currentNoteIndex}`;

    // Check if a URL-specific note exists
    chrome.storage.local.get(urlSpecificKey, (result) => {
        const urlSpecificNote = result[urlSpecificKey];

        if (urlSpecificNote) {
            // If a URL-specific note exists, load it
            console.log(`Stickara: Found existing URL-specific note ${currentNoteIndex}, loading it`);

            // Update the cache with the URL-specific note
            notes[currentNoteIndex] = { ...createDefaultNoteData(), ...urlSpecificNote };
            notes[currentNoteIndex].isGlobal = false; // Ensure it's marked as non-global

            // Apply the URL-specific note data to the UI
            applyNoteDataToUI(notes[currentNoteIndex]);
        } else {
            // If no URL-specific note exists, create a new one
            console.log(`Stickara: No existing URL-specific note ${currentNoteIndex}, creating one`);

            // Create a new URL-specific note with default data
            const newUrlNote = createDefaultNoteData();
            newUrlNote.isGlobal = false;

            // Update the cache
            notes[currentNoteIndex] = newUrlNote;

            // Apply the new note data to the UI
            applyNoteDataToUI(notes[currentNoteIndex]);

            // Save the new note to storage
            chrome.storage.local.set({ [urlSpecificKey]: newUrlNote }, () => {
                if (chrome.runtime.lastError) {
                    console.error(`Stickara: Error creating URL-specific note ${currentNoteIndex}:`, chrome.runtime.lastError.message);
                } else {
                    console.log(`Stickara: Created new URL-specific note ${currentNoteIndex}`);
                }
            });
        }
    });

    console.log(`Stickara: Switched to URL-specific note mode`);
}

/**
 * Shows the global note that works across all URLs.
 * Loads the global note from storage if it exists.
 */
function showGlobalNote() {
    if (!noteContainer) return;

    // Update button states
    const urlNoteBtn = document.getElementById('Stickara-url-note-btn');
    const globalNoteBtn = document.getElementById('Stickara-global-note-btn');

    if (urlNoteBtn) {
        urlNoteBtn.classList.remove('active');
        urlNoteBtn.title = 'Show URL-specific note (only for this page)';
        urlNoteBtn.setAttribute('aria-label', 'Show URL-specific note (only for this page)');
    }

    if (globalNoteBtn) {
        globalNoteBtn.classList.add('active');
        globalNoteBtn.title = 'Global note active';
        globalNoteBtn.setAttribute('aria-label', 'Global note active');
    }

    // If already in global mode, do nothing
    if (noteContainer.classList.contains('global-note')) {
        console.log(`Stickara: Already in global note mode`);
        return;
    }

    // Save the current note content before switching
    if (notes && notes[currentNoteIndex]) {
        // Create a copy of the current note data
        const currentNoteData = { ...notes[currentNoteIndex] };

        // If we're switching from URL-specific to global, save both versions
        if (typeof saveBothNoteVersions === 'function' && !currentNoteData.isGlobal) {
            // Save the URL-specific version with current content
            const urlSpecificData = { ...currentNoteData, isGlobal: false };

            // For global, we'll check if one exists first
            const globalNoteKey = `${STORAGE_KEY_PREFIX}global_note${currentNoteIndex}`;

            chrome.storage.local.get(globalNoteKey, (result) => {
                const existingGlobalNote = result[globalNoteKey];

                if (existingGlobalNote) {
                    // If global note exists, save both versions without changing global note
                    saveBothNoteVersions(currentNoteIndex, existingGlobalNote, urlSpecificData, (success) => {
                        if (success) {
                            console.log(`Stickara: Saved both versions of note ${currentNoteIndex} before switching`);
                        }
                    });
                } else {
                    // If no global note exists, only save the URL-specific version using raw URL like highlights do
                    const rawUrl = window.stickaraRawCurrentUrl || window.location.href;
                    const urlSpecificKey = `${STORAGE_KEY_PREFIX}${rawUrl}_note${currentNoteIndex}`;
                    chrome.storage.local.set({ [urlSpecificKey]: urlSpecificData }, () => {
                        if (chrome.runtime.lastError) {
                            console.error(`Stickara: Error saving URL-specific note ${currentNoteIndex}:`, chrome.runtime.lastError.message);
                        } else {
                            console.log(`Stickara: Saved URL-specific note ${currentNoteIndex} before switching to global mode`);
                        }
                    });
                }
            });
        }
    }

    // Add global-note class
    noteContainer.classList.add('global-note');

    // Store the preference for this note using the centralized function
    saveNoteScopePreference(currentNoteIndex, true);

    // Load the global note
    const globalNoteKey = `${STORAGE_KEY_PREFIX}global_note${currentNoteIndex}`;

    // Check if a global note exists
    chrome.storage.local.get(globalNoteKey, (result) => {
        const globalNote = result[globalNoteKey];

        if (globalNote) {
            // If a global note exists, load it
            console.log(`Stickara: Found existing global note ${currentNoteIndex}, loading it`);

            // Update the cache with the global note
            notes[currentNoteIndex] = { ...createDefaultNoteData(), ...globalNote };
            notes[currentNoteIndex].isGlobal = true; // Ensure it's marked as global

            // Apply the global note data to the UI
            applyNoteDataToUI(notes[currentNoteIndex]);
        } else {
            // If no global note exists, create a new one
            console.log(`Stickara: No existing global note ${currentNoteIndex}, creating one`);

            // Create a new global note with default data
            const newGlobalNote = createDefaultNoteData();
            newGlobalNote.isGlobal = true;

            // Update the cache
            notes[currentNoteIndex] = newGlobalNote;

            // Apply the new note data to the UI
            applyNoteDataToUI(notes[currentNoteIndex]);

            // Save the new note to storage
            chrome.storage.local.set({ [globalNoteKey]: newGlobalNote }, () => {
                if (chrome.runtime.lastError) {
                    console.error(`Stickara: Error creating global note ${currentNoteIndex}:`, chrome.runtime.lastError.message);
                } else {
                    console.log(`Stickara: Created new global note ${currentNoteIndex}`);
                }
            });
        }
    });

    console.log(`Stickara: Switched to global note mode`);
}

/**
 * Legacy function for toggling global note mode.
 * Now redirects to the new separate functions.
 * @deprecated Use showUrlNote() or showGlobalNote() instead
 */
function toggleGlobalNote() {
    if (!noteContainer) return;

    // Check current state and call the appropriate function
    if (noteContainer.classList.contains('global-note')) {
        showUrlNote(); // Switch to URL-specific note
    } else {
        showGlobalNote(); // Switch to global note
    }
}


/**
 * Toggles the minimized/maximized state of the note container.
 * @deprecated This function is no longer used as the minimize button has been removed
 */
function toggleMinimize() {
    // Function disabled as requested - minimize button and functionality removed
    console.log("Stickara: Minimize functionality has been disabled");
    return;

    // Original code commented out but kept for reference
    /*
    if (!noteContainer || !noteHeader) return;
    const isMinimized = noteContainer.classList.toggle('minimized');

    if (isMinimized) {
        // Store the current height *before* minimizing
        noteContainer.dataset.originalHeight = noteContainer.style.height || window.getComputedStyle(noteContainer).height;
        noteContainer.style.minHeight = '0px'; // Allow shrinking below CSS min-height
        // Calculate minimized height based on header + borders/padding
        const borderTop = parseFloat(window.getComputedStyle(noteContainer).borderTopWidth) || 0;
        const borderBottom = parseFloat(window.getComputedStyle(noteContainer).borderBottomWidth) || 0;
        const paddingTop = parseFloat(window.getComputedStyle(noteContainer).paddingTop) || 0;
        const paddingBottom = parseFloat(window.getComputedStyle(noteContainer).paddingBottom) || 0;
        const headerHeight = noteHeader.offsetHeight;
        noteContainer.style.height = `${headerHeight + borderTop + borderBottom + paddingTop + paddingBottom}px`;
    } else {
        // Restore original height or a default if none was stored
        noteContainer.style.height = noteContainer.dataset.originalHeight || '340px';
        noteContainer.style.minHeight = ''; // Restore minHeight from CSS
    }

    updateMinimizeButtonState(isMinimized); // Update button appearance (defined in ui.js)
    calculateTextAreaHeight(); // Recalculate text area (will hide it if minimized)
    scheduleSave(); // Save state change
    */
}

// --- Formatting and Content Handlers ---

/**
 * Applies a formatting command (bold, italic, etc.) using execCommand.
 * Handles special cases like toggling off block-level formats.
 * @param {string} cmd - The command string.
 * @param {string|null} [value=null] - Optional value for the command (e.g., 'BLOCKQUOTE' for 'formatBlock').
 */
function applyFormatting(cmd, value = null) {
    try {
        // Ensure focus is within the note text area before executing command
        if (!noteText || !noteText.contains(window.getSelection()?.anchorNode)) {
            noteText?.focus(); // Attempt to focus if selection is outside
        }

        // Special handling for block-level formats (BLOCKQUOTE, PRE)
        if (cmd === 'formatBlock' && (value === 'BLOCKQUOTE' || value === 'PRE')) {
            const selection = window.getSelection();
            if (selection && selection.rangeCount > 0) {
                const node = selection.anchorNode;
                let currentBlockElement = null;

                // Find if we're currently in a blockquote or pre element
                if (node) {
                    if (value === 'BLOCKQUOTE') {
                        currentBlockElement = node.parentElement?.closest('blockquote');
                    } else if (value === 'PRE') {
                        currentBlockElement = node.parentElement?.closest('pre');
                    }
                }

                // If we're already in the same block type, toggle it off by converting to paragraph
                if (currentBlockElement && currentBlockElement.parentElement === noteText) {
                    console.log(`Stickara: Toggling off ${value} format`);
                    document.execCommand('formatBlock', false, 'P');
                    noteText?.focus();
                    scheduleSave();
                    return;
                }
            }
        }

        // Use the value if provided, otherwise pass null
        document.execCommand(cmd, false, value);
        noteText?.focus(); // Re-ensure focus in the editor
        scheduleSave(); // Save changes
    } catch (e) {
        console.error(`Stickara: Error applying formatting command '${cmd}' with value '${value}':`, e);
        alert(`Could not apply formatting: ${cmd}.`);
    }
}

/**
 * Applies a highlight (using a <mark> tag) to the current selection
 * *within* the noteText contentEditable element.
 * Uses a distinct CSS class 'Stickara-in-note-highlight'.
 * @param {string} [color=DEFAULT_HIGHLIGHT_COLOR] - The color name (e.g., 'yellow').
 * @param {string} [style='color'] - The style name (e.g., 'color', 'underline', 'wavy', 'border-thick').
 */
function applyInNoteHighlight(color, style) {
    if (!noteText) {
        console.error("Stickara: applyInNoteHighlight - noteText not found.");
        return;
    }
    if (!style) {
        console.error("Stickara: applyInNoteHighlight called without a style.");
        return;
    }

    const selection = window.getSelection();
    if (!selection || selection.isCollapsed || selection.rangeCount === 0) {
        // No selection or selection is empty
        return;
    }

    const range = selection.getRangeAt(0);

    // --- Basic Validation ---
    const validStyles = ['color', 'underline', 'wavy', 'border-thick', 'strikethrough', 'blur'];
    if (!validStyles.includes(style)) {
        console.warn(`Stickara (In-Note): Invalid highlight style '${style}' provided.`);
        return; // Stop
    }
    // Default color only if style is 'color' and color is missing/invalid
    if (style === 'color' && (!color || typeof HIGHLIGHT_COLORS === 'undefined' || !Object.keys(HIGHLIGHT_COLORS).includes(color))) {
        console.warn(`Stickara (In-Note): Style is 'color' but invalid/missing color '${color}' provided. Defaulting.`);
        color = DEFAULT_HIGHLIGHT_COLOR;
    }
    // ---------------------------

    try {
        const mark = document.createElement('mark');
        // Apply mutually exclusive classes
        let classList = `Stickara-in-note-highlight`;
        // Remove existing color/style classes first? Not strictly necessary if we replace entire selection

        if (style === 'color') { // Color style
            classList += ` color-${color}`; // Assumes color is valid/defaulted
            mark.dataset.style = 'color';
            mark.dataset.color = color;
        } else { // Other styles
            classList += ` style-${style}`; // This will now include style-strikethrough
            mark.dataset.style = style;
            // data-color implicitly empty/null
        }
        mark.className = classList;
        // Keep data attributes for debugging/consistency?
        // mark.setAttribute('data-color', color || '');
        // mark.setAttribute('data-style', style);

        // --- Robustly wrap content using extract/insert (like applyHighlight) ---\
        try {
            // Check if range is already within a mark of the *same* style/color - skip if so?
            // (Add later if needed, complexity increases)

            const contents = range.extractContents(); // Extract the selected nodes
            mark.appendChild(contents); // Put the extracted nodes inside the new mark
            range.insertNode(mark); // Insert the mark with its contents back into the range

            // Optional: Re-select the newly inserted mark or collapse selection
            // selection.removeAllRanges();
            // const newRange = document.createRange();
            // newRange.selectNode(mark);
            // selection.addRange(newRange);
            selection.collapseToEnd(); // Simple collapse usually works well

        } catch (e) {
            console.error("Stickara: Failed to wrap selection for in-note highlight using extract/insert.", e);
            alert("Could not apply highlight. The selection might be too complex or cross element boundaries.");
            // Avoid execCommand fallback as it doesn't support styles
        }
        // ---------------------------

        noteText.focus(); // Restore focus
        handleFormattingState(); // Update toolbar state if needed
        scheduleSave(); // Save the change

    } catch (e) {
        console.error(`Stickara: Error applying in-note highlight (Color: ${color}, Style: ${style}):`, e);
        alert("An error occurred while applying the highlight.");
    }
}

/**
 * Changes the font size of the selected text within the note area.
 * If no text is selected, applies font size to future typing.
 * @param {number} delta - The amount to change the font size by (e.g., +2, -2).
 */
function changeTextSize(delta) {
    if (!noteText) return;

    const selection = window.getSelection();
    let currentSize = DEFAULT_FONT_SIZE;

    // Try to get current font size from selection or cursor position
    if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;

        // If it's a text node, get its parent element
        if (element.nodeType === Node.TEXT_NODE) {
            element = element.parentElement;
        }

        // Look for font size in the element or its parents
        while (element && element !== noteText) {
            const computedStyle = window.getComputedStyle(element);
            const fontSize = parseFloat(computedStyle.fontSize);
            if (fontSize && fontSize !== parseFloat(window.getComputedStyle(noteText).fontSize)) {
                currentSize = fontSize;
                break;
            }
            element = element.parentElement;
        }

        // If no specific font size found, use the note's default
        if (currentSize === DEFAULT_FONT_SIZE) {
            const noteComputedStyle = window.getComputedStyle(noteText);
            currentSize = parseFloat(noteComputedStyle.fontSize) || DEFAULT_FONT_SIZE;
        }
    }

    // Calculate new size (clamped between 10px and 48px)
    const newSize = Math.max(10, Math.min(currentSize + delta, 48));

    // Save the font size preference for future notes
    if (notes && notes[currentNoteIndex]) {
        notes[currentNoteIndex].preferredFontSize = newSize;
    }

    // Use the setFontSize function if it exists
    if (typeof setFontSize === 'function') {
        setFontSize(newSize);
    } else {
        // Fallback approach
        try {
            // Focus the note text area
            noteText.focus();

            // Use execCommand to wrap selection in a font tag with size 7 (largest)
            document.execCommand('fontSize', false, '7');

            // Find all font elements just created and convert to spans with the right size
            const fonts = noteText.querySelectorAll('font[size="7"]');
            fonts.forEach(font => {
                const span = document.createElement('span');
                span.style.fontSize = newSize + 'px';
                span.innerHTML = font.innerHTML;
                font.parentNode.replaceChild(span, font);
            });
        } catch (e) {
            console.error("Stickara: Error applying font size:", e);
        }
    }

    noteText.focus();
    scheduleSave();
}

/**
 * Handles clicks within the noteText area, specifically checking for equations,
 * simulated checklist items, TIMESTAMP LINKS, and VIDEO SOURCE URL LINKS.
 * PRIORITIZES allowing default action for the source URL link.
 * @param {MouseEvent} e - The click event.
 */
function handlePotentialActionClick(e) {
    const target = e.target;

    // --- PRIORITY CHECK: Allow default action for the Source URL link and Element Picker links ---
    // Check if the direct click target OR its parent is a link we want to allow.
    const videoSourceLink = target.closest('p.Stickara-video-associated-url a');
    const elementPickerLink = target.closest('a[contenteditable="false"]');

    if (videoSourceLink || elementPickerLink) {
        console.log("Stickara: External link clicked. INTENTIONALLY allowing default browser action.");
        // Let the browser handle the link navigation
        return; // Exit the handler immediately, allowing the default action.
    }
    // --- END PRIORITY CHECK ---

    // Now check for other interactive elements where we *do* want to prevent default

    // Check for timestamp link click (Stickara's own timestamp links)
    const timestampLink = target.closest(`.${TIMESTAMP_LINK_CLASS}`); // Use class defined in state.js
    if (timestampLink) {
        // Check for Ctrl+click for video screenshot
        if (e.ctrlKey) {
            console.log("Stickara: Ctrl+click on Stickara timestamp detected. Capturing video screenshot.");
            e.preventDefault(); // Prevent default span behavior
            handleVideoTimestampScreenshot(timestampLink); // Call the video screenshot handler
            return; // Handled
        } else {
            console.log("Stickara: Stickara timestamp link clicked. Preventing default and handling seek.");
            e.preventDefault(); // Prevent default span behavior
            handleTimestampClick(timestampLink); // Call the handler function below
            return; // Handled
        }
    }

    // Check for Ctrl+click on any timestamp-like element on the page
    if (e.ctrlKey) {
        const timestampInfo = detectVideoTimestamp(target);
        if (timestampInfo) {
            console.log("Stickara: Ctrl+click on webpage timestamp detected. Capturing video screenshot.");
            e.preventDefault(); // Prevent default behavior
            handleWebpageTimestampScreenshot(target, timestampInfo); // Call the webpage timestamp screenshot handler
            return; // Handled
        }
    }

    // Check for equation span
    const equationSpan = target.closest('.Stickara-equation');
    if (equationSpan) {
        console.log("Stickara: Equation span clicked. Handling editor open.");
        e.preventDefault(); // Prevent potential text selection issues
        openEquationEditor(equationSpan); // Assumes defined elsewhere
        return; // Handled
    }

    // Check for checklist item click
    const checkListItem = target.closest('li.Stickara-checklist-item');
    if (checkListItem) {
        console.log("Stickara: Checklist item clicked. Preventing default and toggling state.");
        e.preventDefault(); // Prevent potential text selection
        checkListItem.classList.toggle('checked');
        scheduleSave(); // Save state change
        return; // Handled
    }

    // Check for the old video title link (obsolete, defensive check)
    const videoTitleLink = target.closest('h3 > a.Stickara-video-title-link');
    if (videoTitleLink) {
        console.warn("Stickara: Click detected on potentially obsolete title link. Preventing default.");
        e.preventDefault();
        return; // Handled (by preventing default)
    }

    // If no specific action element was clicked, allow default behavior
}


/**
 * Inserts a placeholder for a LaTeX equation and opens the advanced editor.
 */
function insertEquation() {
    console.log("Stickara: insertEquation called");

    const equationSpan = document.createElement('span');
    equationSpan.className = 'Stickara-equation'; // CSS class
    equationSpan.contentEditable = 'false'; // Prevent direct editing of KaTeX output
    equationSpan.setAttribute('data-latex', ''); // Store LaTeX source here
    equationSpan.innerHTML = '[ New Equation ]'; // Placeholder text
    equationSpan.title = "Click to edit equation";
    equationSpan.style.cursor = 'pointer'; // Indicate clickable

    console.log("Stickara: Created equation span, inserting at cursor");
    insertNodeAtCursor(equationSpan); // Utility function

    console.log("Stickara: Opening equation editor");
    openEquationEditor(equationSpan); // Open advanced editor immediately

    console.log("Stickara: insertEquation function completed successfully");
}

/**
 * Opens the advanced equation editor modal for editing LaTeX equations.
 * @param {HTMLElement} equationSpan - The span element containing the equation.
 */
function openEquationEditor(equationSpan) {
    console.log("Stickara: openEquationEditor called");
    console.log("Stickara: Checking if openAdvancedEquationEditor exists:", typeof openAdvancedEquationEditor);

    // Check if the advanced equation editor function exists
    if (typeof openAdvancedEquationEditor === 'function') {
        console.log("Stickara: Opening advanced equation editor");
        openAdvancedEquationEditor(equationSpan);
    } else {
        console.log("Stickara: Advanced editor not available, using simple fallback");
        // Fallback to simple prompt if advanced editor not available
        openSimpleEquationEditor(equationSpan);
    }
}

/**
 * Simple fallback equation editor using prompt.
 * @param {HTMLElement} equationSpan - The span element containing the equation.
 */
function openSimpleEquationEditor(equationSpan) {
    console.log("Stickara: Using simple equation editor fallback");
    const currentLatex = equationSpan.getAttribute('data-latex') || '';
    const newLatex = prompt('Enter LaTeX code (e.g., E = mc^2):\n\nExamples:\n- E = mc^2\n- x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}\n- \\int_a^b f(x) dx\n- \\sum_{i=1}^{n} x_i', currentLatex);

    if (newLatex !== null) { // Only proceed if user didn't cancel prompt
        console.log("Stickara: Updating equation with LaTeX:", newLatex);
        updateEquationSpan(equationSpan, newLatex);
    } else {
        console.log("Stickara: User cancelled equation editing");
    }
}

/**
 * Updates an equation span with new LaTeX content and renders it.
 * @param {HTMLElement} equationSpan - The span element to update.
 * @param {string} latex - The LaTeX code to render.
 */
function updateEquationSpan(equationSpan, latex) {
    equationSpan.setAttribute('data-latex', latex);

    // Make the span editable for cursor positioning but protect the content
    equationSpan.contentEditable = 'false';

    try {
        if (typeof katex !== 'undefined') {
            katex.render(latex.trim() || '\\color{grey}{\\text{[Empty]}}', equationSpan, {
                throwOnError: false, // Don't throw fatal errors, show error message in span
                displayMode: false, // Render inline
                output: 'html', // Ensure HTML output for contentEditable compatibility
                strict: false // Allow more flexible LaTeX
            });

            // Apply normal text styling to make equations blend with text
            equationSpan.style.fontSize = 'inherit';
            equationSpan.style.lineHeight = 'inherit';
            equationSpan.style.color = 'inherit';
            equationSpan.style.fontFamily = 'inherit';
            equationSpan.style.display = 'inline';
            equationSpan.style.verticalAlign = 'baseline';
            equationSpan.style.margin = '0 2px';
            equationSpan.style.padding = '1px 3px';
            equationSpan.style.borderRadius = '2px';
            equationSpan.style.background = 'rgba(0, 0, 0, 0.02)';
            equationSpan.style.border = '1px solid transparent';

        } else {
            console.error("Stickara: KaTeX library not loaded.");
            equationSpan.innerHTML = '<span style="color: red; font-family: monospace;">[KaTeX Error!]</span>';
        }
    } catch (e) {
        equationSpan.innerHTML = `<span style="color: red; font-family: monospace;">[Invalid LaTeX]</span>`;
        console.error("Stickara: KaTeX rendering error:", e);
    }
    scheduleSave(); // Save the updated note content
}

/**
 * Inserts the current date and/or time at the cursor position.
 * Formats the date/time in a user-friendly format.
 */
function handleAddDateTimeNote() {
    if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
        showNote();
        // Use internal function after a delay to ensure UI is ready
        setTimeout(handleAddDateTimeNoteInternal, 150);
    } else {
        handleAddDateTimeNoteInternal();
    }
}

// Internal function to perform the date/time addition
function handleAddDateTimeNoteInternal() {
    if (!noteText) {
        console.error("Stickara: noteText element not found for date/time insertion.");
        return;
    }

    // Get current date and time
    const now = new Date();

    // Format the date and time with improved formatting
    const formattedDate = now.toLocaleDateString(undefined, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    const formattedTime = now.toLocaleTimeString(undefined, {
        hour: '2-digit',
        minute: '2-digit'
    });

    // Create simple plain text with emojis and good spacing
    const dateTimeText = `📅 ${formattedDate}  ⏱️ ${formattedTime}`;

    // Focus the note text area
    noteText.focus();

    // Insert plain text at cursor position
    document.execCommand('insertText', false, dateTimeText);

    // Schedule save
    scheduleSave();

    console.log(`Stickara: Styled date/time inserted`);
}



// --- Timestamped Notes Handlers ---

/**
 * Enhanced helper to find the most likely active media element (video or audio).
 * Prioritizes visible, playing VIDEOS with improved cross-platform compatibility.
 * @returns {HTMLMediaElement|null} The found media element or null.
 */
function findActiveMediaElement() {
    const mediaElements = document.querySelectorAll('video, audio');
    let bestVideoCandidate = null;
    let bestAudioCandidate = null;
    let maxVideoPlayTime = 0;
    let maxAudioPlayTime = 0;

    // Enhanced platform-specific selectors for better compatibility
    const platformSpecificSelectors = [
        // YouTube
        'video.html5-main-video',
        'video.video-stream',
        // Vimeo
        'video[data-player-id]',
        // Twitch
        'video[data-a-target="video-player"]',
        // General HTML5 video
        'video[controls]',
        'video[autoplay]',
        // Audio elements
        'audio[controls]',
        'audio[autoplay]'
    ];

    // First, try platform-specific selectors
    for (const selector of platformSpecificSelectors) {
        const specificElements = document.querySelectorAll(selector);
        for (const el of specificElements) {
            try {
                if (!el || el.paused || el.ended) continue;

                // More lenient checks for platform-specific elements
                const hasValidTime = el.currentTime > 0 || (!el.paused && el.readyState >= 2);
                const hasValidSrc = el.currentSrc || el.src || el.querySelector('source');

                if (!hasValidTime || !hasValidSrc) continue;

                const rect = el.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0 &&
                                rect.top < window.innerHeight && rect.bottom > 0 &&
                                rect.left < window.innerWidth && rect.right > 0;

                if (!isVisible) continue;

                if (el.tagName === 'VIDEO') {
                    if (el.currentTime > maxVideoPlayTime) {
                        bestVideoCandidate = el;
                        maxVideoPlayTime = el.currentTime;
                    }
                } else if (el.tagName === 'AUDIO') {
                    if (!bestVideoCandidate && el.currentTime > maxAudioPlayTime) {
                        bestAudioCandidate = el;
                        maxAudioPlayTime = el.currentTime;
                    }
                }
            } catch (e) {
                console.warn("[findActiveMediaElement] Error checking platform-specific media element:", e, el);
            }
        }
    }

    // If platform-specific search found something, return it
    if (bestVideoCandidate || bestAudioCandidate) {
        return bestVideoCandidate || bestAudioCandidate;
    }

    // Fallback to general search with original logic
    for (const el of mediaElements) {
        try {
            if (!el || el.paused || el.ended || el.currentTime <= 0 || !el.currentSrc) continue;
            const rect = el.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 && rect.top < window.innerHeight && rect.bottom > 0 && rect.left < window.innerWidth && rect.right > 0;
            if (!isVisible) continue;

            if (el.tagName === 'VIDEO') {
                if (el.currentTime > maxVideoPlayTime) {
                    bestVideoCandidate = el; maxVideoPlayTime = el.currentTime;
                }
            } else if (el.tagName === 'AUDIO') {
                 if (!bestVideoCandidate && el.currentTime > maxAudioPlayTime) {
                    bestAudioCandidate = el; maxAudioPlayTime = el.currentTime;
                }
            }
        } catch (e) { console.warn("[findActiveMediaElement] Error checking media element:", e, el); }
    }
    return bestVideoCandidate || bestAudioCandidate;
}


/**
 * Attempts to extract the title for a given media element.
 * Tries common patterns (YouTube, attributes, nearby headings, page title).
 * @param {HTMLMediaElement} mediaElement - The video or audio element.
 * @returns {string} The extracted title or a fallback string.
 */
function getVideoTitle(mediaElement) {
    if (!mediaElement) return "Media";

    // 1. YouTube Specific Selectors
    if (document.location.hostname.includes("youtube.com")) {
        const ytSelectors = ['#container > h1.title > yt-formatted-string', '#info-contents h1.title yt-formatted-string', '#info h1 yt-formatted-string', 'h1.title yt-formatted-string', '#video-title', '#watch-headline-title h1', '.slim-video-metadata-title', '.ytp-title-link'];
        for (const selector of ytSelectors) {
            const ytTitleElement = document.querySelector(selector);
            if (ytTitleElement && ytTitleElement.textContent?.trim()) return ytTitleElement.textContent.trim();
        }
    }

    // 2. Player Containers
    const playerContainer = mediaElement.closest('.player-container, .video-player, .media-player, .vjs-player, [data-video-id], [role="main"]');
    if (playerContainer) {
        const containerLabel = playerContainer.getAttribute('aria-label') || playerContainer.title;
        if (containerLabel && containerLabel.trim().length > 5 && !containerLabel.toLowerCase().includes('player')) return containerLabel.trim();
        const titleInContainer = playerContainer.querySelector('.video-title, .media-title, .title, .player-title, h1, h2, h3');
        if (titleInContainer && titleInContainer.textContent?.trim()) return titleInContainer.textContent.trim();
    }

    // 3. Media Element Attributes
    const mediaLabel = mediaElement.getAttribute('aria-label') || mediaElement.title;
     if (mediaLabel && mediaLabel.trim().length > 5 && !mediaLabel.toLowerCase().includes('player')) return mediaLabel.trim();

    // 4. Nearby Heading
    let parent = mediaElement.parentElement;
    for (let i = 0; i < 5 && parent; i++) {
        const heading = parent.querySelector('h1, h2, h3');
        if (heading && heading.textContent?.trim()) return heading.textContent.trim();
        parent = parent.parentElement;
    }

    // 5. Document Title
    if (document.title) return document.title.split(/ [-|–—:] /)[0].trim();

    return "Media Content";
}

/**
 * Inserts a timestamped note marker at the current cursor position.
 * On the first click for a video in the current note, inserts the title (non-linked),
 * the source URL (linked), and starts a list.
 * Saves title/URL to note data. Schedules save reliably.
 * **MODIFIED:** Captures window.location.href instead of mediaElement.currentSrc.
 * **MODIFIED:** Source URL link generation now adds `contenteditable="false"`.
 */
function handleAddTimestampNote() {
    if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
        showNote();
        // Use internal function after a delay to ensure UI is ready
        setTimeout(handleAddTimestampNoteInternal, 150);
    } else {
        handleAddTimestampNoteInternal();
    }
}

// Internal function to perform the timestamp addition
function handleAddTimestampNoteInternal() {
     // console.log("Stickara: handleAddTimestampNoteInternal started."); // DEBUG: Less noisy
     if (!noteText || !notes || !notes[currentNoteIndex]) {
        console.error("Stickara: noteText element or current note data not found for timestamp insertion.");
        return;
     }

     const mediaElement = findActiveMediaElement();
     if (!mediaElement) {
         alert("Stickara: Could not find an active video or audio element playing on the page.");
         return;
     }

     const currentTimeSeconds = mediaElement.currentTime;

     // Enhanced timestamp formatting with automatic format selection
     const videoDuration = mediaElement.duration || 0;
     const useSubSeconds = videoDuration > 0 && videoDuration < 300; // Use sub-second precision for videos under 5 minutes
     const formattedTime = formatSecondsToMMSS(currentTimeSeconds, useSubSeconds);

     // *** USE PAGE URL ***
     const pageUrl = window.location.href; // Use the document's URL

     const storedVideoUrl = notes[currentNoteIndex].associatedVideoUrl;
     // *** COMPARE WITH PAGE URL ***
     const isFirstTimeForThisPage = !storedVideoUrl || (pageUrl !== storedVideoUrl);

     let htmlToInsert = '';
     // Simple timestamp span with enhanced precision but original styling
     const timestampSpanHTML = `<span class="${TIMESTAMP_LINK_CLASS}" data-timestamp="${currentTimeSeconds}" data-formatted-time="${formattedTime}" contenteditable="false" title="Click to seek media to ${formattedTime}">[@${formattedTime}]</span> `;

     // *** USE pageUrl for logic and link generation ***
     if (isFirstTimeForThisPage && pageUrl) {
         const title = getVideoTitle(mediaElement);
         const escapedTitle = window.escapeHtml(title);
         // Use pageUrl for the link href and display text
         const safeUrl = window.escapeHtml(pageUrl);

         const titleHTML = `<h3>${escapedTitle}</h3>`; // Title is just text
         // *** ADDED contenteditable="false" to the A tag ***
         const urlDisplayHTML = `<p class="Stickara-video-associated-url"><small>Source: <a href="${safeUrl}" target="_blank" rel="noopener noreferrer" title="Open page source" contenteditable="false">${safeUrl}</a></small></p>`; // URL is linked

         // Store the PAGE URL in the note data
         notes[currentNoteIndex].associatedVideoUrl = pageUrl;
         notes[currentNoteIndex].associatedVideoTitle = title;
         // console.log(`Stickara: Updated note ${currentNoteIndex} cache with Page URL/Title.`); // DEBUG: Less noisy

         // Insert structure: title, linked URL, list with timestamp (no leading break)
         htmlToInsert = `${titleHTML}${urlDisplayHTML}<ul>\n  <li>${timestampSpanHTML}</li>\n</ul>\n<br>`;

         // Update UI state variable (consider renaming lastTimestampedVideoSrc -> lastTimestampedPageUrl later if feasible)
         lastTimestampedVideoSrc = pageUrl;
         // console.log(`Stickara: First timestamp for page "${title}" (${pageUrl}). Inserting title, linked URL, and list.`); // DEBUG: Less noisy

     } else {
         // Subsequent timestamp logic: add timestamp to existing list if possible, or just insert LI
         const lastUL = noteText.querySelector('ul:last-of-type');
         if (lastUL) {
             const newLi = document.createElement('li');
             newLi.innerHTML = timestampSpanHTML;
             lastUL.appendChild(newLi);
             htmlToInsert = null; // Indicate we don't need insertHtmlAtCursor
             // console.log(`Stickara: Appended subsequent timestamp ${formattedTime} to last list.`); // DEBUG: Less noisy
         } else {
             htmlToInsert = `<ul><li>${timestampSpanHTML}</li></ul>`;
             // console.log(`Stickara: Inserting subsequent timestamp ${formattedTime} in a new list.`); // DEBUG: Less noisy
         }

         // This case might be less relevant now we always use pageUrl, but keep for safety
         if (isFirstTimeForThisPage && !pageUrl) {
             console.warn(`Stickara: First timestamp, but page URL is missing? Inserting timestamp without title.`);
             notes[currentNoteIndex].associatedVideoUrl = 'unknown';
             notes[currentNoteIndex].associatedVideoTitle = '[Unknown Page]';
             lastTimestampedVideoSrc = 'unknown';
         }
     }
     // *** END CHANGES related to pageUrl ***

     // --- Insert HTML and Ensure Save ---
     try {
         if (htmlToInsert !== null) {
            // console.log(`[Timestamp] Calling insertHtmlAtCursor for: ${htmlToInsert.substring(0, 50)}...`); // DEBUG: Less noisy
            insertHtmlAtCursor(htmlToInsert);
            // console.log("[Timestamp] insertHtmlAtCursor finished."); // DEBUG: Less noisy
         } else {
            // console.log("[Timestamp] Appended LI directly, scheduling save."); // DEBUG: Less noisy
             scheduleSave();
         }

        // --- Focus and scroll manipulation ---
        noteText.focus();
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
            selection.collapseToEnd();
            const tempSpan = document.createElement('span');
            try {
                const range = selection.getRangeAt(0);
                range.insertNode(tempSpan);
                tempSpan.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                const parent = tempSpan.parentNode;
                parent?.removeChild(tempSpan);
                parent?.normalize();
            } catch (e) {
                 console.warn("Stickara: Could not insert/remove temp span for scrolling.", e);
                 noteText.scrollTop = noteText.scrollHeight;
            }
        } else {
             noteText.scrollTop = noteText.scrollHeight;
        }
        // ---------------------------------

     } catch (insertionError) {
         console.error("Stickara: Error during timestamp insertion logic or focus/scroll:", insertionError);
     } finally {
         // Ensure save is scheduled if not already handled
         if (htmlToInsert === null && !saveTimeout) {
            scheduleSave();
         }
     }
}

/**
 * Handles the click on a timestamp link within the note.
 * Attempts to find the active media and seek to the stored time.
 * Enhanced with better feedback and cross-platform compatibility.
 * @param {HTMLSpanElement} timestampElement - The clicked timestamp span.
 */
function handleTimestampClick(timestampElement) {
    const timestampSeconds = parseFloat(timestampElement.dataset.timestamp);
    const formattedTime = timestampElement.dataset.formattedTime || formatSecondsToMMSS(timestampSeconds);

    if (isNaN(timestampSeconds) || timestampSeconds < 0) {
        console.error("Stickara: Invalid timestamp data found:", timestampElement.dataset.timestamp);
        alert("Stickara: Could not read the timestamp value.");
        return;
    }

    const mediaElement = findActiveMediaElement();
    if (!mediaElement) {
        alert("Stickara: Could not find the active video/audio element on the page to seek.");
        return;
    }

    try {
        // Enhanced seeking with better feedback
        const originalTime = mediaElement.currentTime;
        mediaElement.currentTime = timestampSeconds;

        // Simple visual feedback without changing colors
        const originalOpacity = timestampElement.style.opacity;
        timestampElement.style.opacity = '0.5';
        setTimeout(() => {
            timestampElement.style.opacity = originalOpacity;
        }, 200);

        console.log(`Stickara: Seeked media element from ${formatSecondsToMMSS(originalTime)} to ${formattedTime} (${timestampSeconds}s).`);

        // Auto-play if paused (with user gesture consideration)
        if (mediaElement.paused) {
            mediaElement.play().catch(e => {
                console.warn("Stickara: Could not automatically play media after seek (user interaction may be required):", e);
                // Show a subtle notification instead of console warning
                if (typeof showStatus === 'function') {
                    showStatus(`Seeked to ${formattedTime}. Click play to resume.`, 'info');
                }
            });
        }

        // Scroll media into view with enhanced positioning
        mediaElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });

        // Focus the media element for keyboard controls
        if (mediaElement.focus) {
            mediaElement.focus();
        }

    } catch (error) {
        console.error("Stickara: Error seeking media element:", error);
        alert(`Stickara: An error occurred while trying to seek to ${formattedTime}.`);
    }
}

/**
 * Handles Ctrl+click on timestamp links to capture video screenshots.
 * Seeks to the timestamp and captures a high-resolution screenshot of the video.
 * @param {HTMLSpanElement} timestampElement - The clicked timestamp span.
 */
async function handleVideoTimestampScreenshot(timestampElement) {
    // Prevent multiple simultaneous screenshot operations
    if (isVideoScreenshotInProgress) {
        console.log("Stickara: Video screenshot already in progress, ignoring request");
        return;
    }

    const timestampSeconds = parseFloat(timestampElement.dataset.timestamp);
    const formattedTime = timestampElement.dataset.formattedTime || formatSecondsToMMSS(timestampSeconds);

    if (isNaN(timestampSeconds) || timestampSeconds < 0) {
        console.error("Stickara: Invalid timestamp data found:", timestampElement.dataset.timestamp);
        alert("Stickara: Could not read the timestamp value.");
        return;
    }

    const mediaElement = findActiveMediaElement();
    if (!mediaElement) {
        alert("Stickara: Could not find the active video/audio element on the page to capture.");
        return;
    }

    // Only proceed if it's a video element (not audio)
    if (mediaElement.tagName !== 'VIDEO') {
        alert("Stickara: Video screenshots are only supported for video elements, not audio.");
        return;
    }

    try {
        isVideoScreenshotInProgress = true;
        lastVideoScreenshotTimestamp = timestampSeconds;

        // Show status feedback
        if (typeof showStatus === 'function') {
            showStatus(`Capturing video screenshot at ${formattedTime}...`, 'info');
        }

        // Seek to the timestamp first
        const originalTime = mediaElement.currentTime;
        mediaElement.currentTime = timestampSeconds;

        // Wait for the video to seek to the correct position
        await new Promise((resolve) => {
            const onSeeked = () => {
                mediaElement.removeEventListener('seeked', onSeeked);
                resolve();
            };
            mediaElement.addEventListener('seeked', onSeeked);

            // Fallback timeout in case seeked event doesn't fire
            setTimeout(() => {
                mediaElement.removeEventListener('seeked', onSeeked);
                resolve();
            }, 2000);
        });

        // Additional small delay to ensure frame is fully rendered
        await new Promise(resolve => setTimeout(resolve, 100));

        // Capture the video screenshot
        await captureVideoScreenshot(mediaElement, formattedTime);

        console.log(`Stickara: Video screenshot captured at ${formattedTime} (${timestampSeconds}s).`);

    } catch (error) {
        console.error("Stickara: Error capturing video screenshot:", error);
        if (typeof showStatus === 'function') {
            showStatus(`Failed to capture video screenshot: ${error.message}`, 'error');
        } else {
            alert("Stickara: Could not capture video screenshot. Please try again.");
        }
    } finally {
        isVideoScreenshotInProgress = false;
    }
}

/**
 * Handles Ctrl+click on webpage timestamp elements to capture video screenshots.
 * Works with any timestamp-like element found on the webpage.
 * @param {HTMLElement} element - The clicked timestamp element
 * @param {Object} timestampInfo - The parsed timestamp information
 */
async function handleWebpageTimestampScreenshot(element, timestampInfo) {
    // Prevent multiple simultaneous screenshot operations
    if (isVideoScreenshotInProgress) {
        console.log("Stickara: Video screenshot already in progress, ignoring request");
        return;
    }

    const timestampSeconds = timestampInfo.totalSeconds;
    const formattedTime = timestampInfo.formattedTime;

    const mediaElement = findActiveMediaElement();
    if (!mediaElement) {
        alert("Stickara: Could not find the active video/audio element on the page to capture.");
        return;
    }

    // Only proceed if it's a video element (not audio)
    if (mediaElement.tagName !== 'VIDEO') {
        alert("Stickara: Video screenshots are only supported for video elements, not audio.");
        return;
    }

    try {
        isVideoScreenshotInProgress = true;
        lastVideoScreenshotTimestamp = timestampSeconds;

        // Show status feedback
        if (typeof showStatus === 'function') {
            showStatus(`Capturing video screenshot at ${formattedTime}...`, 'info');
        }

        // Seek to the timestamp first
        const originalTime = mediaElement.currentTime;
        mediaElement.currentTime = timestampSeconds;

        // Wait for the video to seek to the correct position
        await new Promise((resolve) => {
            const onSeeked = () => {
                mediaElement.removeEventListener('seeked', onSeeked);
                resolve();
            };
            mediaElement.addEventListener('seeked', onSeeked);

            // Fallback timeout in case seeked event doesn't fire
            setTimeout(() => {
                mediaElement.removeEventListener('seeked', onSeeked);
                resolve();
            }, 2000);
        });

        // Additional small delay to ensure frame is fully rendered
        await new Promise(resolve => setTimeout(resolve, 100));

        // Capture the video screenshot
        await captureVideoScreenshot(mediaElement, formattedTime);

        console.log(`Stickara: Video screenshot captured from webpage timestamp at ${formattedTime} (${timestampSeconds}s).`);

    } catch (error) {
        console.error("Stickara: Error capturing video screenshot from webpage timestamp:", error);
        if (typeof showStatus === 'function') {
            showStatus(`Failed to capture video screenshot: ${error.message}`, 'error');
        } else {
            alert("Stickara: Could not capture video screenshot. Please try again.");
        }
    } finally {
        isVideoScreenshotInProgress = false;
    }
}

// --- End Timestamped Notes Handlers ---

/**
 * Handles Enter key press to preserve formatting (font family, font size, etc.)
 * @param {KeyboardEvent} e - The keyboard event
 */
function handleEnterKeyFormatting(e) {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let element = range.commonAncestorContainer;

    // If it's a text node, get its parent element
    if (element.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
    }

    // Collect current formatting styles
    const currentStyles = {};
    while (element && element !== noteText) {
        if (element.style.fontFamily && !currentStyles.fontFamily) {
            currentStyles.fontFamily = element.style.fontFamily;
        }
        if (element.style.fontSize && !currentStyles.fontSize) {
            currentStyles.fontSize = element.style.fontSize;
        }
        if (element.style.fontWeight && !currentStyles.fontWeight) {
            currentStyles.fontWeight = element.style.fontWeight;
        }
        if (element.style.fontStyle && !currentStyles.fontStyle) {
            currentStyles.fontStyle = element.style.fontStyle;
        }
        if (element.style.textDecoration && !currentStyles.textDecoration) {
            currentStyles.textDecoration = element.style.textDecoration;
        }
        element = element.parentElement;
    }

    // If we have styles to preserve, set up a timeout to apply them after the Enter key creates a new line
    if (Object.keys(currentStyles).length > 0) {
        setTimeout(() => {
            const newSelection = window.getSelection();
            if (newSelection && newSelection.rangeCount > 0) {
                const newRange = newSelection.getRangeAt(0);

                // Create a span with the preserved styles
                const span = document.createElement('span');
                Object.assign(span.style, currentStyles);
                span.innerHTML = '&#8203;'; // Zero-width space

                // Insert the styled span at the cursor
                try {
                    newRange.insertNode(span);

                    // Move cursor to the end of the span
                    const finalRange = document.createRange();
                    finalRange.setStartAfter(span);
                    finalRange.collapse(true);
                    newSelection.removeAllRanges();
                    newSelection.addRange(finalRange);
                } catch (err) {
                    console.warn("Stickara: Error preserving formatting on Enter:", err);
                }
            }
        }, 10); // Small delay to let the Enter key create the new line first
    }
}

/**
 * Handles Escape key press to exit block-level formats (blockquote, pre).
 * @param {KeyboardEvent} e - The keyboard event
 */
function handleEscapeKeyFormatting(e) {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const node = selection.anchorNode;
    if (!node) return;

    // Check if we're in a blockquote or pre element
    const blockquoteParent = node.parentElement?.closest('blockquote');
    const preParent = node.parentElement?.closest('pre');

    if ((blockquoteParent && blockquoteParent.parentElement === noteText) ||
        (preParent && preParent.parentElement === noteText)) {

        e.preventDefault();
        e.stopPropagation();

        try {
            // Convert to paragraph to exit the block format
            document.execCommand('formatBlock', false, 'P');
            noteText?.focus();
            scheduleSave();

            // Update formatting state to reflect the change
            setTimeout(() => {
                if (typeof handleFormattingState === 'function') {
                    handleFormattingState();
                }
            }, 10);

            console.log("Stickara: Exited block format using Escape key");
        } catch (err) {
            console.error("Stickara: Error exiting block format:", err);
        }
    }
}

/**
 * Updates the active state of formatting buttons based on the current selection.
 */
function handleFormattingState() {
    // Only run if the note is visible and the necessary elements exist
    if (!noteContainer || !noteContainer.classList.contains('visible') || !noteText) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !noteText.contains(selection.anchorNode)) {
        // If selection is outside noteText, deactivate all buttons
        noteContainer.querySelectorAll('.Stickara-format-btn').forEach(button => {
            button.classList.remove('active');
        });
        return;
    }

    // Deactivate all first, then selectively activate based on queryCommandState
    noteContainer.querySelectorAll('.Stickara-format-btn').forEach(button => {
         button.classList.remove('active');
    });

    const simpleCommands = ['bold', 'italic', 'underline', 'strikethrough', 'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'];
    simpleCommands.forEach(cmd => {
        const button = noteContainer.querySelector(`.Stickara-format-btn[data-cmd="${cmd}"]`);
        if (button) {
            try {
                if (document.queryCommandState(cmd)) {
                    button.classList.add('active');
                }
            } catch (err) {
                console.warn(`Stickara: Error querying command state for '${cmd}':`, err);
            }
        }
    });

    // Special handling for lists (check if selection parent is LI within UL/OL)
    try {
        const node = selection.anchorNode;
        if (node) {
            const listParent = node.parentElement?.closest('ol, ul');
            if (listParent?.parentElement === noteText || listParent?.parentElement?.closest(`#${TEXTAREA_ID}`)) { // Ensure list is directly inside noteText
                if (listParent.tagName === 'OL') {
                    noteContainer.querySelector('.Stickara-format-btn[data-cmd="insertOrderedList"]')?.classList.add('active');
                } else if (listParent.tagName === 'UL') {
                    noteContainer.querySelector('.Stickara-format-btn[data-cmd="insertUnorderedList"]')?.classList.add('active');
                }
            }
        }
    } catch (err) {
        console.warn("Stickara: Error checking list state:", err);
    }


    // Special handling for blockquote and pre (check parent node)
    try {
        const node = selection.anchorNode;
         if (node) {
            const blockquoteParent = node.parentElement?.closest('blockquote');
            if (blockquoteParent && blockquoteParent.parentElement === noteText) {
                noteContainer.querySelector('.Stickara-format-btn[data-value="BLOCKQUOTE"]')?.classList.add('active');
            }
            const preParent = node.parentElement?.closest('pre');
            if (preParent && preParent.parentElement === noteText) {
                 noteContainer.querySelector('.Stickara-format-btn[data-value="PRE"]')?.classList.add('active');
             }
        }
    } catch (err) {
        console.warn("Stickara: Error checking block state:", err);
    }

}


/**
 * Changes the note's background color theme.
 * @param {HTMLButtonElement} swatchElement - The clicked color swatch button.
 */
function changeColor(swatchElement) {
    if (!noteContainer) return;
    const newColor = swatchElement.dataset.color;
    if (!newColor) return;
    noteContainer.className = noteContainer.className.replace(/theme-\w+/g, '').trim();
    noteContainer.classList.add(`theme-${newColor}`);
    updateActiveSwatch(newColor); // Update button state (ui.js)
    scheduleSave();
}

/**
 * Changes the note's opacity.
 * @param {HTMLButtonElement} buttonElement - The clicked opacity button.
 */
function changeOpacity(buttonElement) {
    if (!noteContainer) return;
    const newOpacity = parseFloat(buttonElement.dataset.opacity);
    if (isNaN(newOpacity)) return;
    noteContainer.style.opacity = String(newOpacity);
    updateActiveOpacity(newOpacity); // Update button state (ui.js)
    scheduleSave();
}

/**
 * Toggles text shadow effect for all content in the note.
 * @param {HTMLButtonElement} buttonElement - The clicked text shadow button.
 */
function toggleTextShadow(buttonElement) {
    if (!noteContainer || !noteText) return;

    const isCurrentlyEnabled = buttonElement.dataset.textShadow === 'true';
    const newState = !isCurrentlyEnabled;

    // Toggle the text shadow class on the note text area
    if (newState) {
        noteText.classList.add('Stickara-text-shadow-enabled');
        buttonElement.dataset.textShadow = 'true';
        buttonElement.classList.add('active');
    } else {
        noteText.classList.remove('Stickara-text-shadow-enabled');
        buttonElement.dataset.textShadow = 'false';
        buttonElement.classList.remove('active');
    }

    // Update button state and save
    updateActiveTextShadow(newState); // Update button state (ui.js)
    scheduleSave();
}


/**
 * Copies the plain text content of the note to the clipboard.
 */
function copyNoteText() {
    if (!noteText) return;
    // Use innerText for better representation of visual line breaks in contentEditable
    const textToCopy = noteText.innerText?.trim() || "";
    if (!textToCopy) {
        showCopyFeedback('× Nothing to Copy!', true, 'Copy note text to clipboard'); // Util function
        return;
    }
    navigator.clipboard.writeText(textToCopy).then(() => {
        showCopyFeedback('✅ Copied!', false, 'Copy note text to clipboard'); // Util function
    }).catch(err => {
        console.error('Stickara: Failed to copy note text:', err);
        showCopyFeedback('× Copy Failed!', true, 'Copy note text to clipboard'); // Util function
        alert("Failed to copy text. Your browser might have blocked clipboard access.");
    });
}


/**
 * Handles the click event for the "Insert Image" tool button.
 * Triggers the hidden file input.
 */
function handleImageSelectClick() {
    const fileInput = document.getElementById(IMAGE_INPUT_ID);
    if (fileInput) {
        fileInput.click(); // Open file chooser
    } else {
        console.error("Stickara: Image file input not found.");
    }
}

/**
 * Handles the file selection change event for image uploads.
 * Reads the file and inserts it as a base64 encoded image.
 * @param {Event} event - The change event from the file input.
 */
function handleImageUpload(event) {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function (e) {
            if (e.target?.result) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.classList.add('Stickara-inserted-image');
                img.contentEditable = 'false';
                img.style.maxWidth = '95%'; // Slightly smaller max width
                img.style.maxHeight = '300px';
                img.style.display = 'block';
                img.style.margin = '10px auto'; // Center horizontally
                img.style.borderRadius = '4px'; // Add slight rounding
                insertNodeAtCursor(img); // Utility function (utils.js) - calls scheduleSave
            } else {
                 alert("Stickara: Error reading image file data.");
            }
        };
        reader.onerror = () => {
            alert("Stickara: Error reading image file.");
            console.error("Stickara: FileReader error.");
        };
        reader.readAsDataURL(file);
    } else {
        alert("Please select a valid image file (e.g., JPG, PNG, GIF).");
    }

    // Reset file input to allow selecting the same file again if needed
    if (event.target) {
        event.target.value = null;
    }
}

// --- Note Switching ---

/**
 * Updates the Note Switcher dropdown UI based on which notes exist.
 * **MODIFIED: Supports up to 10 notes. Displays custom title.**
 */
function updateNoteSwitcher() {
    if (!noteSwitcher || !notes || !noteContainer) {
         console.warn("updateNoteSwitcher: Missing elements or notes cache.");
         return;
    }

    const dropdownButton = noteSwitcher.querySelector('.Stickara-dropdown-button');
    if (dropdownButton) {
        const iconHtml = dropdownButton.querySelector('.Stickara-icon')?.outerHTML || '';
        // <<< Use custom title if available for current note >>>
        const displayTitle = notes[currentNoteIndex]?.title || `Note ${currentNoteIndex}`;
        dropdownButton.innerHTML = iconHtml + displayTitle;
    }

    const dropdownContent = noteSwitcher.querySelector('.Stickara-dropdown-content');
    if (!dropdownContent) return;
    dropdownContent.innerHTML = '';

    // --- Loop from 1 to 10 ---
    for (let index = 1; index <= 10; index++) {
        const btn = document.createElement('button');
        btn.dataset.index = String(index); // Use string for dataset
        btn.classList.add('Stickara-note-switch-btn');

        if (notes[index]) {
            // <<< Use custom title if available, else default >>>
            const btnText = notes[index].title || `Note ${index}`;
            btn.textContent = btnText;
            // -------------------------------------------------
            if (index === currentNoteIndex) {
                btn.classList.add('active');
                btn.title = `Currently viewing: ${btnText}`; // Update title too
                btn.setAttribute('aria-current', 'true');
                btn.disabled = true;
            } else {
                btn.title = `Switch to: ${btnText}`; // Update title too
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const targetIndex = parseInt(e.target.closest('button').dataset.index, 10);
                    // console.log(`Switch button clicked for index: ${targetIndex}`); // DEBUG
                    switchToNote(targetIndex, false); // Pass isNew = false
                    closeAllDropdowns();
                });
            }
        } else {
            btn.textContent = `+ Add Note ${index}`; // Text for adding remains the same
            btn.classList.add('add-note');
            btn.title = `Create Note ${index}`;
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const targetIndex = parseInt(e.target.closest('button').dataset.index, 10);
                // console.log(`Add Note button clicked for index: ${targetIndex}`); // DEBUG
                addNewNote(targetIndex);
                closeAllDropdowns();
            });
        }
        dropdownContent.appendChild(btn);
    }
    // --- End Loop ---
}

/**
 * Switches the view to a different note index.
 * Saves the current note first.
 * RESETS the lastTimestampedVideoSrc state variable (UI state).
 * FORCES save if switching to a newly created note.
 * **MODIFIED: Supports up to 10 notes.**
 * @param {number} index - The note index to switch to (1-10).
 * @param {boolean} isNew - Indicates if this is a switch to a newly created note.
 */
function switchToNote(index, isNew = false) {
    // console.log(`%c---> switchToNote START (Index: ${index}, IsNew: ${isNew}, Current: ${currentNoteIndex})`, 'color: blue; font-weight: bold;'); // DEBUG

    if (index === currentNoteIndex) {
        // console.log(`%c   Switch aborted (already on index ${index}).`, 'color: blue;'); // DEBUG
        return;
    }
    // --- Update Bounds Check ---
    if (index < 1 || index > 10 || isNaN(index)) {
        console.warn(`%c   Switch aborted (invalid index ${index}, must be 1-10).`, 'color: orange;'); // DEBUG
        return;
    }
    // --- End Update Bounds Check ---

    // --- Save current note state BEFORE changing index ---
    // console.log(`%c   Saving current note ${currentNoteIndex} before switch...`, 'color: blue;'); // DEBUG
    clearTimeout(saveTimeout);
    saveCurrentNote(); // Force save current note data
    // --- Cleanup UI states ---
    if (typeof isRecording !== 'undefined' && isRecording) stopRecording();
    if (typeof closeFlashcardModal !== 'undefined') closeFlashcardModal();
    if (typeof hideInNoteSearchBar !== 'undefined') hideInNoteSearchBar();
    lastTimestampedVideoSrc = null; // Reset UI state variable
    // console.log(`%c   Cleanup finished.`, 'color: blue;'); // DEBUG

    // --- Update state index ---
    const previousNoteIndex = currentNoteIndex;
    currentNoteIndex = index;
    // console.log(`%c   Global 'currentNoteIndex' updated to: ${currentNoteIndex}.`, 'color: green;'); // DEBUG

    // --- Ensure data exists in cache for the target index ---
    if (!notes[currentNoteIndex]) {
        console.warn(`%c   Note data for index ${currentNoteIndex} missing in cache! Creating default. (isNew was ${isNew})`, 'color: orange;');
        notes[currentNoteIndex] = createDefaultNoteData();
    } else {
        // console.log(`%c   Data for note ${currentNoteIndex} found in cache.`, 'color: blue;');
    }

    // --- Load data into UI ---
    // console.log(`%c   Calling loadCurrentNote for index ${currentNoteIndex}...`, 'color: blue;'); // DEBUG
    try {
        loadCurrentNote(); // This should apply data from notes[currentNoteIndex] to UI
        // console.log(`%c   loadCurrentNote finished successfully.`, 'color: green;'); // DEBUG
    } catch (loadError) {
        console.error(`%c   CRITICAL ERROR during loadCurrentNote for index ${currentNoteIndex}:`, 'color: red;', loadError);
        alert(`Error loading Note ${currentNoteIndex}. Please try reloading the page.`);
        // Attempt to revert index? Or leave it broken? Reverting might be complex.
        // currentNoteIndex = previousNoteIndex; // Risky?
        return; // Stop further processing if load failed
    }


    // --- Update UI elements (Switcher, Save State) ---
    // console.log(`%c   Calling updateNoteSwitcher...`, 'color: blue;'); // DEBUG
    updateNoteSwitcher(); // Update dropdown UI
    // console.log(`%c   Calling saveState (to save new currentNoteIndex)...`, 'color: blue;'); // DEBUG
    saveState(); // Save the new currentNoteIndex and visibility state

    // --- Handle Immediate Save for New Note ---
    if (isNew) {
        // console.log(`%c   Note ${currentNoteIndex} is new. Forcing immediate save of default state...`, 'color: blue; font-weight: bold;'); // DEBUG
        if (!notes[currentNoteIndex] || typeof notes[currentNoteIndex].text === 'undefined') {
             console.error(`%c      [Before Save Check] CRITICAL ERROR: Data for new note ${currentNoteIndex} is missing or invalid! Cannot save.`, 'color: red;', notes[currentNoteIndex]);
        } else {
            // console.log(`%c      [Before Save Check] Data looks okay: Text='${(notes[currentNoteIndex].text || '').substring(0, 20)}...', LastSaved=${notes[currentNoteIndex].lastSaved}`, 'color: blue;');
            clearTimeout(saveTimeout); // Clear just in case
            saveCurrentNote(); // Save the default data immediately
            // console.log(`%c      Immediate save for new note ${currentNoteIndex} completed.`, 'color: green;'); // DEBUG
        }
    } else {
        // console.log(`%c   Note ${currentNoteIndex} is not new. Save will occur later.`, 'color: blue;'); // DEBUG
    }
    // -----------------------------

    // console.log(`%c---> switchToNote END (Switched from ${previousNoteIndex} to ${currentNoteIndex})`, 'color: blue; font-weight: bold;'); // DEBUG
}

/**
 * Adds a new note slot if it doesn't exist, then switches to it.
 * Passes isNew=true to switchToNote.
 * **MODIFIED: Supports up to 10 notes.**
 * @param {number} indexToAdd - The index of the note to add (1-10).
 */
function addNewNote(indexToAdd) {
    // console.log(`%c---> addNewNote START (Index: ${indexToAdd})`, 'color: purple; font-weight: bold;'); // DEBUG
    // --- Update Bounds Check ---
    if (indexToAdd < 1 || indexToAdd > 10 || isNaN(indexToAdd)) {
         console.warn(`%c   Invalid index for addNewNote: ${indexToAdd} (must be 1-10)`, 'color: orange;'); // DEBUG
         return;
    }
    // --- End Update Bounds Check ---

    if (notes[indexToAdd]) {
        // console.log(`%c   Note ${indexToAdd} already exists. Switching instead.`, 'color: purple;'); // DEBUG
        switchToNote(indexToAdd, false); // isNew = false
        return;
    }

    // console.log(`%c   Saving current note ${currentNoteIndex} before adding new one...`, 'color: purple;'); // DEBUG
    clearTimeout(saveTimeout);
    saveCurrentNote();

    // console.log(`%c   Creating default data for Note ${indexToAdd} in cache.`, 'color: purple;'); // DEBUG
    notes[indexToAdd] = createDefaultNoteData();
    // console.log(`%c      Default data created: LastSaved=${notes[indexToAdd].lastSaved}`, 'color: purple;');

    // console.log(`%c   Switching to newly added Note ${indexToAdd} (isNew=true)...`, 'color: purple; font-weight: bold;'); // DEBUG
    switchToNote(indexToAdd, true); // isNew = true

    if (noteContainer && !noteContainer.classList.contains('visible')) {
        // console.log("%c   Note container was hidden, showing it.", 'color: purple;'); // DEBUG
        showNote();
    }
    // console.log(`%c---> addNewNote END (Finished adding Note ${indexToAdd})`, 'color: purple; font-weight: bold;'); // DEBUG
}


// --- Checklist Conversion Handler ---
/**
 * Converts the currently selected text into an interactive checklist
 * using <li> elements with CSS styling and click handling.
 */
function handleConvertSelectionToChecklist() {
    const selection = window.getSelection();

    if (!selection || selection.isCollapsed || selection.rangeCount === 0) {
        alert("Please select the text you want to convert to a checklist."); return;
    }
    const container = selection.anchorNode?.parentElement;
    // Prevent conversion inside Stickara UI itself
    if (container?.closest(`#${NOTE_ID}, .Stickara-diagram-editor, #${FLASHCARD_MODAL_ID}`)) {
        console.log("Stickara Checklist: Selection is inside Stickara UI."); return;
    }
    // Ensure note is visible
    if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
        alert("Please open the Stickara before converting to a checklist."); return;
    }

    const selectedText = selection.toString();
    if (!selectedText.trim()) return;

    // Split selection into lines, trim, and filter out empty lines
    const lines = selectedText.split(/[\r\n]+/)
        .map(line => line.trim())
        .filter(line => line.length > 0);
    if (lines.length === 0) return;

    let listHtml = '<ul class="Stickara-checklist">'; // Add class for styling
    lines.forEach(line => {
        // Basic HTML escaping for the line content using the global escapeHtml function
        const escapedLine = window.escapeHtml(line);
        listHtml += `<li class="Stickara-checklist-item">${escapedLine}</li>`; // Add class for item styling/JS targeting
    });
    listHtml += '</ul>';

    const range = selection.getRangeAt(0);
    // Check if the selection starts within an existing list to avoid extra line breaks
    let needsBreaks = true;
    try {
         const parentElement = range.startContainer.nodeType === Node.ELEMENT_NODE
            ? range.startContainer
            : range.startContainer.parentElement;
        if (parentElement?.closest('ul, ol')) {
            needsBreaks = false;
        }
    } catch (e) { /* Ignore potential errors checking parent */ }

    const contentToInsert = (needsBreaks ? '<br>' : '') + listHtml + (needsBreaks ? '<br>' : '');

    insertHtmlAtCursor(contentToInsert); // Insert the formatted HTML list - calls scheduleSave

    selection.collapseToEnd(); // Deselect text after conversion
    console.log("Stickara: Converted selection to interactive checklist.");
}




// --- START: Screenshot Feature ---

// Assume showStatus is defined (e.g., in content-utils.js or adapted here)
// Basic status function - adapt if you have a more sophisticated UI element
function showStatus(message, type = 'info', duration = 3000) {
    // Check for a specific status element, otherwise log
    const statusElId = 'Stickara-status-bar'; // Example ID, adjust if needed
    let statusEl = document.getElementById(statusElId);

    // If status element doesn't exist, try to create a simple one
    if (!statusEl) {
        statusEl = document.createElement('div');
        statusEl.id = statusElId;
        // Basic styling (you'll want more specific CSS)
        statusEl.style.position = 'fixed';
        statusEl.style.bottom = '10px';
        statusEl.style.left = '10px';
        statusEl.style.padding = '8px 15px';
        statusEl.style.borderRadius = '4px';
        statusEl.style.backgroundColor = '#333';
        statusEl.style.color = '#fff';
        statusEl.style.zIndex = '2147483646'; // Below note maybe?
        statusEl.style.opacity = '0';
        statusEl.style.transition = 'opacity 0.3s ease-in-out';
        statusEl.style.fontSize = '13px';
        document.body.appendChild(statusEl);
    }

    statusEl.textContent = message;
    statusEl.style.backgroundColor = type === 'error' ? '#d9534f' : (type === 'success' ? '#5cb85c' : '#337ab7'); // Adjust colors
    statusEl.style.opacity = '1'; // Make visible

    // Clear previous timeout if any
    if (statusEl.dataset.timeoutId) {
        clearTimeout(parseInt(statusEl.dataset.timeoutId));
    }

    // Set new timeout to hide
    const timeoutId = setTimeout(() => {
        statusEl.style.opacity = '0';
        // Optionally remove the element after fade out if it was dynamically created?
        // Or just clear text: statusEl.textContent = '';
    }, duration);
    statusEl.dataset.timeoutId = String(timeoutId); // Store timeout ID

}


// --- START: INSERTED/REPLACED CODE ---
/**
 * Handles the click event for the "Screenshot" tool button.
 * Gathers options from inline checkboxes and calls capturePageScreenshot.
 */
function handleCaptureScreenshot() {
    console.log("Stickara Screenshot: Capture button clicked");

    // Close all open dropdowns before taking screenshot to ensure clean capture
    if (typeof closeAllDropdowns === 'function') {
        closeAllDropdowns();
        console.log("Stickara Screenshot: Closed all dropdowns for clean capture");
    }

    // Check if selected area mode is enabled via checkbox
    const selectedAreaCheckbox = document.getElementById('Stickara-screenshot-option-selected-area');
    const selectedAreaEnabled = selectedAreaCheckbox?.checked ?? false;

    if (selectedAreaEnabled) {
        // Start area selection mode
        startAreaSelection();
    } else {
        // Use existing screenshot logic
        captureScreenshotWithCheckboxOptions();
    }
}

function captureScreenshotWithCheckboxOptions() {
    // Check if the core capture function exists
    if (typeof capturePageScreenshot !== 'function') {
        console.error("Stickara Error: capturePageScreenshot function not found.");
        alert("Error: Screenshot feature is not available.");
        return;
    }

    // --- Get options from checkboxes ---
    // Use the defined IDs from content-ui.js (ASSUMED TO EXIST)
    const downloadCheckbox = document.getElementById('Stickara-screenshot-option-download');
    const hideUiCheckbox = document.getElementById('Stickara-screenshot-option-hide-ui');
    const fullPageCheckbox = document.getElementById('Stickara-screenshot-option-fullpage'); // <<< GET NEW CHECKBOX

    // Build options object, defaulting to false if element not found
    const options = {
        // captureMode: 'visible', // REMOVED: Set based on checkbox below
        actionAfterCapture: (downloadCheckbox?.checked ?? false) ? 'download' : 'annotate',
        hideStickara: hideUiCheckbox?.checked ?? false,
        // --- ADDED: Set captureMode based on checkbox ---
        captureMode: (fullPageCheckbox?.checked ?? false) ? 'full' : 'visible',
        quality: 100 // Add quality parameter for high quality
    };
    // --- End gathering options ---

    console.log("Stickara Screenshot: Initiating capture with options:", options);
    capturePageScreenshot(options); // Call the function that messages the background
}


/**
 * Initiates the screenshot capture process by messaging the background script.
 * Sends options gathered from UI or defaults.
 * @param {object} options - Configuration for the screenshot.
 * @param {'visible' | 'full'} [options.captureMode='visible'] - Area to capture ('visible' implemented).
 * @param {'annotate' | 'download'} [options.actionAfterCapture='annotate'] - What to do after capture.
 */
function capturePageScreenshot(options = {}) {
    // Default options (used if options object is incomplete)
    const defaultOptions = {
        captureMode: 'visible',
        actionAfterCapture: 'annotate'
    };
    // Ensure finalOptions has all properties, prioritizing passed options
    const finalOptions = { ...defaultOptions, ...options };

    console.log(`SB Screenshot: Requesting screenshot from background with options:`, finalOptions);
    showStatus('Capturing screenshot...', 'info');

    chrome.runtime.sendMessage({
        action: 'captureScreenshotWithOptions',
        options: finalOptions
    }, (response) => {
        // --- Handle response (same logic as before) ---
        if (chrome.runtime.lastError) {
            console.error("SB Screenshot: Error sending capture request:", chrome.runtime.lastError.message);
            showStatus(`Capture failed: ${chrome.runtime.lastError.message}`, 'error'); return;
        }
        if (response?.success) {
            if (finalOptions.actionAfterCapture === 'annotate') {
                if (response.imageDataUrl) {
                    showStatus('Capture complete. Opening editor...', 'success');
                    // Ensure the annotation modal function exists before calling
                    if (typeof openScreenshotAnnotationModal === 'function') {
                         openScreenshotAnnotationModal(response.imageDataUrl);
                    } else {
                         console.error("Stickara Error: openScreenshotAnnotationModal function not found.");
                         showStatus("Error: Could not open editor.", 'error');
                    }
                } else {
                    console.error("SB Screenshot: Annotation requested but no image data received.");
                    showStatus('Capture failed: No image data for annotation.', 'error');
                 }
            } else if (finalOptions.actionAfterCapture === 'download') {
                 console.log("SB Screenshot: Direct download initiated by background script.");
                 showStatus('Capture complete. Download started (check browser downloads).', 'success');
            } else {
                 // Success response but unexpected combination
                 console.log("SB Screenshot: Capture request processed by background.", response.message || '(No message)');
                 showStatus(response.message || 'Capture request processed successfully.', 'info');
            }
        } else { // response.success is false or response is missing
             console.error("SB Screenshot: Failed to capture screenshot.", response?.error || '(No error details)');
             showStatus(`Capture failed: ${response?.error || 'Unknown background error'}`, 'error');
        }
        // --- End response handling ---
    });
}
// --- END: INSERTED/REPLACED CODE ---

// --- END: Screenshot Feature ---


// Update Load Log
console.log("Stickara: Interactions Logic Loaded (Screenshot Options includes Hide Note)");

// --- Resizing Handlers ---

/**
 * Starts the resizing operation for the note container.
 * @param {MouseEvent} e - The mousedown event on a resize handle.
 */
function startResize(e) {
    if (!e.target.classList.contains('Stickara-resizer')) return;
    e.preventDefault();
    e.stopPropagation(); // Prevent triggering drag

    isResizing = true;
    resizeDirection = e.target.dataset.direction;
    resizeStartX = e.clientX;
    resizeStartY = e.clientY;

    const rect = noteContainer.getBoundingClientRect();
    initialWidth = rect.width;
    initialHeight = rect.height;
    // Use page coordinates for positioning calculations (relative to viewport if fixed, doc if absolute)
    initialRectLeft = rect.left; // Position relative to viewport
    initialRectTop = rect.top;  // Position relative to viewport

    document.addEventListener('mousemove', resizeNote);
    document.addEventListener('mouseup', stopResize);
    document.body.style.cursor = getComputedStyle(e.target).cursor; // Set body cursor based on handle
    document.body.style.userSelect = 'none'; // Prevent selection
}

/**
 * Handles the note container resizing movement.
 * @param {MouseEvent} e - The mousemove event.
 */
function resizeNote(e) {
    if (!isResizing || !noteContainer) return;
    e.preventDefault();

    const dx = e.clientX - resizeStartX;
    const dy = e.clientY - resizeStartY;

    let newWidth = initialWidth;
    let newHeight = initialHeight;
    let newTop = initialRectTop;
    let newLeft = initialRectLeft;

    const minWidth = parseInt(window.getComputedStyle(noteContainer).minWidth) || 150;
    const minHeight = parseInt(window.getComputedStyle(noteContainer).minHeight) || 100;

    // Calculate new dimensions/positions based on direction
    if (resizeDirection.includes('right')) {
        newWidth = Math.max(minWidth, initialWidth + dx);
    }
    if (resizeDirection.includes('bottom')) {
        newHeight = Math.max(minHeight, initialHeight + dy);
    }
    if (resizeDirection.includes('left')) {
        const potentialWidth = initialWidth - dx;
        if (potentialWidth >= minWidth) {
            newWidth = potentialWidth;
            newLeft = initialRectLeft + dx;
        } else {
            newWidth = minWidth;
            newLeft = initialRectLeft + (initialWidth - minWidth);
        }
    }
    if (resizeDirection.includes('top')) {
        const potentialHeight = initialHeight - dy;
        if (potentialHeight >= minHeight) {
            newHeight = potentialHeight;
            newTop = initialRectTop + dy;
        } else {
            newHeight = minHeight;
            newTop = initialRectTop + (initialHeight - minHeight);
        }
    }

    // Apply styles
    noteContainer.style.width = newWidth + 'px';
    noteContainer.style.height = newHeight + 'px';

    // Update position only if top or left handles are involved
    // Position is always relative to viewport because we use getBoundingClientRect
    // and apply styles directly. If the note is absolute positioned, the browser handles the offset.
    if (resizeDirection.includes('top')) {
         noteContainer.style.top = newTop + 'px';
         noteContainer.style.bottom = 'auto'; // Ensure top takes precedence
    }
    if (resizeDirection.includes('left')) {
        noteContainer.style.left = newLeft + 'px';
        noteContainer.style.right = 'auto'; // Ensure left takes precedence
    }

    // Important: Trigger manual recalculation if needed, as ResizeObserver might lag
    if (typeof calculateTextAreaHeight === 'function') {
        requestAnimationFrame(calculateTextAreaHeight); // Use rAF for smoother updates
    }
}

/**
 * Stops the resizing operation.
 */
function stopResize() {
    if (!isResizing) return;
    isResizing = false;

    document.removeEventListener('mousemove', resizeNote);
    document.removeEventListener('mouseup', stopResize);
    document.body.style.cursor = ''; // Reset body cursor
    document.body.style.userSelect = '';

    // Save the final size/position
    scheduleSave();
}

// --- Event Listeners Setup ---

/**
 * Initializes all event listeners for note interactions.
 */
function initInteractions() {
    if (!noteContainer || !noteHeader || !noteText) {
        console.error("Stickara: Cannot initialize interactions, core elements missing.");
        return;
    }

    // Prevent setup if already initialized
    if (noteContainer.dataset.interactionsInitialized === 'true') {
        console.warn("Stickara: Interactions already initialized.");
        return;
    }

    // Dragging
    if (noteHeader) {
        noteHeader.addEventListener('mousedown', startDrag);
        // Add mouseup listener to the document to catch drag end even if cursor leaves the header
        document.addEventListener('mouseup', stopDrag);
    } else {
        console.warn("Stickara: Note header not found, cannot attach drag listener.");
    }

    // Add mousedown listener for resize handles (using event delegation on container)
    noteContainer.addEventListener('mousedown', startResize);

    // Close dropdowns on clicks outside
    document.addEventListener('click', closeAllDropdowns);

    // Formatting toolbar and general note clicks/input
    if (noteText) {
        // Focus tracking for context menu
        noteText.addEventListener('focus', () => { /* console.log('Note focused'); */ });
        noteText.addEventListener('blur', () => { /* console.log('Note blurred'); */ });

        // Handle clicks for specific actions like timestamp links
        noteText.addEventListener('click', handlePotentialActionClick);

        // Update formatting button states on selection change or input
        document.addEventListener('selectionchange', handleFormattingState); // Use document for broader selection tracking
        noteText.addEventListener('input', () => { handleFormattingState(); scheduleSave(); calculateTextAreaHeight(); });
        noteText.addEventListener('keyup', handleFormattingState); // Handle keyup as well for arrow keys etc.
        noteText.addEventListener('mouseup', handleFormattingState); // Handle mouseup after selection

        // --- START ADDITION: Handle Enter key in empty blockquote/pre ---
        noteText.addEventListener('keydown', (e) => {
            // Handle Escape key to exit block formats
            if (e.key === 'Escape') {
                handleEscapeKeyFormatting(e);
                return;
            }

            // Handle Enter key formatting preservation
            if (e.key === 'Enter' && !e.shiftKey) {
                handleEnterKeyFormatting(e);
            }

            if (e.key === 'Enter' && !e.shiftKey) { // Check for Enter key without Shift
                const selection = window.getSelection();
                if (!selection || selection.rangeCount === 0) return;

                const range = selection.getRangeAt(0);
                const container = range.commonAncestorContainer;

                // Find the nearest block-level parent element (blockquote or pre)
                let blockElement = null;
                if (container.nodeType === Node.ELEMENT_NODE) {
                    blockElement = container.closest('blockquote, pre');
                } else if (container.parentElement) {
                    blockElement = container.parentElement.closest('blockquote, pre');
                }

                if (blockElement && noteText.contains(blockElement)) {
                    // Check if the block is empty or contains only whitespace/placeholder br
                    const content = blockElement.textContent.trim();
                    const html = blockElement.innerHTML.trim().toLowerCase();

                    // Check if block is effectively empty (no text, or just <br>, or empty paragraph within blockquote)
                    const isEmpty = content === '' || html === '<br>' || html === '<p><br></p>' || html === '<p></p>';

                    if (isEmpty) {
                        e.preventDefault(); // Stop Enter from creating a newline inside the block

                        // Option 1: Change the block to a paragraph (simpler)
                        // This command works well for blockquote, might need adjustment for pre if issues arise
                         try {
                            document.execCommand('formatBlock', false, 'P'); // Change to paragraph
                            // Ensure focus remains or moves correctly if needed
                            requestAnimationFrame(() => noteText.focus());
                        } catch (err) {
                             console.error("Stickara: Error changing block format:", err);
                             // Fallback or alternative method might be needed here
                        }

                        // Option 2: Manually create and insert a new paragraph (more complex, potentially more robust)
                        // const newPara = document.createElement('p');
                        // // Add a zero-width space or <br> to make it selectable/editable
                        // newPara.innerHTML = '<br>'; // Browsers often need a <br> in empty contenteditables
                        // blockElement.parentNode.insertBefore(newPara, blockElement.nextSibling);
                        // // Move selection to the new paragraph
                        // const newRange = document.createRange();
                        // newRange.setStart(newPara, 0);
                        // newRange.collapse(true);
                        // selection.removeAllRanges();
                        // selection.addRange(newRange);

                        handleFormattingState(); // Update button states
                        scheduleSave(); // Save the change
                    }
                }
            }
        });
        // --- END ADDITION ---

        // Other event listeners for note interactions
        if (typeof handleImagePaste === 'function') noteText.addEventListener('paste', handleImagePaste); else console.warn("handleImagePaste missing.");

        // Keyboard event listeners (using bubbling on specific elements)
        noteText.addEventListener('keydown', (e) => {
            // In-note Search (Ctrl+F / Cmd+F)
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault(); // Prevent browser default find
                if (typeof showInNoteSearchBar === 'function') showInNoteSearchBar();
                return; // Stop further processing
            }

            // Handle Escape key for search bar
            if (e.key === 'Escape') {
                if (typeof inNoteSearchActive !== 'undefined' && inNoteSearchActive && typeof hideInNoteSearchBar === 'function') {
                     hideInNoteSearchBar();
                     e.stopPropagation(); // Prevent closing other things
                     return;
                }
            }

            // Table Keyboard Shortcuts - Only work when cursor is inside a table cell
            if (handleTableKeyboardShortcuts(e)) {
                return; // Shortcut was handled, stop further processing
            }

            // Allow default behavior for most keys within the text area
            // The window-level capture listener handles preventing youtube shortcuts
            // console.log(`NoteText Keydown: ${e.key}`); // Debug

        }, true); // Use CAPTURE phase for this handler to intercept events earlier

    } else {
        console.warn("Stickara: Note text area not found, cannot attach text interaction listeners.");
    }

    // Initialize ResizeObserver (safe check)
    if (typeof initResizeObserver === 'function') initResizeObserver(); else console.warn("initResizeObserver function missing.");

    // Mark as initialized
    noteContainer.dataset.interactionsInitialized = 'true';

    console.log("Stickara: Note interactions initialized.");
}

// --- Initialization Call ---

// Attempt to initialize interactions once the DOM is ready
// This assumes noteContainer might be created asynchronously
let interactionInitAttempts = 0;
const maxInteractionInitAttempts = 10;

function attemptInteractionInitialization() {
    if (noteContainer && noteContainer.dataset.interactionsInitialized !== 'true') {
        initInteractions();
    } else if (interactionInitAttempts < maxInteractionInitAttempts) {
        interactionInitAttempts++;
        setTimeout(attemptInteractionInitialization, 500); // Retry after 500ms
    } else if (!noteContainer) {
         console.error("Stickara: Failed to find noteContainer after multiple attempts. Interactions not initialized.");
    } else {
        // Already initialized or max attempts reached
         // console.log("Stickara: Interaction initialization already done or max attempts reached.");
    }
}

// Start the initialization attempts when the script loads
if (document.readyState === 'complete' || document.readyState === 'interactive') {
     attemptInteractionInitialization();
} else {
     document.addEventListener('DOMContentLoaded', attemptInteractionInitialization);
}

/**
 * Handles paste events into the note text area.
 * If the pasted content contains an image, it reads it and inserts it.
 * @param {ClipboardEvent} event - The paste event.
 */
function handleImagePaste(event) {
    if (!noteText || !noteText.isContentEditable) return;

    const items = event.clipboardData?.items;
    if (!items) return;

    let foundImage = false;
    for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
            const blob = items[i].getAsFile();
            if (!blob) continue;

            foundImage = true;
            event.preventDefault(); // Prevent default paste action for the image

            const reader = new FileReader();
            reader.onload = function (e) {
                if (e.target?.result) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.classList.add('Stickara-inserted-image');
                    img.contentEditable = 'false';
                    img.style.maxWidth = '95%';
                    img.style.maxHeight = '300px';
                    img.style.display = 'block';
                    img.style.margin = '10px auto';
                    img.style.borderRadius = '4px';

                    if (typeof insertNodeAtCursor === 'function') {
                        insertNodeAtCursor(img); // Calls scheduleSave
                    } else {
                        console.error("Stickara: insertNodeAtCursor function missing, cannot insert pasted image.");
                        alert("Error inserting pasted image.");
                    }
                } else {
                    alert("Stickara: Error reading pasted image data.");
                }
            };
            reader.onerror = () => {
                alert("Stickara: Error reading pasted image file blob.");
                console.error("Stickara: FileReader error on pasted image blob.");
            };
            reader.readAsDataURL(blob);

            break; // Handle only the first image found
        }
    }

    // If an image was found and handled, we already prevented default.
    // If no image was found, the default paste action (for text etc.) will proceed.
}

/**
 * Handle keyboard shortcuts for table manipulation
 * Only works when cursor is inside a table cell
 * @param {KeyboardEvent} e - The keyboard event
 * @returns {boolean} - True if shortcut was handled, false otherwise
 */
function handleTableKeyboardShortcuts(e) {
    // Check if cursor is inside a table cell
    const currentCell = getCurrentTableCell();
    if (!currentCell) return false;

    const table = currentCell.closest('table');
    if (!table) return false;

    // Define keyboard shortcuts
    const shortcuts = {
        // Ctrl + Shift + R: Add row below current row
        addRow: e.ctrlKey && e.shiftKey && e.key === 'R',
        // Ctrl + Shift + C: Add column to the right of current column
        addColumn: e.ctrlKey && e.shiftKey && e.key === 'C',
        // Ctrl + Shift + Delete: Remove current row (with confirmation)
        removeRow: e.ctrlKey && e.shiftKey && e.key === 'Delete',
        // Ctrl + Alt + Delete: Remove current column (with confirmation)
        removeColumn: e.ctrlKey && e.altKey && e.key === 'Delete'
    };

    // Handle shortcuts
    if (shortcuts.addRow) {
        e.preventDefault();
        e.stopPropagation();
        handleTableAddRowShortcut(table, currentCell);
        return true;
    }

    if (shortcuts.addColumn) {
        e.preventDefault();
        e.stopPropagation();
        handleTableAddColumnShortcut(table, currentCell);
        return true;
    }

    if (shortcuts.removeRow) {
        e.preventDefault();
        e.stopPropagation();
        handleTableRemoveRowShortcut(table, currentCell);
        return true;
    }

    if (shortcuts.removeColumn) {
        e.preventDefault();
        e.stopPropagation();
        handleTableRemoveColumnShortcut(table, currentCell);
        return true;
    }

    return false; // No shortcut was handled
}

/**
 * Get the current table cell where the cursor is positioned
 * @returns {HTMLElement|null} - The current table cell or null
 */
function getCurrentTableCell() {
    const selection = window.getSelection();
    if (!selection.rangeCount) return null;

    const range = selection.getRangeAt(0);
    let node = range.startContainer;

    // If the node is a text node, get its parent element
    if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement;
    }

    // Find the closest table cell (td or th)
    return node.closest('td, th');
}

/**
 * Handle Ctrl+Shift+R: Add row below current row
 */
function handleTableAddRowShortcut(table, currentCell) {
    if (typeof addTableRowInNote === 'function') {
        addTableRowInNote(table);
        showTableShortcutNotification('Row added below current row! (Ctrl+Shift+R)', 'success');
    } else {
        console.error('addTableRowInNote function not available');
        showTableShortcutNotification('Error: Table function not available', 'error');
    }
}

/**
 * Handle Ctrl+Shift+C: Add column to the right of current column
 */
function handleTableAddColumnShortcut(table, currentCell) {
    if (typeof addTableColumnInNote === 'function') {
        addTableColumnInNote(table);
        showTableShortcutNotification('Column added to the right! (Ctrl+Shift+C)', 'success');
    } else {
        console.error('addTableColumnInNote function not available');
        showTableShortcutNotification('Error: Table function not available', 'error');
    }
}

/**
 * Handle Ctrl+Shift+Delete: Remove current row with confirmation
 */
function handleTableRemoveRowShortcut(table, currentCell) {
    const tbody = table.querySelector('tbody') || table;
    const rows = tbody.querySelectorAll('tr');

    if (rows.length <= 1) {
        showTableShortcutNotification('Cannot remove the last row!', 'warning');
        return;
    }

    // Show confirmation
    if (confirm('Are you sure you want to remove this row?\n\nShortcut: Ctrl+Shift+Delete')) {
        if (typeof removeTableRowInNote === 'function') {
            removeTableRowInNote(table);
            showTableShortcutNotification('Row removed! (Ctrl+Shift+Delete)', 'success');
        } else {
            console.error('removeTableRowInNote function not available');
            showTableShortcutNotification('Error: Table function not available', 'error');
        }
    }
}

/**
 * Handle Ctrl+Alt+Delete: Remove current column with confirmation
 */
function handleTableRemoveColumnShortcut(table, currentCell) {
    const firstRow = table.querySelector('tr');
    if (!firstRow || firstRow.children.length <= 1) {
        showTableShortcutNotification('Cannot remove the last column!', 'warning');
        return;
    }

    // Show confirmation
    if (confirm('Are you sure you want to remove this column?\n\nShortcut: Ctrl+Alt+Delete')) {
        if (typeof removeTableColumnInNote === 'function') {
            removeTableColumnInNote(table);
            showTableShortcutNotification('Column removed! (Ctrl+Alt+Delete)', 'success');
        } else {
            console.error('removeTableColumnInNote function not available');
            showTableShortcutNotification('Error: Table function not available', 'error');
        }
    }
}

/**
 * Show notification for table keyboard shortcuts
 */
function showTableShortcutNotification(message, type = 'info') {
    // Use existing notification system if available
    if (typeof showTableNotificationInNote === 'function') {
        showTableNotificationInNote(message, type);
    } else if (typeof showStatus === 'function') {
        showStatus(message, type);
    } else {
        // Fallback to console
        console.log(`Table Shortcut: ${message}`);
    }
}

console.log("Stickara: Note interactions with table keyboard shortcuts loaded.");

// --- END OF FILE content-interactions.js ---
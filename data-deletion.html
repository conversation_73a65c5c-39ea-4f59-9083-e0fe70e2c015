<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Data Deletion Policy</title>
    <link rel="stylesheet" href="data-deletion.css">
</head>
<body>
    <h1>Stickara Data Deletion Policy</h1>
    
    <p>This document explains how you can delete your data from Stickara. We believe in giving you complete control over your information.</p>
    
    <div class="section">
        <h2>Local Data Deletion</h2>
        
        <p>Most of your Stickara data is stored locally on your device. Here's how to delete it:</p>
        
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Open the Stickara popup by clicking the extension icon in your browser toolbar.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Click the "Clear Page Data" button to delete notes and highlights for the current page.
            </div>
            <div class="step">
                <span class="step-number">3.</span> To delete all data, go to Chrome's extension management page:
                <ul>
                    <li>Enter <code>chrome://extensions</code> in your address bar</li>
                    <li>Find Stickara and click "Details"</li>
                    <li>Scroll down and click "Clear Data"</li>
                </ul>
            </div>
        </div>
        
        <div class="note">
            <strong>Note:</strong> Clearing local data is permanent and cannot be undone unless you have enabled Google Drive sync.
        </div>
    </div>
    
    <div class="section">
        <h2>Google Drive Data Deletion</h2>
        
        <p>If you've enabled Google Drive sync, your data is also stored in your Google Drive account. Here's how to delete it:</p>
        
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Open the Stickara popup by clicking the extension icon in your browser toolbar.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Click the "Disconnect" button in the Google Drive sync section to stop syncing.
            </div>
            <div class="step">
                <span class="step-number">3.</span> To delete the synced data from Google Drive:
                <ul>
                    <li>Go to <a href="https://drive.google.com" target="_blank">Google Drive</a></li>
                    <li>Navigate to the "Stickara" folder (or the custom folder name you specified)</li>
                    <li>Right-click the folder and select "Remove"</li>
                </ul>
            </div>
        </div>
        
        <div class="note">
            <strong>Note:</strong> If you've enabled the "Auto-Delete" feature in the screenshot settings, your screenshots will be automatically deleted from Google Drive after the specified period.
        </div>
    </div>
    
    <div class="section">
        <h2>Google Calendar Data Deletion</h2>
        
        <p>If you've used the reminder feature with Google Calendar integration, here's how to delete those events:</p>
        
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Go to <a href="https://calendar.google.com" target="_blank">Google Calendar</a>.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Find events created by Stickara (they will have "Stickara Reminder" in the title).
            </div>
            <div class="step">
                <span class="step-number">3.</span> Click on each event and select "Delete" to remove it.
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>API Keys Deletion</h2>
        
        <p>If you've entered API keys for speech recognition services, here's how to remove them:</p>
        
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Open the Stickara popup by clicking the extension icon in your browser toolbar.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Click on "Settings" and then "Voice Settings".
            </div>
            <div class="step">
                <span class="step-number">3.</span> Delete any API keys you've entered and click "Save Settings".
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>Uninstalling the Extension</h2>
        
        <p>Uninstalling Stickara will remove all local data associated with the extension:</p>
        
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Right-click the Stickara icon in your browser toolbar.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Select "Remove from Chrome..." (or your browser's equivalent).
            </div>
            <div class="step">
                <span class="step-number">3.</span> Confirm the uninstallation when prompted.
            </div>
        </div>
        
        <div class="note">
            <strong>Important:</strong> If you've enabled Google Drive sync, uninstalling the extension will not delete data from your Google Drive. Follow the Google Drive data deletion steps above to remove that data.
        </div>
    </div>
    
    <p>If you have any questions about data deletion or need assistance, please contact us at [Your Contact Information].</p>
    
    <p class="last-updated">Last updated: June 2023</p>
</body>
</html>

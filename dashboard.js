// --- START OF FILE dashboard.js ---
document.addEventListener('DOMContentLoaded', () => {
    console.log("Dashboard: DOMContentLoaded event fired. Initializing script...");

    // URL utilities are no longer needed - using direct URL handling



    // --- Get DOM Element References ---
    // Tab navigation
    const notesTabBtn = document.getElementById('notes-tab-btn');
    const highlightsTabBtn = document.getElementById('highlights-tab-btn');
    const notesTabContent = document.getElementById('notes-tab-content');
    const highlightsTabContent = document.getElementById('highlights-tab-content');

    // Notes tab elements
    const resultsDiv = document.getElementById('dashboard-results');
    const statusMessage = document.getElementById('dashboard-status-message');
    const searchInput = document.getElementById('dashboard-search-query');
    // --- REINSTATED Checkbox References ---
    const reminderFilterCheckbox = document.getElementById('dashboard-filter-reminder');
    const globalPinFilterCheckbox = document.getElementById('dashboard-filter-global');
    // --- END REINSTATED Checkbox References ---
    const sideBySideViewBtn = document.getElementById('side-by-side-view-btn');
    const sortBySelect = document.getElementById('dashboard-sort-by');
    const bulkActionsBar = document.getElementById('bulk-actions-bar');
    const selectionCountSpan = document.getElementById('selection-count');
    const bulkDeleteButton = document.getElementById('bulk-delete-selected');
    const bulkExportButton = document.getElementById('bulk-export-selected');
    const bulkExportFormatSelect = document.getElementById('bulk-export-format-select');
    const bulkMoveDropdownButton = document.getElementById('bulk-move-btn'); // Get bulk move button
    const bulkMoveOptionsDiv = document.getElementById('bulk-move-options'); // Get move options container
    const notesArea = document.getElementById('notes-area');

    // Notes pagination elements
    const prevPageBtn = document.getElementById('prev-page-btn');
    const nextPageBtn = document.getElementById('next-page-btn');
    const pageIndicator = document.getElementById('page-indicator');
    const pageSizeSelect = document.getElementById('page-size-select');
    const notesCountDisplay = document.getElementById('notes-count-display');

    // Notes select all checkbox
    const selectAllCheckbox = document.getElementById('select-all-checkbox');

    // Highlights tab elements
    const highlightsResultsDiv = document.getElementById('highlights-results');
    const highlightsSearchInput = document.getElementById('highlights-search-query');
    const highlightColorFilter = document.getElementById('highlight-color-filter');
    const highlightStyleFilter = document.getElementById('highlight-style-filter');
    const highlightNotesFilter = document.getElementById('highlight-notes-filter');
    const highlightsSortBySelect = document.getElementById('highlights-sort-by');
    const highlightsBulkActionsBar = document.getElementById('highlights-bulk-actions-bar');
    const highlightsSelectionCountSpan = document.getElementById('highlights-selection-count');
    const bulkDeleteHighlightsButton = document.getElementById('bulk-delete-highlights');
    const bulkExportHighlightsButton = document.getElementById('bulk-export-highlights');
    const highlightsExportFormatSelect = document.getElementById('highlights-export-format-select');
    const highlightsMoveDropdownButton = document.getElementById('highlights-move-btn'); // Get highlights move button
    const highlightsMoveOptionsDiv = document.getElementById('highlights-move-options'); // Get highlights move options container
    const highlightsArea = document.getElementById('highlights-area');

    // Highlights pagination elements
    const highlightsPrevPageBtn = document.getElementById('highlights-prev-page-btn');
    const highlightsNextPageBtn = document.getElementById('highlights-next-page-btn');
    const highlightsPageIndicator = document.getElementById('highlights-page-indicator');
    const highlightsPageSizeSelect = document.getElementById('highlights-page-size-select');
    const highlightsCountDisplay = document.getElementById('highlights-count-display');

    // Highlights select all checkbox
    const selectAllHighlightsCheckbox = document.getElementById('select-all-highlights-checkbox');


    // --- Notebook Elements ---
    const notebookSidebar = document.querySelector('.notebook-sidebar');
    const notebookListUl = document.getElementById('notebook-list');
    const addNotebookBtn = document.getElementById('add-notebook-btn');

    // --- Rename Modal Elements ---
    const renameModal = document.getElementById('rename-modal');
    const renameInput = document.getElementById('rename-notebook-input');
    const renameIdInput = document.getElementById('rename-notebook-id');
    const renameSaveButton = document.getElementById('rename-notebook-save');
    const modalCloseButton = document.querySelector('#rename-modal .close-button');

    // --- Drop Target Overlay ---
    const dropOverlay = document.getElementById('drop-overlay');

    // --- Content View Modal Elements ---
    const contentViewModal = document.getElementById('content-view-modal');
    const contentViewTitle = document.getElementById('content-view-title');
    const contentViewTypeIcon = document.getElementById('content-view-type-icon');
    const contentViewUrl = document.getElementById('content-view-url');
    const contentViewDetails = document.getElementById('content-view-details');
    const contentViewBody = document.getElementById('content-view-body');
    const contentViewOpenPageBtn = document.getElementById('content-view-open-page');
    const contentViewCloseBtn = document.getElementById('content-view-close-btn');
    const contentViewCopyBtn = document.getElementById('content-view-copy-btn');
    const contentViewEditBtn = document.getElementById('content-view-edit-btn');
    const contentViewZoomIn = document.getElementById('content-view-zoom-in');
    const contentViewZoomOut = document.getElementById('content-view-zoom-out');
    const contentViewZoomLevel = document.getElementById('content-view-zoom-level');
    const contentViewPrevBtn = document.getElementById('content-view-prev');
    const contentViewNextBtn = document.getElementById('content-view-next');

    // Content view state
    let currentZoomLevel = 100;
    let currentViewingNoteKey = null; // Tracks currently viewed note in modal
    let currentViewingHighlightId = null; // Tracks currently viewed highlight in modal

    /** Apply the current zoom level to the content view body */
    function applyZoomLevel() {
        // Remove all existing zoom classes
        contentViewBody.className = contentViewBody.className.replace(/\bzoom-\d+\b/g, '');

        // Add the current zoom class
        contentViewBody.classList.add(`zoom-${currentZoomLevel}`);

        // Remove any inline font-size style that might override our CSS
        contentViewBody.style.fontSize = '';
    }

    // --- Critical Element Check ---
    if (!resultsDiv || !notebookListUl || !notebookSidebar) {
        console.error("Dashboard CRITICAL ERROR: A required HTML element ('dashboard-results', 'notebook-list', or '.notebook-sidebar') was not found. Stopping initialization.");
        document.body.innerHTML = '<p style="color: red; font-family: sans-serif; padding: 20px;">Dashboard Error: Required HTML elements are missing. Cannot load notes.</p>';
        return;
    }

    /**
     * Updates the dashboard header statistics
     */
    function updateDashboardStats() {
        const totalNotesElement = document.getElementById('total-notes-count');
        const totalHighlightsElement = document.getElementById('total-highlights-count');
        const totalNotebooksElement = document.getElementById('total-notebooks-count');

        if (totalNotesElement) {
            totalNotesElement.textContent = allNotesData.length;
        }

        if (totalHighlightsElement) {
            totalHighlightsElement.textContent = allHighlightsData.length;
        }

        if (totalNotebooksElement) {
            // Count actual notebooks (excluding default "All" and "Ungrouped")
            const notebookCount = Array.isArray(notebooks) ? notebooks.length : 0;
            totalNotebooksElement.textContent = notebookCount;
        }
    }

    // --- Enhanced Element Validation with Search Focus ---
    if (!statusMessage) console.warn("Dashboard Warning: Status message element ('dashboard-status-message') not found.");

    // Critical search input validation
    if (!searchInput) {
        console.error("Dashboard CRITICAL: Notes search input element ('dashboard-search-query') not found - search functionality will not work!");
    }

    if (!highlightsSearchInput) {
        console.error("Dashboard CRITICAL: Highlights search input element ('highlights-search-query') not found - highlights search functionality will not work!");
    }

    // --- REINSTATED Checkbox Warnings ---
    if (!reminderFilterCheckbox) console.warn("Dashboard Warning: Reminder filter checkbox ('dashboard-filter-reminder') not found.");
    if (!globalPinFilterCheckbox) console.warn("Dashboard Warning: Global pin filter checkbox ('dashboard-filter-global') not found.");
    // --- END REINSTATED Checkbox Warnings ---
    if (!sideBySideViewBtn) console.warn("Dashboard Warning: Side-by-side view button ('side-by-side-view-btn') not found.");
    if (!sortBySelect) console.warn("Dashboard Warning: Sort select element ('dashboard-sort-by') not found.");
    // Log other missing elements (addNotebookBtn, rename modal, bulk actions, bulk move, drop overlay)
    if (!addNotebookBtn) console.warn("Dashboard Warning: Add notebook button ('add-notebook-btn') not found.");
    if (!renameModal || !renameInput || !renameIdInput || !renameSaveButton || !modalCloseButton) console.warn("Dashboard Warning: One or more rename modal elements missing.");
    if (!bulkActionsBar || !selectionCountSpan || !bulkDeleteButton || !bulkExportButton || !bulkExportFormatSelect || !bulkMoveDropdownButton || !bulkMoveOptionsDiv) console.warn("Dashboard Warning: One or more bulk action elements not found.");
    if (!dropOverlay) console.warn("Dashboard Warning: Drop overlay element ('drop-overlay') not found.");


    // --- Constants & State ---
    const STORAGE_KEY_PREFIX = 'Stickara_note_';
    const HIGHLIGHT_KEY_PREFIX = 'Stickara_highlights_';
    const NOTEBOOKS_STORAGE_KEY = 'Stickara_notebooks';

    // Notes state
    let allNotesData = []; // Holds {key, url, decodedUrl, index, notebookId, plainText, data: {...fullNoteData}}
    let notebooks = []; // Holds {id, name, _lastUpdated}
    let currentNotebookFilter = null; // null = All, 'UNGROUPED', or notebookId
    let selectedNoteKeys = new Set();
    let debounceTimer = null;
    let draggedNoteKey = null;
    let isSideBySideView = false; // Track if side-by-side view is active

    // Notes pagination state
    let currentPage = 1;
    let pageSize = 15; // Default notes per page
    let totalPages = 1;

    // Highlights state
    let allHighlightsData = []; // Holds {key, url, decodedUrl, highlightId, text, color, style, timestamp, data: {...fullHighlightData}}
    let selectedHighlightIds = new Set();
    let highlightsDebounceTimer = null;
    let draggedHighlightId = null; // Track the currently dragged highlight

    // Highlights pagination state
    let highlightsCurrentPage = 1;
    let highlightsPageSize = 15; // Default highlights per page
    let highlightsTotalPages = 1;

    // Active tab state
    let activeTab = 'notes'; // 'notes' or 'highlights' - tracks current active tab

    // --- Utility Functions ---

    /**
     * Safely escapes HTML special characters to prevent XSS attacks
     * @param {string} text - The text to escape
     * @returns {string} - The escaped text safe for insertion into HTML
     */
    function escapeHtml(text) {
        if (!text) return '';
        return String(text)
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    /** Displays status message */
    function showStatus(message, type = 'info', duration = 3000) {
        if (!statusMessage) return;
        console.log(`Dashboard Status (${type}): ${message}`);
        statusMessage.textContent = message;
        statusMessage.className = 'visible ' + type;
        statusMessage.style.opacity = '1';
        if (statusMessage.timeoutId) clearTimeout(statusMessage.timeoutId);
        if (statusMessage.resetTimeoutId) clearTimeout(statusMessage.resetTimeoutId);
        statusMessage.timeoutId = setTimeout(() => {
            statusMessage.style.opacity = '0';
            statusMessage.resetTimeoutId = setTimeout(() => {
                // Only clear if the message hasn't changed in the meantime
                if (statusMessage.textContent === message) { statusMessage.textContent = ''; statusMessage.className = ''; }
                statusMessage.timeoutId = null; statusMessage.resetTimeoutId = null;
            }, 300); // Match CSS transition duration
        }, duration);
    }

    /**
     * Converts HTML to Plain Text with enhanced security and image handling
     * This function is designed to safely extract plain text from potentially unsafe HTML
     * @param {string} html - The HTML content to convert
     * @param {string} format - Output format ('txt', 'md', or 'html')
     * @returns {string} - Safe plain text representation
     */
    function getPlainText(html, format = 'txt') {
         if (!html) return '';
         try {
             // Create a temporary div in a detached DOM
             const tempDiv = document.createElement('div');

             // SECURITY: Sanitize the HTML before processing
             // First replace <script> tags and other potentially dangerous elements
             const sanitizedHtml = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                                      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
                                      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
                                      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
                                      .replace(/javascript:/gi, 'removed:')
                                      .replace(/on\w+(\s*)=/gi, 'data-removed$1=');

             // Replace <br> tags with newlines *before* inserting into div
             const processedHtml = sanitizedHtml.replace(/<br\s*\/?>/gi, '\n');

             // Set the innerHTML with the sanitized content
             tempDiv.innerHTML = processedHtml;

             // Handle equation spans before processing other elements
             const equationSpans = tempDiv.querySelectorAll('.Stickara-equation, .Stickara-equation-span');
             equationSpans.forEach(span => {
                 const latex = span.getAttribute('data-latex');
                 if (latex) {
                     // Replace the rendered equation with the original LaTeX
                     const latexText = document.createTextNode(`[${latex}]`);
                     span.parentNode.replaceChild(latexText, span);
                 } else {
                     // If no data-latex, try to extract a simple representation
                     const equationText = span.textContent || span.innerText || '[Equation]';
                     const simpleText = document.createTextNode(equationText);
                     span.parentNode.replaceChild(simpleText, span);
                 }
             });

             // Remove any remaining script elements that might have been nested
             const scriptElements = tempDiv.querySelectorAll('script, iframe, object, embed');
             scriptElements.forEach(el => el.parentNode.removeChild(el));

             // Remove event handlers from all elements
             const allElements = tempDiv.querySelectorAll('*');
             allElements.forEach(el => {
                 // Remove all attributes that start with "on" (event handlers)
                 Array.from(el.attributes).forEach(attr => {
                     if (attr.name.startsWith('on') || attr.name.includes('javascript:')) {
                         el.removeAttribute(attr.name);
                     }
                 });
             });

             // Handle images based on format
             if (format === 'md') {
                 // For Markdown, replace images with Markdown image syntax
                 tempDiv.querySelectorAll('img').forEach((img, index) => {
                     const alt = img.alt || `Image ${index + 1}`;
                     const src = img.src || '#';
                     const mdImage = document.createElement('span');

                     // For data URLs (embedded images), we need to handle them specially
                     if (src.startsWith('data:image/')) {
                         // For Markdown export, we'll keep the data URL as is
                         // This ensures the image data is preserved in the Markdown file
                         mdImage.textContent = `\n\n![${alt}](${src})\n\n`;
                     } else {
                         // For external URLs, use standard Markdown image syntax
                         mdImage.textContent = `\n\n![${alt}](${src})\n\n`;
                     }

                     img.parentNode.replaceChild(mdImage, img);
                 });
             } else if (format === 'txt') {
                 // For plain text, replace images with descriptive text
                 tempDiv.querySelectorAll('img').forEach((img, index) => {
                     const alt = img.alt || `Image ${index + 1}`;
                     const placeholder = document.createElement('span');
                     placeholder.textContent = `\n\n[IMAGE: ${alt}]\n\n`;
                     img.parentNode.replaceChild(placeholder, img);
                 });
             }
             // Note: For HTML format, we don't modify images here as we'll use the original HTML

             // Add newlines after block elements for better separation
             tempDiv.querySelectorAll('p, li, h1, h2, h3, h4, h5, h6, div, blockquote, pre').forEach(el => {
                 const content = el.textContent || '';
                 // Add newline only if content exists and doesn't end with whitespace
                 if (content.length > 0 && !/\s$/.test(content)) {
                     el.append('\n');
                 }
             });

             // Extract the text content
             let text = tempDiv.textContent || tempDiv.innerText || "";

             // Clean up whitespace
             text = text.replace(/ +/g, ' '); // Collapse multiple spaces
             text = text.replace(/\n\s*\n/g, '\n\n'); // Collapse multiple newlines
             text = text.trim(); // Remove leading/trailing whitespace

             return text;
         } catch (e) {
             console.error("Dashboard Error in getPlainText:", e);
             return "[Error extracting text]"; // Return error string
         }
     }

    /**
     * Process equations in modal content for proper display
     * @param {HTMLElement} container - The container element to process
     */
    function processEquationsInModal(container) {
        if (!container) return;

        // Find all equation spans in the modal content
        const equationSpans = container.querySelectorAll('.Stickara-equation, .Stickara-equation-span');

        equationSpans.forEach(span => {
            const latex = span.getAttribute('data-latex');
            if (latex) {
                // Check if KaTeX is available for rendering
                if (typeof katex !== 'undefined') {
                    try {
                        // Clear the span and re-render with KaTeX
                        span.innerHTML = '';
                        katex.render(latex, span, {
                            throwOnError: false,
                            displayMode: false,
                            output: 'html',
                            strict: false
                        });

                        // Apply styling to make equations look good in modal
                        span.style.fontSize = 'inherit';
                        span.style.lineHeight = 'inherit';
                        span.style.color = 'inherit';
                        span.style.fontFamily = 'inherit';
                        span.style.display = 'inline';
                        span.style.verticalAlign = 'baseline';
                        span.style.margin = '0 2px';
                        span.style.padding = '1px 3px';
                        span.style.background = 'rgba(0, 0, 0, 0.05)';
                        span.style.borderRadius = '2px';
                        span.style.border = '1px solid rgba(0, 0, 0, 0.1)';

                    } catch (e) {
                        console.warn('Failed to render equation in modal:', e);
                        // Fallback to showing the LaTeX code
                        span.textContent = `[${latex}]`;
                    }
                } else {
                    // KaTeX not available, show LaTeX code
                    span.textContent = `[${latex}]`;
                }
            }
        });
    }



    /** Parses URL and index from note storage key */
     function parseNoteKey(key) {
         if (!key || !key.startsWith(STORAGE_KEY_PREFIX)) return null;

         // Handle global notes first
         if (key.includes('global_note')) {
             const indexMatch = key.match(/global_note(\d+)$/);
             if (indexMatch && indexMatch[1]) {
                 return {
                     url: null,
                     index: parseInt(indexMatch[1], 10),
                     isGlobal: true
                 };
             }
             console.warn(`Dashboard: Global note key format invalid: ${key}`);
             return null;
         }

         // For URL-specific notes, use simple parsing like highlights do
         // This ensures consistency with the new storage approach

         const keySuffix = key.substring(STORAGE_KEY_PREFIX.length);

         // Special case for snippet notes
         if (keySuffix.startsWith('snippet_')) {
             // For snippet notes, use the timestamp as both URL and index
             const snippetId = keySuffix.substring(8); // Remove 'snippet_' prefix
             const snippetTimestamp = parseInt(snippetId, 10);
             if (!isNaN(snippetTimestamp)) {
                 return {
                     url: `snippet_${snippetId}`,
                     index: 1, // Use 1 as default index for snippets
                     isSnippet: true
                 };
             }
         }

         // Special case for price comparison notes
         if (keySuffix.includes('_price_')) {
             // Extract URL and product name
             const priceParts = keySuffix.split('_price_');
             if (priceParts.length === 2) {
                 const url = priceParts[0];
                 const productName = priceParts[1];

                 // Handle the new safer format where % is replaced with _pct_
                 let decodedUrl = url;
                 let decodedProductName = productName;

                 try {
                     // Try to convert _pct_ back to % for decoding
                     if (url.includes('_pct_')) {
                         const urlWithPercent = url.replace(/_pct_/g, '%');
                         // Validate percent-encoding pattern before decoding
                         const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
                         if (validPercentPattern.test(urlWithPercent)) {
                             decodedUrl = decodeURIComponent(urlWithPercent);
                         } else {
                             console.warn(`Invalid percent-encoding pattern in price comparison URL: ${urlWithPercent}`);
                             decodedUrl = url; // Keep original if invalid
                         }
                     } else if (url.includes('%')) {
                         const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
                         if (validPercentPattern.test(url)) {
                             decodedUrl = decodeURIComponent(url);
                         } else {
                             console.warn(`Invalid percent-encoding pattern in URL: ${url}`);
                             decodedUrl = url; // Keep original if invalid
                         }
                     } else {
                         decodedUrl = url; // No percent encoding
                     }

                     if (productName.includes('_pct_')) {
                         const productNameWithPercent = productName.replace(/_pct_/g, '%');
                         const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
                         if (validPercentPattern.test(productNameWithPercent)) {
                             decodedProductName = decodeURIComponent(productNameWithPercent);
                         } else {
                             console.warn(`Invalid percent-encoding pattern in product name: ${productNameWithPercent}`);
                             decodedProductName = productName; // Keep original if invalid
                         }
                     } else if (productName.includes('%')) {
                         const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
                         if (validPercentPattern.test(productName)) {
                             decodedProductName = decodeURIComponent(productName);
                         } else {
                             console.warn(`Invalid percent-encoding pattern in product name: ${productName}`);
                             decodedProductName = productName; // Keep original if invalid
                         }
                     } else {
                         decodedProductName = productName; // No percent encoding
                     }
                 } catch (e) {
                     console.warn(`Failed to decode price comparison URL or product name: ${e.message}`);
                     // Keep the original values if decoding fails
                     decodedUrl = url;
                     decodedProductName = productName;
                 }

                 return {
                     url: decodedUrl,
                     index: 1, // Use 1 as default index for price comparison notes
                     isPriceComparison: true,
                     productName: decodedProductName
                 };
             }
         }

         // Find the *last* occurrence of '_note' to handle URLs that might contain it
         const lastNoteMarker = keySuffix.lastIndexOf('_note');
         if (lastNoteMarker === -1 || lastNoteMarker === 0) return null; // '_note' not found or at the beginning

         const url = keySuffix.substring(0, lastNoteMarker);
         const noteIndexStr = keySuffix.substring(lastNoteMarker + 5); // Length of '_note' is 5

         // Parse the note index
         const index = parseInt(noteIndexStr, 10);
         if (isNaN(index)) {
             console.warn(`Dashboard: Invalid note index in key: ${key}`);
             return null;
         }

         // Check for valid URL
         if (!url || url.length === 0) {
             console.warn(`Dashboard: Empty URL in key: ${key}`);
             return null;
         }

         // Return the URL directly without complex denormalization
         // This matches the new storage approach that preserves URLs exactly
         return {
             url: url,
             index: index,
             isGlobal: false
         };
    }

    /** Opens the content view modal for a note */
    function openNoteInModal(noteKey) {
        console.log(`Dashboard: Opening note in modal: ${noteKey}`);

        // Store current viewing note key
        currentViewingNoteKey = noteKey;
        currentViewingHighlightId = null;

        // Update URL hash for deep linking
        updateUrlHash();

        // Reset zoom level
        currentZoomLevel = 100;
        contentViewZoomLevel.textContent = '100%';
        applyZoomLevel();

        // Find the note in our data
        const noteData = allNotesData.find(n => n.key === noteKey);
        if (!noteData) {
            console.error(`Note with key ${noteKey} not found in data.`);
            showStatus(`Error: Note not found.`, 'error');
            return;
        }

        // Set type indicator
        contentViewTypeIcon.textContent = '📝';

        // Set modal title
        contentViewTitle.textContent = noteData.data.title || `Note ${noteData.index}`;

        // Process URL for display
        let displayUrl = noteData.decodedUrl || '[No URL]';

        // If URL is very long, truncate it for display
        if (displayUrl.length > 60 && displayUrl !== '[No URL]') {
            try {
                const urlObj = new URL(displayUrl);
                const domain = urlObj.hostname;
                const path = urlObj.pathname;

                // Create a shortened version with domain + truncated path
                if (path.length > 30) {
                    displayUrl = domain + path.substring(0, 27) + '...';
                } else {
                    displayUrl = domain + path;
                }
            } catch (e) {
                // If URL parsing fails, just truncate the string
                displayUrl = displayUrl.substring(0, 57) + '...';
            }
        }

        // Set URL
        let urlHtml = '';
        if (noteData.decodedUrl) {
            urlHtml = `<a href="#" id="content-view-url-link" title="${escapeHtml(noteData.decodedUrl)}">${escapeHtml(displayUrl)}</a>`;
        } else {
            urlHtml = '<span>[No URL]</span>';
        }
        contentViewUrl.innerHTML = urlHtml;

        // Add click event to URL link
        const urlLink = document.getElementById('content-view-url-link');
        if (urlLink) {
            urlLink.addEventListener('click', (e) => {
                e.preventDefault();
                openNoteInTab(noteData.url, noteData.index);
                closeContentViewModal();
            });
        }

        // Set details
        let detailsHtml = '';

        // Add notebook info
        const notebook = notebooks.find(nb => nb.id === noteData.notebookId);
        const notebookName = notebook ? notebook.name : 'Ungrouped';
        detailsHtml += `<span class="detail-notebook">📓 ${escapeHtml(notebookName)}</span>`;

        // Add tags if available
        if (noteData.data.tags && noteData.data.tags.length > 0) {
            detailsHtml += `<span class="detail-tags">🏷️ ${noteData.data.tags.map(tag => escapeHtml(tag)).join(', ')}</span>`;
        }

        // Add saved date
        if (noteData.data.lastSaved) {
            const savedDate = new Date(noteData.data.lastSaved);
            if (!isNaN(savedDate.getTime())) {
                detailsHtml += `<span class="detail-date">🕒 ${savedDate.toLocaleString()}</span>`;
            }
        }

        contentViewDetails.innerHTML = detailsHtml;

        // Set content
        contentViewBody.innerHTML = `<div class="note-content">${noteData.data.text || '[No content]'}</div>`;

        // Process equations in the modal content for proper display
        processEquationsInModal(contentViewBody);

        // Set up button actions

        // Open page button
        contentViewOpenPageBtn.onclick = () => {
            openNoteInTab(noteData.url, noteData.index);
            closeContentViewModal();
        };

        // Copy button
        contentViewCopyBtn.onclick = () => {
            const textToCopy = noteData.plainText || getPlainText(noteData.data.text || '');
            navigator.clipboard.writeText(textToCopy)
                .then(() => {
                    showStatus('Note content copied to clipboard', 'success');
                    // Visual feedback
                    const originalText = contentViewCopyBtn.textContent;
                    contentViewCopyBtn.textContent = '✅';
                    setTimeout(() => {
                        contentViewCopyBtn.textContent = originalText;
                    }, 1500);
                })
                .catch(err => {
                    console.error('Failed to copy text: ', err);
                    showStatus('Failed to copy content', 'error');
                });
        };

        // Edit button
        contentViewEditBtn.onclick = () => {
            // Open the note in the original page for editing
            openNoteInTab(noteData.url, noteData.index);
            closeContentViewModal();
            showStatus('Opening note for editing...', 'info');
        };

        // Zoom controls
        contentViewZoomIn.onclick = () => {
            const zoomLevels = [75, 80, 90, 100, 110, 125, 150, 175, 200];
            const currentIndex = zoomLevels.indexOf(currentZoomLevel);
            if (currentIndex < zoomLevels.length - 1) {
                currentZoomLevel = zoomLevels[currentIndex + 1];
                contentViewZoomLevel.textContent = `${currentZoomLevel}%`;
                applyZoomLevel();
            }
        };

        contentViewZoomOut.onclick = () => {
            const zoomLevels = [75, 80, 90, 100, 110, 125, 150, 175, 200];
            const currentIndex = zoomLevels.indexOf(currentZoomLevel);
            if (currentIndex > 0) {
                currentZoomLevel = zoomLevels[currentIndex - 1];
                contentViewZoomLevel.textContent = `${currentZoomLevel}%`;
                applyZoomLevel();
            }
        };

        // Navigation buttons

        // Find the index of the current note in the filtered notes
        const filteredNotes = getFilteredNotes();
        const currentNoteIndex = filteredNotes.findIndex(n => n.key === noteKey);

        // Previous button
        contentViewPrevBtn.disabled = currentNoteIndex <= 0;
        contentViewPrevBtn.onclick = () => {
            if (currentNoteIndex > 0) {
                const prevNote = filteredNotes[currentNoteIndex - 1];
                openNoteInModal(prevNote.key);
            }
        };

        // Next button
        contentViewNextBtn.disabled = currentNoteIndex >= filteredNotes.length - 1;
        contentViewNextBtn.onclick = () => {
            if (currentNoteIndex < filteredNotes.length - 1) {
                const nextNote = filteredNotes[currentNoteIndex + 1];
                openNoteInModal(nextNote.key);
            }
        };

        // Show the modal with animation
        contentViewModal.style.display = 'flex';

        // Add event listener to close button
        contentViewCloseBtn.onclick = closeContentViewModal;

        // Add event listener to close on Escape key
        document.addEventListener('keydown', handleContentViewModalKeydown);

        // Add event listener to close when clicking outside
        contentViewModal.addEventListener('click', handleContentViewModalOutsideClick);
    }

    /** Helper function to get currently filtered notes */
    function getFilteredNotes() {
        // Get filter values
        const searchQuery = searchInput ? searchInput.value.trim().toLowerCase() : '';
        const reminderFilter = reminderFilterCheckbox ? reminderFilterCheckbox.checked : false;
        const globalPinFilter = globalPinFilterCheckbox ? globalPinFilterCheckbox.checked : false;

        // Filter notes
        return allNotesData.filter(note => {
            // Notebook filter
            if (currentNotebookFilter === 'UNGROUPED') {
                // Show only ungrouped notes
                if (note.notebookId) return false;
            } else if (currentNotebookFilter) {
                // Show only notes in the selected notebook
                if (note.notebookId !== currentNotebookFilter) return false;
            }

            // Search filter
            if (searchQuery) {
                const matchesTitle = note.data.title && note.data.title.toLowerCase().includes(searchQuery);
                const matchesUrl = note.decodedUrl && note.decodedUrl.toLowerCase().includes(searchQuery);
                const matchesContent = note.plainText && note.plainText.toLowerCase().includes(searchQuery);
                const matchesTags = note.data.tags && note.data.tags.some(tag => tag.toLowerCase().includes(searchQuery));

                if (!(matchesTitle || matchesUrl || matchesContent || matchesTags)) {
                    return false;
                }
            }

            // Reminder filter
            if (reminderFilter && !note.data.reminder) {
                return false;
            }

            // Global pin filter
            if (globalPinFilter && !note.data.globallyPinned) {
                return false;
            }

            return true;
        });
    }

    /** Opens the content view modal for a highlight */
    function openHighlightInModal(highlightId) {
        console.log(`Dashboard: Opening highlight in modal: ${highlightId}`);

        // Store current viewing highlight ID
        currentViewingHighlightId = highlightId;
        currentViewingNoteKey = null;

        // Update URL hash for deep linking
        updateUrlHash();

        // Reset zoom level
        currentZoomLevel = 100;
        contentViewZoomLevel.textContent = '100%';
        applyZoomLevel();

        // Find the highlight in our data
        const highlightData = allHighlightsData.find(h => h.highlightId === highlightId);
        if (!highlightData) {
            console.error(`Highlight with ID ${highlightId} not found in data.`);
            showStatus(`Error: Highlight not found.`, 'error');
            return;
        }

        // Set type indicator based on highlight style
        if (highlightData.style === 'color') {
            switch (highlightData.color) {
                case 'yellow':
                    contentViewTypeIcon.textContent = '🟨';
                    break;
                case 'green':
                    contentViewTypeIcon.textContent = '🟩';
                    break;
                case 'blue':
                    contentViewTypeIcon.textContent = '🟦';
                    break;
                case 'pink':
                    contentViewTypeIcon.textContent = '🟪';
                    break;
                case 'purple':
                    contentViewTypeIcon.textContent = '🟪';
                    break;
                default:
                    contentViewTypeIcon.textContent = '🔍';
            }
        } else if (highlightData.style === 'underline') {
            contentViewTypeIcon.textContent = '📏';
        } else if (highlightData.style === 'wavy') {
            contentViewTypeIcon.textContent = '〰️';
        } else {
            contentViewTypeIcon.textContent = '🔍';
        }

        // Process URL for display
        let displayUrl = highlightData.decodedUrl || '[Unknown Page]';

        // If URL is very long, truncate it for display
        if (displayUrl.length > 60) {
            const urlObj = new URL(displayUrl);
            const domain = urlObj.hostname;
            const path = urlObj.pathname;

            // Create a shortened version with domain + truncated path
            if (path.length > 30) {
                displayUrl = domain + path.substring(0, 27) + '...';
            }
        }

        // Set modal title
        contentViewTitle.textContent = `Highlight from ${displayUrl}`;

        // Set URL
        let urlHtml = '';
        if (highlightData.decodedUrl) {
            urlHtml = `<a href="#" id="content-view-url-link" title="${escapeHtml(highlightData.decodedUrl)}">${escapeHtml(displayUrl)}</a>`;
        } else {
            urlHtml = '<span>[No URL]</span>';
        }
        contentViewUrl.innerHTML = urlHtml;

        // Add click event to URL link
        const urlLink = document.getElementById('content-view-url-link');
        if (urlLink) {
            urlLink.addEventListener('click', (e) => {
                e.preventDefault();
                openHighlightInTab(highlightData);
                closeContentViewModal();
            });
        }

        // Set details
        let detailsHtml = '';

        // Add highlight style/color info with icon
        if (highlightData.style === 'color') {
            let colorIcon = '⬜';
            switch (highlightData.color) {
                case 'yellow':
                    colorIcon = '🟨';
                    break;
                case 'green':
                    colorIcon = '🟩';
                    break;
                case 'blue':
                    colorIcon = '🟦';
                    break;
                case 'pink':
                case 'purple':
                    colorIcon = '🟪';
                    break;
            }
            detailsHtml += `<span class="detail-style">${colorIcon} ${highlightData.color.charAt(0).toUpperCase() + highlightData.color.slice(1)}</span>`;
        } else {
            const styleIcon = highlightData.style === 'underline' ? '📏' : '〰️';
            detailsHtml += `<span class="detail-style">${styleIcon} ${highlightData.style.charAt(0).toUpperCase() + highlightData.style.slice(1)}</span>`;
        }

        // Add notebook info if available
        if (highlightData.notebookId) {
            const notebook = notebooks.find(nb => nb.id === highlightData.notebookId);
            if (notebook) {
                detailsHtml += `<span class="detail-notebook">📓 ${escapeHtml(notebook.name)}</span>`;
            }
        } else {
            detailsHtml += `<span class="detail-notebook">📓 Ungrouped</span>`;
        }

        // Add date
        if (highlightData.timestamp) {
            const date = new Date(highlightData.timestamp);
            if (!isNaN(date.getTime())) {
                detailsHtml += `<span class="detail-date">🕒 ${date.toLocaleString()}</span>`;
            }
        }

        contentViewDetails.innerHTML = detailsHtml;

        // Set content
        let contentHtml = '';

        // Add highlight text with enhanced styling
        contentHtml += `<p class="highlight-text ${highlightData.style === 'color' ? `color-${highlightData.color}` : `style-${highlightData.style}`}">${escapeHtml(highlightData.text)}</p>`;

        // Add note text if available
        if (highlightData.noteText) {
            contentHtml += `<div class="highlight-note">
                <h4>📝 Note</h4>
                <div class="note-content">${escapeHtml(highlightData.noteText)}</div>
            </div>`;
        }

        contentViewBody.innerHTML = contentHtml;

        // Set up button actions

        // Open page button
        contentViewOpenPageBtn.onclick = () => {
            openHighlightInTab(highlightData);
            closeContentViewModal();
        };

        // Copy button
        contentViewCopyBtn.onclick = () => {
            const textToCopy = highlightData.text + (highlightData.noteText ? `\n\nNote: ${highlightData.noteText}` : '');
            navigator.clipboard.writeText(textToCopy)
                .then(() => {
                    showStatus('Highlight content copied to clipboard', 'success');
                    // Visual feedback
                    const originalText = contentViewCopyBtn.textContent;
                    contentViewCopyBtn.textContent = '✅';
                    setTimeout(() => {
                        contentViewCopyBtn.textContent = originalText;
                    }, 1500);
                })
                .catch(err => {
                    console.error('Failed to copy text: ', err);
                    showStatus('Failed to copy content', 'error');
                });
        };

        // Edit button - disable for highlights as they can't be edited directly
        contentViewEditBtn.disabled = true;
        contentViewEditBtn.style.opacity = '0.5';
        contentViewEditBtn.title = 'Highlights cannot be edited directly';

        // Zoom controls
        contentViewZoomIn.onclick = () => {
            const zoomLevels = [75, 80, 90, 100, 110, 125, 150, 175, 200];
            const currentIndex = zoomLevels.indexOf(currentZoomLevel);
            if (currentIndex < zoomLevels.length - 1) {
                currentZoomLevel = zoomLevels[currentIndex + 1];
                contentViewZoomLevel.textContent = `${currentZoomLevel}%`;
                applyZoomLevel();
            }
        };

        contentViewZoomOut.onclick = () => {
            const zoomLevels = [75, 80, 90, 100, 110, 125, 150, 175, 200];
            const currentIndex = zoomLevels.indexOf(currentZoomLevel);
            if (currentIndex > 0) {
                currentZoomLevel = zoomLevels[currentIndex - 1];
                contentViewZoomLevel.textContent = `${currentZoomLevel}%`;
                applyZoomLevel();
            }
        };

        // Navigation buttons

        // Find the index of the current highlight in the filtered highlights
        const filteredHighlights = getFilteredHighlights();
        const currentHighlightIndex = filteredHighlights.findIndex(h => h.highlightId === highlightId);

        // Debug info for navigation (can be removed in production)
        console.log(`Highlight Navigation: ${currentHighlightIndex + 1} of ${filteredHighlights.length}`);

        // Previous button
        contentViewPrevBtn.disabled = currentHighlightIndex <= 0;
        contentViewPrevBtn.onclick = () => {
            if (currentHighlightIndex > 0) {
                const prevHighlight = filteredHighlights[currentHighlightIndex - 1];
                openHighlightInModal(prevHighlight.highlightId);
            }
        };

        // Next button
        contentViewNextBtn.disabled = currentHighlightIndex >= filteredHighlights.length - 1;
        contentViewNextBtn.onclick = () => {
            if (currentHighlightIndex < filteredHighlights.length - 1) {
                const nextHighlight = filteredHighlights[currentHighlightIndex + 1];
                openHighlightInModal(nextHighlight.highlightId);
            }
        };

        // Show the modal with animation
        contentViewModal.style.display = 'flex';

        // Add event listener to close button
        contentViewCloseBtn.onclick = closeContentViewModal;

        // Add event listener to close on Escape key
        document.addEventListener('keydown', handleContentViewModalKeydown);

        // Add event listener to close when clicking outside
        contentViewModal.addEventListener('click', handleContentViewModalOutsideClick);
    }

    /** Helper function to get currently filtered highlights */
    function getFilteredHighlights() {
        // Get filter values
        const searchQuery = highlightsSearchInput ? highlightsSearchInput.value.trim().toLowerCase() : '';
        const colorFilter = highlightColorFilter ? highlightColorFilter.value : 'all';
        const styleFilter = highlightStyleFilter ? highlightStyleFilter.value : 'all';
        const notesFilter = highlightNotesFilter ? highlightNotesFilter.value : 'all';

        // Filter highlights
        return allHighlightsData.filter(highlight => {
            // Text search (including notes)
            if (searchQuery &&
                !highlight.text.toLowerCase().includes(searchQuery) &&
                !highlight.decodedUrl.toLowerCase().includes(searchQuery) &&
                !(highlight.noteText && highlight.noteText.toLowerCase().includes(searchQuery))) {
                return false;
            }

            // Color filter
            if (colorFilter !== 'all' && highlight.color !== colorFilter) {
                return false;
            }

            // Style filter
            if (styleFilter !== 'all' && highlight.style !== styleFilter) {
                return false;
            }

            // Notes filter
            if (notesFilter === 'with-notes' && !highlight.noteText) {
                return false;
            }

            // Notebook filter
            if (currentNotebookFilter) {
                if (currentNotebookFilter === 'UNGROUPED') {
                    // Show only highlights without a notebook
                    if (highlight.notebookId) {
                        return false;
                    }
                } else if (currentNotebookFilter !== 'ALL') {
                    // Show only highlights in the selected notebook
                    if (highlight.notebookId !== currentNotebookFilter) {
                        return false;
                    }
                }
            }

            return true;
        });
    }

    /** Closes the content view modal */
    function closeContentViewModal() {
        // Add closing animation
        contentViewModal.classList.add('closing');

        // Wait for animation to complete before hiding
        setTimeout(() => {
            contentViewModal.style.display = 'none';
            contentViewModal.classList.remove('closing');

            // Reset state
            currentViewingNoteKey = null;
            currentViewingHighlightId = null;
            currentZoomLevel = 100;

            // Update URL hash to remove content viewing parameters
            updateUrlHash();

            // Remove event listeners
            document.removeEventListener('keydown', handleContentViewModalKeydown);
            contentViewModal.removeEventListener('click', handleContentViewModalOutsideClick);

            // Clear content
            contentViewTitle.textContent = 'View Content';
            contentViewTypeIcon.textContent = '📝';
            contentViewUrl.innerHTML = '';
            contentViewDetails.innerHTML = '';
            contentViewBody.innerHTML = '';
            contentViewZoomLevel.textContent = '100%';

            // Reset button states
            contentViewEditBtn.disabled = false;
            contentViewEditBtn.style.opacity = '1';
            contentViewEditBtn.title = 'Edit note';
            contentViewCopyBtn.textContent = '📋';
            contentViewPrevBtn.disabled = false;
            contentViewNextBtn.disabled = false;
        }, 200); // Match the CSS animation duration
    }

    /** Handle keydown events for the content view modal */
    function handleContentViewModalKeydown(e) {
        if (e.key === 'Escape') {
            closeContentViewModal();
        } else if (e.key === 'ArrowLeft' && !contentViewPrevBtn.disabled) {
            // Navigate to previous item with left arrow key
            contentViewPrevBtn.click();
        } else if (e.key === 'ArrowRight' && !contentViewNextBtn.disabled) {
            // Navigate to next item with right arrow key
            contentViewNextBtn.click();
        } else if (e.key === '+' || e.key === '=') {
            // Zoom in with + key
            contentViewZoomIn.click();
        } else if (e.key === '-') {
            // Zoom out with - key
            contentViewZoomOut.click();
        } else if (e.key === 'c' && (e.ctrlKey || e.metaKey)) {
            // Copy content with Ctrl+C or Cmd+C
            contentViewCopyBtn.click();
        }
    }

    /** Handle clicks outside the content view modal */
    function handleContentViewModalOutsideClick(e) {
        if (e.target === contentViewModal) {
            closeContentViewModal();
        }
    }

    // URL fixing functions removed - using direct URL handling

    /** Opens note URL in a tab */
     function openNoteInTab(url, noteIndex) {
         console.log(`Dashboard: Request to open URL: ${url} for Note ${noteIndex}`);

         // Handle snippet notes
         if (url.startsWith('snippet_')) {
             showStatus(`Snippet notes cannot be opened in a tab.`, 'info');
             return;
         }

         // With the new storage approach, URLs are stored in raw format
         // so we can use them directly without complex processing
         let targetUrl = url;

         // Basic validation - ensure URL has a protocol
         if (!targetUrl.includes('://')) {
             targetUrl = 'http://' + targetUrl;
             console.log(`Added http:// prefix to URL: ${targetUrl}`);
         }

         console.log(`Using URL: ${targetUrl}`);

         chrome.tabs.query({ url: targetUrl }, (existingTabs) => {
             if (chrome.runtime.lastError) {
                 console.error(`Error querying tabs for ${targetUrl}: ${chrome.runtime.lastError.message}`);
                 showStatus(`Error finding tab: ${chrome.runtime.lastError.message}. Trying to create...`, 'error');
                 // Attempt to create tab even if query failed
                 chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                    if (chrome.runtime.lastError || !newTab) showStatus(`Failed to create tab after query error: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');
                 });
                 return;
             }

             if (existingTabs && existingTabs.length > 0) {
                 const targetTab = existingTabs[0];
                 console.log(`Found existing tab ID ${targetTab.id} for ${targetUrl}. Activating.`);
                 // Update the tab to be active and focus its window
                 chrome.tabs.update(targetTab.id, { active: true }, (updatedTab) => {
                     if (chrome.runtime.lastError || !updatedTab) {
                         console.error(`Error activating tab ${targetTab.id}: ${chrome.runtime.lastError?.message}`);
                         showStatus(`Error switching to tab. Trying to create...`, 'error');
                         chrome.tabs.create({ url: targetUrl, active: true }); // Fallback to creating
                     } else {
                         // Also focus the window containing the tab
                         chrome.windows.update(updatedTab.windowId, { focused: true });
                         console.log(`Tab ${updatedTab.id} activated and window ${updatedTab.windowId} focused.`);
                     }
                 });
             } else {
                 console.log(`No existing tab found for ${targetUrl}. Creating new tab.`);
                 chrome.tabs.create({ url: targetUrl, active: true }, (newTab) => {
                    if (chrome.runtime.lastError || !newTab) {
                        console.error(`Error creating new tab for ${targetUrl}: ${chrome.runtime.lastError?.message}`);
                        showStatus(`Failed to create tab: ${chrome.runtime.lastError?.message || 'Unknown error'}`, 'error');
                    } else {
                        console.log(`Created new tab ID ${newTab.id} for ${targetUrl}`);
                    }
                 });
             }
         });
     }

     // Legacy URL processing function removed - using direct URL handling

    // --- Notebook Management Functions ---

    /** Generates a unique ID */
     function generateNotebookId() {
         return `nb-${Date.now()}-${Math.random().toString(16).slice(2, 8)}`;
     }

     /** Loads notebooks from storage */
     async function loadNotebooks() {
        console.log("Dashboard: Loading notebooks...");
        try {
            const result = await chrome.storage.local.get([NOTEBOOKS_STORAGE_KEY]);
            let loadedNotebooks = result[NOTEBOOKS_STORAGE_KEY];

            // Ensure it's an array, default to empty if not found or invalid
            if (!Array.isArray(loadedNotebooks)) {
                console.warn("Notebook data in storage was not an array. Resetting to empty.");
                loadedNotebooks = [];
            }

            // Get the list of deleted notebook IDs if it exists
            const deletedIds = Array.isArray(loadedNotebooks._deletedIds) ? loadedNotebooks._deletedIds : [];

            // Get the list of deleted notebook names if it exists
            const deletedNames = Array.isArray(loadedNotebooks._deletedNames) ? loadedNotebooks._deletedNames : [];

            // Log deleted IDs and names for debugging
            if (deletedIds.length > 0) {
                console.log(`Dashboard: Found ${deletedIds.length} deleted notebook IDs: ${JSON.stringify(deletedIds)}`);
            }

            if (deletedNames.length > 0) {
                console.log(`Dashboard: Found ${deletedNames.length} deleted notebook names: ${JSON.stringify(deletedNames)}`);
            }

            // Filter out invalid entries and deleted notebooks, ensure fields exist, and normalize timestamp
            // Make a copy of the array to avoid modifying the original
            const validNotebooks = [];

            // First pass: collect valid notebooks that aren't in the deleted list
            for (let i = 0; i < loadedNotebooks.length; i++) {
                const nb = loadedNotebooks[i];
                // Skip non-object entries or entries without required fields
                if (!nb || typeof nb.id !== 'string' || typeof nb.name !== 'string') {
                    continue;
                }

                // Skip notebooks that are in the deleted list
                if (deletedIds.includes(nb.id)) {
                    console.log(`Dashboard: Skipping deleted notebook: ${nb.id} (${nb.name})`);
                    continue;
                }

                // Add valid notebook to the array
                validNotebooks.push({
                    ...nb,
                    name: nb.name.trim(), // Trim whitespace from names
                    _lastUpdated: Number(nb._lastUpdated) || 0 // Ensure timestamp is number, default 0
                });
            }

            // Sort alphabetically by name
            validNotebooks.sort((a, b) => a.name.localeCompare(b.name));

            // Assign the filtered and sorted array to notebooks
            notebooks = validNotebooks;

            // Preserve the _lastUpdated timestamp from the original array if it exists
            if (loadedNotebooks._lastUpdated) {
                notebooks._lastUpdated = Number(loadedNotebooks._lastUpdated);
            } else {
                // If no timestamp exists, create one to ensure future syncs work correctly
                notebooks._lastUpdated = Date.now();
            }

            // Always preserve the deleted IDs list, even if it's empty
            notebooks._deletedIds = deletedIds;

            // Always preserve the deleted names list, even if it's empty
            notebooks._deletedNames = deletedNames;

            console.log(`Dashboard: Loaded ${notebooks.length} valid notebooks.`);
            renderNotebookList(); // Update the UI
        } catch (error) {
            console.error("Dashboard: Error loading notebooks:", error);
            notebooks = []; // Reset to empty on error
            renderNotebookList(); // Still render the (empty) list
            showStatus("Error loading notebooks.", 'error');
        }
    }

    /** Saves notebooks to storage (with timestamp) */
     async function saveNotebooks() {
         console.log("Dashboard: Saving notebooks...", notebooks);
         try {
            // Ensure notebooks is an array before saving
            if (!Array.isArray(notebooks)) {
                console.error("Attempted to save non-array notebook data. Aborting save.");
                showStatus("Internal error saving notebooks.", 'error');
                return;
            }

            // Always update the _lastUpdated timestamp to ensure sync works correctly
            notebooks._lastUpdated = Date.now();

            // Ensure _deletedIds exists
            if (!Array.isArray(notebooks._deletedIds)) {
                notebooks._deletedIds = [];
            }

            // Ensure _deletedNames exists
            if (!Array.isArray(notebooks._deletedNames)) {
                notebooks._deletedNames = [];
            }

            // Log deleted IDs and names for debugging
            if (notebooks._deletedIds.length > 0) {
                console.log(`Dashboard: Saving notebooks with ${notebooks._deletedIds.length} deleted IDs: ${JSON.stringify(notebooks._deletedIds)}`);
            }

            if (notebooks._deletedNames.length > 0) {
                console.log(`Dashboard: Saving notebooks with ${notebooks._deletedNames.length} deleted names: ${JSON.stringify(notebooks._deletedNames)}`);
            }

            // Create a deep copy to avoid reference issues
            const notebooksToSave = JSON.parse(JSON.stringify(notebooks));

            // Filter out any notebooks that are in the deleted list
            if (notebooksToSave._deletedIds && notebooksToSave._deletedIds.length > 0) {
                const deletedIds = notebooksToSave._deletedIds;
                for (let i = notebooksToSave.length - 1; i >= 0; i--) {
                    if (notebooksToSave[i] && deletedIds.includes(notebooksToSave[i].id)) {
                        console.log(`Dashboard: Removing deleted notebook ${notebooksToSave[i].id} from save data`);
                        notebooksToSave.splice(i, 1);
                    }
                }
            }

            // Save the current state of the notebooks array
            await chrome.storage.local.set({ [NOTEBOOKS_STORAGE_KEY]: notebooksToSave });

            console.log("Dashboard: Notebooks saved successfully.");
            // Optional Drive Trigger for metadata:
            // Consider if this should be triggered *every* time notebooks are saved,
            // or only on specific structural changes (create/delete/rename).
            // Triggering every time is simpler but potentially more frequent uploads.
            // await triggerMetadataUpload(notebooks);
         } catch (error) {
            console.error("Dashboard: Error saving notebooks:", error);
            showStatus("Error saving notebook changes.", 'error');
         }
    }

     // --- Optional: Helper to trigger metadata upload ---
     async function triggerMetadataUpload(dataToUpload) {
         try {
             // Check if Drive sync is enabled
             const enabledResult = await chrome.storage.local.get('driveSyncEnabled');
             if (enabledResult.driveSyncEnabled) {
                console.log("Dashboard: Triggering Drive upload for notebook metadata.");
                // Send message to background script to handle the upload
                 await chrome.runtime.sendMessage({
                     action: 'uploadMetadataFile',
                     key: NOTEBOOKS_STORAGE_KEY,
                     data: dataToUpload
                 });
                 console.log("Dashboard: Metadata upload message sent to background.");
             } else {
                 console.log("Dashboard: Drive sync not enabled, skipping metadata upload trigger.");
             }
         } catch(e) {
             // Handle potential errors like background script not responding
             console.warn("Dashboard: Failed to send metadata upload message to background script:", e);
             // Optionally show a status to the user if this fails repeatedly
             // showStatus("Warning: Could not sync notebook changes to Drive.", 'warning');
         }
     }


    /** Creates a new notebook */
     async function createNotebook(name) {
        const trimmedName = name.trim();
        if (!trimmedName) {
            showStatus("Notebook name cannot be empty.", 'error');
            return false;
        }

        // Case-insensitive check for existing notebook name
        if (notebooks.some(nb => nb.name.toLowerCase() === trimmedName.toLowerCase())) {
            showStatus(`Notebook "${trimmedName}" already exists. Please choose a different name.`, 'error');
            return false;
        }

        // Check if this name was previously deleted
        if (Array.isArray(notebooks._deletedNames) && notebooks._deletedNames.includes(trimmedName.toLowerCase())) {
            console.log(`Dashboard: Attempted to create notebook with previously deleted name: "${trimmedName}"`);
            showStatus(`Cannot create notebook "${trimmedName}" as it was previously deleted. Please choose a different name.`, 'error');
            return false;
        }

        console.log(`Dashboard: Creating new notebook "${trimmedName}"`);

        const newNotebook = {
            id: generateNotebookId(),
            name: trimmedName,
            _lastUpdated: Date.now() // Set timestamp on creation
        };
        notebooks.push(newNotebook);
        notebooks.sort((a, b) => a.name.localeCompare(b.name)); // Keep the list sorted

        // Update the global timestamp for the notebooks array
        notebooks._lastUpdated = Date.now();

        await saveNotebooks(); // Save the updated list
        renderNotebookList(); // Update the UI
        updateDashboardStats(); // Update stats
        showStatus(`Notebook "${trimmedName}" created successfully.`, 'success');
        await triggerMetadataUpload(notebooks); // Sync metadata change
        return true;
    }

    /** Renames a notebook */
     async function renameNotebook(notebookId, newName) {
        const trimmedNewName = newName.trim();
        if (!trimmedNewName) {
            showStatus("Notebook name cannot be empty.", 'error');
            return false;
        }
        const notebookIndex = notebooks.findIndex(nb => nb.id === notebookId);
        if (notebookIndex === -1) {
            showStatus("Notebook not found. Cannot rename.", 'error');
            return false;
        }
        // Case-insensitive check if another notebook *already* has the new name
        if (notebooks.some((nb, index) => index !== notebookIndex && nb.name.toLowerCase() === trimmedNewName.toLowerCase())) {
            showStatus(`Another notebook named "${trimmedNewName}" already exists.`, 'error');
            return false;
        }

        const oldName = notebooks[notebookIndex].name;
        notebooks[notebookIndex].name = trimmedNewName;
        notebooks[notebookIndex]._lastUpdated = Date.now(); // Update timestamp on rename
        notebooks.sort((a, b) => a.name.localeCompare(b.name)); // Re-sort if name change affects order

        await saveNotebooks(); // Save the changes
        renderNotebookList(); // Update sidebar UI
        filterAndRenderNotes(); // Re-render notes in case the name is displayed in details
        showStatus(`Notebook renamed from "${oldName}" to "${trimmedNewName}".`, 'success');
        await triggerMetadataUpload(notebooks); // Sync metadata change
        return true;
    }

    /** Deletes a notebook and moves notes */
     async function deleteNotebook(notebookId) {
        const notebookIndex = notebooks.findIndex(nb => nb.id === notebookId);
        if (notebookIndex === -1) {
            showStatus("Notebook not found.", 'error');
            return false;
        }
        const notebookName = notebooks[notebookIndex].name;

        // Confirmation dialog
        if (!confirm(`Are you sure you want to delete the notebook "${notebookName}"? \n\nAll notes currently inside this notebook will be moved to 'Ungrouped'. This action cannot be undone.`)) {
            return false;
        }

        // 1. Get the notebook details before removing it
        const deletedNotebookId = notebooks[notebookIndex].id;
        const deletedNotebookName = notebooks[notebookIndex].name;

        console.log(`Dashboard: Deleting notebook "${deletedNotebookName}" (ID: ${deletedNotebookId})`);

        // 2. Remove notebook from local `notebooks` array
        notebooks.splice(notebookIndex, 1);

        // 3. Store the deleted notebook ID in a special property to track deletions
        if (!notebooks._deletedIds) {
            notebooks._deletedIds = [];
        }

        // 4. Only add to deletedIds if it's not already there
        if (!notebooks._deletedIds.includes(deletedNotebookId)) {
            notebooks._deletedIds.push(deletedNotebookId);
            console.log(`Dashboard: Added notebook ID ${deletedNotebookId} (${deletedNotebookName}) to deleted IDs list`);
        } else {
            console.log(`Dashboard: Notebook ID ${deletedNotebookId} (${deletedNotebookName}) already in deleted IDs list`);
        }

        // 5. Also store the deleted notebook name to prevent recreation with same name
        if (!notebooks._deletedNames) {
            notebooks._deletedNames = [];
        }

        // 6. Only add to deletedNames if it's not already there
        if (!notebooks._deletedNames.includes(deletedNotebookName.toLowerCase())) {
            notebooks._deletedNames.push(deletedNotebookName.toLowerCase());
            console.log(`Dashboard: Added notebook name "${deletedNotebookName}" to deleted names list`);
        }

        try {
            // 2. Identify notes belonging to the deleted notebook (from `allNotesData` cache)
            const notesToUpdateKeys = allNotesData
                .filter(note => note.notebookId === notebookId)
                .map(note => note.key);

            // 3. Update local cache `allNotesData` immediately for UI responsiveness
            allNotesData = allNotesData.map(note => {
                if (note.notebookId === notebookId) {
                    // Set notebookId to null (Ungrouped) in both the main level and nested data
                    return { ...note, notebookId: null, data: {...note.data, notebookId: null } };
                }
                return note;
            });

            // 4. Prepare storage updates for the affected notes
            const updates = {};
            let notesFoundInStorage = 0;
            showStatus(`Deleting notebook "${notebookName}" and moving notes...`, 'info');

            // Fetch current data for notes to be updated
            const currentNotesData = await chrome.storage.local.get(notesToUpdateKeys);
            for (const noteKey of notesToUpdateKeys) {
                const noteData = currentNotesData[noteKey];
                if (noteData) {
                    // Only update if it actually belongs to the notebook (safety check)
                    if(noteData.notebookId === notebookId) {
                        noteData.notebookId = null; // Set to ungrouped
                        updates[noteKey] = noteData;
                        notesFoundInStorage++;
                    } else {
                        console.warn(`Note ${noteKey} was expected in notebook ${notebookId} but storage says it's in ${noteData.notebookId}. Skipping update for this note.`);
                    }
                } else {
                    console.warn(`Note data for key ${noteKey} not found in storage during notebook delete. It might have been deleted already.`);
                    // Don't count as an error, just skip.
                }
            }

            // 5. Save all note updates together (if any)
            const notesToUploadAfterUpdate = []; // Collect notes for potential Drive sync
            if (Object.keys(updates).length > 0) {
                console.log(`Updating ${Object.keys(updates).length} notes in storage to remove notebookId.`);
                await chrome.storage.local.set(updates);
                // Add updated notes to the list for potential Drive sync
                Object.entries(updates).forEach(([key, data]) => notesToUploadAfterUpdate.push({ noteKey: key, noteData: data }));
            } else {
                console.log("No notes needed storage updates for notebook delete.");
            }

            // 6. Save the modified notebooks array (notebook removed)
            // Update the timestamp to ensure this deletion takes precedence in sync
            if (!notebooks._lastUpdated) {
                notebooks._lastUpdated = Date.now();
            } else {
                notebooks._lastUpdated = Date.now();
            }
            await saveNotebooks();

            // 7. Update UI
            renderNotebookList(); // Refresh sidebar (notebook gone)
            // If the deleted notebook was the current filter, reset filter to 'All'
            if (currentNotebookFilter === notebookId) {
                currentNotebookFilter = null; // Go to 'All Notes'
                // Need to update the active state in the list *again* after resetting filter
                renderNotebookList();
            }
            filterAndRenderNotes(); // Refresh note list (notes now show as 'Ungrouped')
            updateDashboardStats(); // Update stats
            showStatus(`Notebook "${notebookName}" deleted. ${notesFoundInStorage} note(s) moved to Ungrouped.`, 'success');

            // 8. Trigger Drive Sync (Metadata and updated Notes)
            // Force immediate sync of notebook metadata to ensure deletion is propagated
            const syncEnabledForMetadata = await chrome.storage.local.get('driveSyncEnabled');
            if (syncEnabledForMetadata.driveSyncEnabled) {
                console.log("Triggering immediate metadata sync to propagate notebook deletion...");
                await chrome.runtime.sendMessage({ action: 'checkDriveSync' });
            } else {
                await triggerMetadataUpload(notebooks); // Fallback to regular metadata upload
            }
            const syncEnabledResult = await chrome.storage.local.get('driveSyncEnabled');
            if (syncEnabledResult.driveSyncEnabled && notesToUploadAfterUpdate.length > 0) {
                console.log(`Triggering ${notesToUploadAfterUpdate.length} background uploads for notes moved from deleted notebook...`);
                for (const { noteKey, noteData } of notesToUploadAfterUpdate) {
                    try {
                        await chrome.runtime.sendMessage({ action: 'uploadNoteToDrive', noteKey: noteKey, noteData: noteData });
                        await new Promise(res => setTimeout(res, 50)); // Small delay
                    } catch (uploadError) {
                        console.warn(`Failed to trigger upload for moved note ${noteKey}:`, uploadError);
                    }
                }
                console.log("Finished triggering background uploads for moved notes.");
            }

            return true;
        } catch (e) {
            console.error(`Error deleting notebook or updating notes:`, e);
            showStatus('Error deleting notebook. Some notes might not have moved. Reloading data.', 'error', 7000);
            // Critical error - Attempt to reload data to ensure consistency
            await loadNotebooks();
            await loadAllNotes();
            return false;
        }
    }


    // --- Core Logic ---

    /** Load all notes and populate cache */
    async function loadAllNotes() {
        console.log("Dashboard: loadAllNotes() called.");
        if (resultsDiv) resultsDiv.innerHTML = '<p class="loading-message">Loading notes...</p>'; // Show loading message
        allNotesData = []; // Reset cache

        try {
            // Check if IndexedDB is available
            if (window.StickaraIndexedDB) {
                console.log("Using IndexedDB for loading notes");

                // Get notes from IndexedDB with pagination
                const options = {
                    limit: 1000, // Load in chunks to avoid memory issues
                    offset: 0
                };

                let hasMoreNotes = true;
                let processedNoteCount = 0;

                while (hasMoreNotes) {
                    // Get a batch of notes from IndexedDB
                    const notes = await window.StickaraIndexedDB.getNotes(options);

                    if (notes.length === 0) {
                        hasMoreNotes = false;
                        continue;
                    }

                    // Process each note
                    for (const note of notes) {
                        const key = note.key;
                        const value = note;

                        // Basic validation of the note data structure
                        if (typeof value === 'object' && value !== null && value.hasOwnProperty('lastSaved')) {
                            const keyInfo = parseNoteKey(key);
                            if (keyInfo) {
                                let decodedUrl = keyInfo.url;
                                // With the new storage approach, URLs are stored in normalized form
                                // so we can use them directly for display
                                if (keyInfo.url) {
                                    decodedUrl = keyInfo.url;

                                    // Only apply decoding if the URL appears to be encoded
                                    if (keyInfo.url.includes('%')) {
                                        try {
                                            // Check if it has valid percent-encoding patterns
                                            const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
                                            if (validPercentPattern.test(keyInfo.url)) {
                                                decodedUrl = decodeURIComponent(keyInfo.url);
                                            } else {
                                                console.warn(`Invalid percent-encoding pattern in URL: ${keyInfo.url}`);
                                                decodedUrl = keyInfo.url; // Keep original if invalid pattern
                                            }
                                        } catch (e) {
                                            console.warn(`Failed to decode URL: ${keyInfo.url}`, e);
                                            decodedUrl = keyInfo.url; // Use original if decoding fails
                                        }
                                    }
                                }

                                let noteData = { ...value };

                                // Add the processed note to the cache
                                allNotesData.push({
                                    key: key,
                                    url: keyInfo.url, // Store original encoded URL
                                    decodedUrl: decodedUrl, // Store decoded URL for display/search
                                    index: keyInfo.index,
                                    notebookId: noteData.notebookId || null, // Load notebookId, default to null (Ungrouped)
                                    plainText: getPlainText(noteData.text || ''), // Pre-calculate plain text
                                    data: noteData // Store the full original data object as well
                                });
                                processedNoteCount++;
                            } else {
                                console.warn(`Skipping item with key ${key}: Could not parse key.`);
                            }
                        } else {
                            console.warn(`Skipping item with key ${key}: Invalid data format (not an object or missing 'lastSaved'). Value:`, value);
                        }
                    }

                    // Update options for next batch
                    options.offset += options.limit;

                    // If we got fewer notes than the limit, we've reached the end
                    if (notes.length < options.limit) {
                        hasMoreNotes = false;
                    }
                }

                console.log(`Dashboard: Loaded ${processedNoteCount} valid notes from IndexedDB.`);
            } else {
                // Fallback to chrome.storage.local if IndexedDB is not available
                console.log("Falling back to chrome.storage.local for loading notes");

                const items = await chrome.storage.local.get(null); // Fetch everything
                let processedNoteCount = 0;
                const keys = Object.keys(items);

                console.log(`Retrieved ${keys.length} items from local storage.`);

                for (const key of keys) {
                    // Check if the key matches the note prefix
                    if (key.startsWith(STORAGE_KEY_PREFIX)) {
                        const value = items[key];
                        // Basic validation of the note data structure
                        if (typeof value === 'object' && value !== null && value.hasOwnProperty('lastSaved')) {
                            const keyInfo = parseNoteKey(key);
                            if (keyInfo) {
                                let decodedUrl = keyInfo.url;
                                try {
                                    // Attempt to decode the URL component only if it looks like valid encoding
                                    if (keyInfo.url && keyInfo.url.includes('%')) {
                                        // Check if it has valid percent-encoding patterns
                                        const validPercentPattern = /^[^%]*(%[0-9A-Fa-f]{2})*[^%]*$/;
                                        if (validPercentPattern.test(keyInfo.url)) {
                                            decodedUrl = decodeURIComponent(keyInfo.url);
                                        } else {
                                            console.warn(`Invalid percent-encoding pattern in URL: ${keyInfo.url}`);
                                            decodedUrl = keyInfo.url; // Keep original if invalid pattern
                                        }
                                    } else {
                                        decodedUrl = keyInfo.url; // No percent encoding, use as-is
                                    }
                                } catch (e) {
                                    console.warn(`Failed to decode URL component: ${keyInfo.url}`, e);
                                    // Keep the original undecoded URL if decoding fails
                                    decodedUrl = keyInfo.url;
                                }

                                let noteData = { ...value };

                                // Add the processed note to the cache
                                allNotesData.push({
                                    key: key,
                                    url: keyInfo.url, // Store original encoded URL
                                    decodedUrl: decodedUrl, // Store decoded URL for display/search
                                    index: keyInfo.index,
                                    notebookId: noteData.notebookId || null, // Load notebookId, default to null (Ungrouped)
                                    plainText: getPlainText(noteData.text || ''), // Pre-calculate plain text
                                    data: noteData // Store the full original data object as well
                                });
                                processedNoteCount++;
                            } else {
                                console.warn(`Skipping item with key ${key}: Could not parse key.`);
                            }
                        } else {
                            console.warn(`Skipping item with key ${key}: Invalid data format (not an object or missing 'lastSaved'). Value:`, value);
                        }
                    }
                }
                console.log(`Dashboard: Loaded ${processedNoteCount} valid notes from chrome.storage.local.`);
            }

            // Render notes based on default filters
            filterAndRenderNotes();

            // Update dashboard stats
            updateDashboardStats();
        } catch (error) {
            console.error("Dashboard CRITICAL ERROR loading all notes:", error);
            if (resultsDiv) resultsDiv.innerHTML = '<p class="no-results error">Error loading notes. Check console for details.</p>';
            showStatus(`Error loading notes: ${error.message || error}`, 'error', 10000);
        }
        // Always update bulk bar after load, regardless of success/failure
        updateBulkActionsBar();
    }

     /** Render notebook list in the sidebar */
     function renderNotebookList() {
        if (!notebookListUl) {
             console.error("Cannot render notebook list: UL element not found.");
             return;
        }
        // Start with static All Notes, Ungrouped, and Global Notes buttons
        notebookListUl.innerHTML = `
            <li><button class="notebook-button ${currentNotebookFilter === null ? 'active' : ''}" data-notebook-id="ALL"><span class="notebook-name">All Notes</span></button></li>
            <li><button class="notebook-button ${currentNotebookFilter === 'UNGROUPED' ? 'active' : ''}" data-notebook-id="UNGROUPED"><span class="notebook-name">Ungrouped</span></button></li>
            <li><button class="notebook-button ${currentNotebookFilter === 'GLOBAL' ? 'active' : ''}" data-notebook-id="GLOBAL"><span class="notebook-name">🌐 Global Notes</span></button></li>
        `;

        // Add a separator only if there are custom notebooks
        if (notebooks.length > 0) {
            const hr = document.createElement('hr');
            notebookListUl.appendChild(hr);
        }

        // Create fragment for custom notebooks
        const fragment = document.createDocumentFragment();
        notebooks.forEach(nb => {
            const li = document.createElement('li');
            li.dataset.notebookId = nb.id; // Set ID on LI for drop target identification
            // Add drag event listeners to the LI element
            li.addEventListener('dragover', handleNotebookDragOver);
            li.addEventListener('dragleave', handleNotebookDragLeave);
            li.addEventListener('drop', handleNotebookDrop);

            const button = document.createElement('button');
            button.className = 'notebook-button';
            button.dataset.notebookId = nb.id; // Set ID on Button for click identification
            if (nb.id === currentNotebookFilter) {
                button.classList.add('active'); // Highlight if it's the current filter
            }

            // Notebook name span (allows for overflow ellipsis)
            const nameSpan = document.createElement('span');
            nameSpan.className = 'notebook-name';
            nameSpan.textContent = nb.name;
            nameSpan.title = nb.name; // Tooltip for full name if truncated
            button.appendChild(nameSpan);

            // Controls container (rename, delete)
            const controlsDiv = document.createElement('div');
            controlsDiv.className = 'notebook-controls';

            // Rename button
            const renameBtn = document.createElement('button');
            renameBtn.innerHTML = '✏️'; // Use emoji or icon font/SVG
            renameBtn.title = 'Rename Notebook';
            renameBtn.onclick = (e) => {
                e.stopPropagation(); // Prevent li click event from firing
                openRenameModal(nb.id, nb.name);
            };

            // Delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.innerHTML = '🗑️'; // Use emoji or icon font/SVG
            deleteBtn.title = 'Delete Notebook';
            deleteBtn.onclick = (e) => {
                e.stopPropagation(); // Prevent li click event from firing
                deleteNotebook(nb.id);
            };

            controlsDiv.appendChild(renameBtn);
            controlsDiv.appendChild(deleteBtn);
            button.appendChild(controlsDiv); // Add controls to the button

            li.appendChild(button); // Add button to the list item
            fragment.appendChild(li); // Add list item to the fragment
        });
        notebookListUl.appendChild(fragment); // Append all custom notebooks

         // Update bulk move dropdown options whenever the list renders
         renderBulkMoveOptions();
    }

    /** Populate options in the bulk move dropdowns */
    function renderBulkMoveOptions() {
        // Populate notes move dropdown
        if (bulkMoveOptionsDiv) {
            bulkMoveOptionsDiv.innerHTML = ''; // Clear existing options

            // Add "Ungrouped" option first
            const ungroupedOption = document.createElement('button');
            ungroupedOption.textContent = 'Ungrouped';
            ungroupedOption.dataset.targetNotebookId = 'UNGROUPED'; // Special identifier
            ungroupedOption.style.color = '#333333';
            ungroupedOption.style.backgroundColor = '#ffffff';
            ungroupedOption.style.visibility = 'visible';
            ungroupedOption.style.display = 'block';
            bulkMoveOptionsDiv.appendChild(ungroupedOption);

            // Add separator if there are custom notebooks
            if (notebooks.length > 0) {
                const hr = document.createElement('hr');
                bulkMoveOptionsDiv.appendChild(hr);
            }

            // Add existing notebooks, sorted alphabetically (they should already be sorted in `notebooks` array)
            notebooks.forEach(nb => {
                const optionButton = document.createElement('button');
                optionButton.textContent = nb.name; // Just display the notebook name
                optionButton.dataset.targetNotebookId = nb.id; // Store the actual ID
                optionButton.title = `Move selected notes to notebook: ${nb.name}`; // Tooltip
                optionButton.style.color = '#333333';
                optionButton.style.backgroundColor = '#ffffff';
                optionButton.style.visibility = 'visible';
                optionButton.style.display = 'block';
                bulkMoveOptionsDiv.appendChild(optionButton);
            });
        } else {
            console.warn("Notes bulk move options container not found.");
        }

        // Populate highlights move dropdown
        if (highlightsMoveOptionsDiv) {
            highlightsMoveOptionsDiv.innerHTML = ''; // Clear existing options

            // Add "Ungrouped" option first
            const ungroupedOption = document.createElement('button');
            ungroupedOption.textContent = 'Ungrouped';
            ungroupedOption.dataset.targetNotebookId = 'UNGROUPED'; // Special identifier
            ungroupedOption.style.color = '#333333';
            ungroupedOption.style.backgroundColor = '#ffffff';
            ungroupedOption.style.visibility = 'visible';
            ungroupedOption.style.display = 'block';
            highlightsMoveOptionsDiv.appendChild(ungroupedOption);

            // Add separator if there are custom notebooks
            if (notebooks.length > 0) {
                const hr = document.createElement('hr');
                highlightsMoveOptionsDiv.appendChild(hr);
            }

            // Add existing notebooks, sorted alphabetically
            notebooks.forEach(nb => {
                const optionButton = document.createElement('button');
                optionButton.textContent = nb.name; // Just display the notebook name
                optionButton.dataset.targetNotebookId = nb.id; // Store the actual ID
                optionButton.title = `Move selected highlights to notebook: ${nb.name}`; // Tooltip
                optionButton.style.color = '#333333';
                optionButton.style.backgroundColor = '#ffffff';
                optionButton.style.visibility = 'visible';
                optionButton.style.display = 'block';
                highlightsMoveOptionsDiv.appendChild(optionButton);
            });
        } else {
            console.warn("Highlights bulk move options container not found.");
        }
    }


     /** Filter notes based on current criteria and render */
    function filterAndRenderNotes() {
        if (!allNotesData) {
            console.warn("filterAndRenderNotes called before allNotesData is loaded.");
            return;
        }
        if (!resultsDiv) {
            console.error("Cannot render notes: Results div not found.");
            return;
        }

        // --- Get Filter Values ---
        const searchQuery = searchInput ? searchInput.value.trim().toLowerCase() : '';
        const reminderFilter = reminderFilterCheckbox ? reminderFilterCheckbox.checked : false;
        const globalPinFilter = globalPinFilterCheckbox ? globalPinFilterCheckbox.checked : false;
        const [sortBy, sortOrder] = sortBySelect ? sortBySelect.value.split('_') : ['lastSaved', 'desc']; // Default sort
        const viewOption = dashboardViewOption ? dashboardViewOption.value : 'all';

        console.log(`Filtering notes: Notebook='${currentNotebookFilter}', Search='${searchQuery}', Reminder=${reminderFilter}, Pinned=${globalPinFilter}, SortBy='${sortBy}', SortOrder='${sortOrder}'`);

        // --- Filtering ---
        let filteredNotes = allNotesData.filter(note => {
            // 1. Notebook Filter
            let notebookFilterOk = false;

            // Check if this is a global note
            const isGlobalNote = note.data.isGlobal === true ||
                (note.key && note.key.includes('global_note'));

            if (currentNotebookFilter === null) { // 'All Notes' selected
                notebookFilterOk = true;
            } else if (currentNotebookFilter === 'UNGROUPED') { // 'Ungrouped' selected
                // Hide global notes from Ungrouped section
                notebookFilterOk = !note.notebookId && !isGlobalNote;
            } else if (currentNotebookFilter === 'GLOBAL') { // 'Global Notes' selected
                // Show only global notes
                notebookFilterOk = isGlobalNote;
            } else { // A specific notebook selected
                notebookFilterOk = note.notebookId === currentNotebookFilter;
            }
            if (!notebookFilterOk) return false; // Early exit if notebook doesn't match

            // 2. Reminder Filter
            // Note passes if filter is off OR if filter is on AND note has a reminder set
            const reminderFilterOk = !reminderFilter || (note.data.reminder && note.data.reminder > 0);
            if (!reminderFilterOk) return false;

            // 3. Global Pin Filter
            // Note passes if filter is off OR if filter is on AND note is globally pinned
            const globalPinFilterOk = !globalPinFilter || note.data.globallyPinned === true;
            if (!globalPinFilterOk) return false;

            // 4. Enhanced Search Query Filter (applies to text, tags, URL, title)
            if (searchQuery) {
                try {
                    // Enhanced text matching with better null handling
                    const textMatch = (note.plainText || note.data?.content || '').toLowerCase().includes(searchQuery);

                    // Enhanced tags matching with better validation
                    let tagsMatch = false;
                    if (note.data.tags && Array.isArray(note.data.tags)) {
                        tagsMatch = note.data.tags.some(tag => {
                            try {
                                return String(tag || '').toLowerCase().includes(searchQuery);
                            } catch (e) {
                                console.warn("Error processing tag:", tag, e);
                                return false;
                            }
                        });
                    }

                    // Enhanced URL matching
                    const urlMatch = (note.decodedUrl || note.url || '').toLowerCase().includes(searchQuery);

                    // Enhanced title matching
                    const titleMatch = (note.data.title || '').toLowerCase().includes(searchQuery);

                    // Note must match at least one field if search query exists
                    if (!(textMatch || tagsMatch || urlMatch || titleMatch)) return false;
                } catch (searchError) {
                    console.warn("Error during search filtering for note:", note.key, searchError);
                    return false; // Exclude notes that cause search errors
                }
            }

            // 6. View Options Filter
            if (viewOption === 'recent') {
                // Filter for notes from the last 7 days
                const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
                if (!note.data._lastSaved || note.data._lastSaved < sevenDaysAgo) {
                    return false;
                }
            } else if (viewOption === 'current') {
                // Get the current URL from the browser (if available)
                const currentUrl = window.location.href;
                if (currentUrl && note.decodedUrl !== currentUrl) {
                    return false;
                }
            }

            // If all checks passed, include the note
            return true;
        });

        console.log(`Found ${filteredNotes.length} notes after filtering.`);

        // --- Sorting ---
        filteredNotes.sort((a, b) => {
            let compareA, compareB;

            // Determine primary sort values
            switch (sortBy) {
                case 'url':
                    compareA = a.decodedUrl?.toLowerCase() || ''; // Use decoded URL, handle null/undefined
                    compareB = b.decodedUrl?.toLowerCase() || '';
                    break;
                case 'title': // Add sorting by title
                    compareA = a.data?.title?.toLowerCase() || a.decodedUrl?.toLowerCase() || ''; // Fallback to URL if no title
                    compareB = b.data?.title?.toLowerCase() || b.decodedUrl?.toLowerCase() || '';
                    break;
                case 'lastSaved':
                default: // Default to lastSaved
                    // Ensure timestamps are numbers, default to 0 if invalid/missing
                    compareA = a.data?.lastSaved ? new Date(a.data.lastSaved).getTime() : 0;
                    compareB = b.data?.lastSaved ? new Date(b.data.lastSaved).getTime() : 0;
                    // Handle potential NaN from invalid dates
                    if (isNaN(compareA)) compareA = 0;
                    if (isNaN(compareB)) compareB = 0;
                    break;
            }

            // Perform primary comparison
            let comparison = 0;
            if (compareA > compareB) {
                comparison = 1;
            } else if (compareA < compareB) {
                comparison = -1;
            }

            // Secondary sort (by URL, then Index) if primary values are equal
            if (comparison === 0) {
                comparison = (a.decodedUrl?.toLowerCase() || '').localeCompare(b.decodedUrl?.toLowerCase() || '');
            }
            if (comparison === 0) {
                comparison = (a.index || 0) - (b.index || 0); // Compare note indices as numbers
            }

            // Apply sort order (ascending or descending)
            return sortOrder === 'desc' ? (comparison * -1) : comparison;
        });

        renderNotes(filteredNotes, searchQuery); // Render the sorted and filtered notes
    }

    /** Render the filtered and sorted notes with pagination */
    function renderNotes(notesToRender, query) {
        if (!resultsDiv) {
            console.error("Cannot render notes: Results div is null.");
            return;
        }
        resultsDiv.innerHTML = ''; // Clear previous results

        // Apply side-by-side view class if active
        if (isSideBySideView) {
            resultsDiv.classList.add('side-by-side-view');
        } else {
            resultsDiv.classList.remove('side-by-side-view');
        }

        // Reset select all checkbox
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }

        if (notesToRender.length === 0) {
            // Display a user-friendly message when no notes match
            resultsDiv.innerHTML = '<p class="no-results">No matching notes found in this view.</p>';

            // Update pagination controls
            updatePaginationControls(0);

            // Update notes count display
            if (notesCountDisplay) {
                notesCountDisplay.textContent = 'Showing 0 of 0 notes';
            }
        } else {
            // Calculate pagination
            totalPages = Math.ceil(notesToRender.length / pageSize);

            // Ensure current page is valid
            if (currentPage > totalPages) {
                currentPage = totalPages;
            } else if (currentPage < 1) {
                currentPage = 1;
            }

            // Calculate start and end indices for current page
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, notesToRender.length);

            // Get notes for current page
            const notesForCurrentPage = notesToRender.slice(startIndex, endIndex);

            // Create fragment for notes
            const fragment = document.createDocumentFragment();
            notesForCurrentPage.forEach(note => {
                try {
                    // Create the DOM element for each note
                    const element = createNoteElement(note, query);
                    if (element) { // Ensure element creation was successful
                        fragment.appendChild(element);
                    }
                } catch (e) {
                    // Log error but continue rendering other notes
                    console.error(`Error creating element for note ${note?.key}:`, e);
                    // Optionally, render an error placeholder for this note
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'note-item error-item';
                    errorDiv.textContent = `Error rendering note ${note?.key}. See console.`;
                    fragment.appendChild(errorDiv);
                }
            });
            resultsDiv.appendChild(fragment); // Append all created elements at once

            // Update pagination controls
            updatePaginationControls(notesToRender.length);

            // Update notes count display
            if (notesCountDisplay) {
                notesCountDisplay.textContent = `Showing ${startIndex + 1}-${endIndex} of ${notesToRender.length} notes`;
            }
        }

        // Always update bulk actions bar state after rendering
        updateBulkActionsBar();
    }

    /** Update pagination controls based on current state */
    function updatePaginationControls(totalNotes) {
        if (!pageIndicator || !prevPageBtn || !nextPageBtn) {
            console.warn("Pagination controls not found in the DOM");
            return;
        }

        // Update page indicator
        pageIndicator.textContent = totalNotes === 0 ?
            'No notes' :
            `Page ${currentPage} of ${totalPages}`;

        // Update button states
        prevPageBtn.disabled = currentPage <= 1 || totalNotes === 0;
        nextPageBtn.disabled = currentPage >= totalPages || totalNotes === 0;
    }


    /** Create DOM element for a single note */
     function createNoteElement(noteDataWrapper, query) {
        // Basic validation of input data
        if (!noteDataWrapper || !noteDataWrapper.key || !noteDataWrapper.data) {
            console.warn("createNoteElement called with invalid noteDataWrapper:", noteDataWrapper);
            return null;
        }

        const { key, url, decodedUrl, index, notebookId, plainText, data: note } = noteDataWrapper; // Destructure for easier access

        // --- Main Container ---
        const itemDiv = document.createElement('div');
        itemDiv.className = 'note-item';

        // Check if this is a global note
        const isGlobalNote = note.isGlobal === true || key.includes('global_note');
        if (isGlobalNote) {
            itemDiv.classList.add('global-note');
        }

        itemDiv.dataset.noteKey = key; // Store note key for identification
        itemDiv.setAttribute('draggable', 'true'); // Make the note draggable
        itemDiv.addEventListener('dragstart', handleNoteDragStart);
        itemDiv.addEventListener('dragend', handleNoteDragEnd);

        // --- Checkbox for Bulk Actions ---
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'note-select-checkbox';
        checkbox.checked = selectedNoteKeys.has(key); // Set initial checked state
        checkbox.title = "Select this note for bulk actions";
        checkbox.addEventListener('change', (e) => {
             if (e.target.checked) {
                 selectedNoteKeys.add(key);
             } else {
                 selectedNoteKeys.delete(key);
             }
             updateBulkActionsBar(); // Update bulk bar visibility/count
        });

        // --- Content Area ---
        const contentDiv = document.createElement('div');
        contentDiv.className = 'note-content';

        // --- URL / Title Link ---
        const urlLink = document.createElement('a');
        urlLink.className = 'note-url';
        urlLink.href = '#'; // Prevent default navigation, handle click via JS

        // Handle snippet notes differently
        if (url && typeof url === 'string' && url.startsWith('snippet_')) {
            urlLink.textContent = note.title || 'Snippet Note';
            urlLink.title = `Snippet Note ${index}`;
            urlLink.style.fontStyle = 'italic';
            // For snippets, clicking doesn't open a tab
            urlLink.addEventListener('click', (e) => {
                e.preventDefault();
                showStatus('Snippet notes cannot be opened in a tab.', 'info');
            });
        }
        // Handle global notes specially
        else if (isGlobalNote) {
            urlLink.textContent = note.title || 'Global Note';
            urlLink.title = `Global Note ${index}`;
            urlLink.style.fontStyle = 'italic';
            // For global notes, clicking opens the modal
            urlLink.addEventListener('click', (e) => {
                e.preventDefault();
                openNoteInModal(key);
            });
        }
        else {
            // Use note title if available, otherwise fall back to decoded URL
            let displayUrl = decodedUrl;

            urlLink.textContent = note.title || displayUrl || '[Missing URL]';
            urlLink.title = `View Note ${index} content`;
            // Add event listener to open the note in modal
            urlLink.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent anchor jump
                openNoteInModal(key); // Open note in modal instead of navigating to page
            });
        }

        // --- Details Section (Index, Notebook, Tags, Reminder, Saved Date) ---
        const detailsSpan = document.createElement('div');
        detailsSpan.className = 'note-details';

        let detailsHTML = `<span class="detail-index">Note ${index}${note.globallyPinned ? ' <span title="Important Note">⭐</span>' : ''}${isGlobalNote ? ' <span title="Global Note (works across all URLs)">🌐</span>' : ''}</span>`;

        // Display Notebook Name with emoji
        const notebook = notebooks.find(nb => nb.id === notebookId);
        // Sanitize notebook name before inserting into HTML using escapeHtml
        const notebookNameDisplay = notebook ? escapeHtml(notebook.name) : 'Ungrouped';
        detailsHTML += `<span class="detail-notebook" title="Notebook: ${notebookNameDisplay}">📓 Notebook: ${notebookNameDisplay}</span>`;

        // Display Tags (if any)
        if (note.tags && Array.isArray(note.tags) && note.tags.length > 0) {
            // Sanitize each tag individually using escapeHtml
            const sanitizedTags = note.tags.map(t => escapeHtml(String(t))).join(', ');
            detailsHTML += `<span class="detail-tags" title="Tags: ${sanitizedTags}">Tags: ${sanitizedTags}</span>`;
        }

        // Display Reminder (if set)
        if (note.reminder) {
            try {
                const reminderDate = new Date(note.reminder);
                // Check if date is valid before displaying
                if (!isNaN(reminderDate.getTime())) {
                    const calendarIcon = note.calendarEventId ? ' 📅' : '';
                    const calendarTitle = note.calendarEventId ? ' (Added to Google Calendar)' : '';
                    detailsHTML += `<span class="detail-reminder" title="Reminder set for ${reminderDate.toLocaleString()}${calendarTitle}">Reminder: ${reminderDate.toLocaleString()}${calendarIcon}</span>`;
                } else {
                    console.warn(`Invalid reminder date format for note ${key}: ${note.reminder}`);
                    detailsHTML += `<span class="detail-reminder error-detail" title="Invalid reminder date: ${note.reminder}">Reminder: [Invalid Date]</span>`;
                }
            } catch(e){ console.warn("Error processing reminder date", note.reminder, e); }
        }

        // Display Last Saved Date (for all note types)
        if (note.lastSaved) {
            try {
                const savedDate = new Date(note.lastSaved);
                 if (!isNaN(savedDate.getTime())) {
                    detailsHTML += `<span class="detail-saved" title="Last saved on ${savedDate.toLocaleString()}">Saved: ${savedDate.toLocaleString()}</span>`;
                 } else {
                    console.warn(`Invalid lastSaved date format for note ${key}: ${note.lastSaved}`);
                    detailsHTML += `<span class="detail-saved error-detail" title="Invalid saved date: ${note.lastSaved}">Saved: [Invalid Date]</span>`;
                 }
            } catch(e){ console.warn("Error processing lastSaved date", note.lastSaved, e); }
        }
        detailsSpan.innerHTML = detailsHTML;

        // --- Text Snippet ---
        const snippetP = document.createElement('p');
        snippetP.className = 'note-snippet';
        snippetP.style.cursor = 'pointer'; // Add pointer cursor to indicate clickable

        // Safely highlight and truncate the snippet
        let displaySnippet = '';
        if (query) {
            // Use our safe highlighting function that prevents XSS
            displaySnippet = window.safeHighlightSearchTerms(plainText || '', query);
        } else {
            // If no query, just escape the plain text
            displaySnippet = escapeHtml(plainText || '');
        }

        // Truncate the snippet after highlighting
        const truncatedSnippet = displaySnippet.substring(0, 250) +
            (plainText && plainText.length > 250 ? '...' : '');

        // Set the HTML content safely
        snippetP.innerHTML = truncatedSnippet;
        // Add a class if the original text was potentially long (e.g., > 180 chars) - used for styling hints
        if (plainText && plainText.length > 180) {
            snippetP.classList.add('potentially-long');
        }

        // Make the snippet text clickable to open the modal
        snippetP.addEventListener('click', (e) => {
            // Don't trigger if clicking on a checkbox or button
            if (e.target.closest('.note-select-checkbox') || e.target.closest('button')) {
                return;
            }
            openNoteInModal(key);
        });

        // --- Assemble Element ---
        contentDiv.appendChild(urlLink);
        contentDiv.appendChild(detailsSpan);
        contentDiv.appendChild(snippetP);
        itemDiv.appendChild(checkbox); // Checkbox first visually
        itemDiv.appendChild(contentDiv);



        return itemDiv; // Return the fully constructed element
    }

     /** Update bulk actions bar visibility and button states */
    function updateBulkActionsBar() {
        if (!bulkActionsBar || !selectionCountSpan) {
             console.warn("Bulk actions bar or selection count span not found. Cannot update.");
             return;
        }
        const count = selectedNoteKeys.size;
        selectionCountSpan.textContent = `${count} note${count !== 1 ? 's' : ''} selected`;

        const shouldBeVisible = count > 0;

        // Use classList.toggle for cleaner visibility management based on count
        bulkActionsBar.classList.toggle('visible', shouldBeVisible);

        // Log state change for debugging if needed
        // console.log(`--> updateBulkActionsBar: Count=${count}. Setting visibility to ${shouldBeVisible}. Current classes: "${bulkActionsBar.className}"`);

        // Enable/Disable bulk action buttons based on selection count
        const buttonsEnabled = shouldBeVisible;
        if (bulkDeleteButton) bulkDeleteButton.disabled = !buttonsEnabled;
        if (bulkExportButton) bulkExportButton.disabled = !buttonsEnabled;
        if (bulkExportFormatSelect) bulkExportFormatSelect.disabled = !buttonsEnabled;
        if (bulkMoveDropdownButton) bulkMoveDropdownButton.disabled = !buttonsEnabled; // Also control the move button

        // Update "Select All" checkbox state
        if (selectAllCheckbox && resultsDiv) {
            const noteItems = resultsDiv.querySelectorAll('.note-item');
            const visibleNoteCount = noteItems.length;

            if (visibleNoteCount === 0) {
                // No notes visible, disable checkbox
                selectAllCheckbox.checked = false;
                selectAllCheckbox.disabled = true;
            } else {
                // Enable checkbox
                selectAllCheckbox.disabled = false;

                // Check if all visible notes are selected
                let allSelected = true;
                noteItems.forEach(item => {
                    const key = item.dataset.noteKey;
                    if (key && !selectedNoteKeys.has(key)) {
                        allSelected = false;
                    }
                });

                // Update checkbox state without triggering change event
                selectAllCheckbox.checked = allSelected && visibleNoteCount > 0;
            }
        }
    }

    /** Debounce filter execution */
    function debouncedFilter() {
         clearTimeout(debounceTimer); // Clear any existing timer
         // Set a new timer to execute filterAndRenderNotes after a delay (e.g., 300ms)
         debounceTimer = setTimeout(() => {
             console.log("Debounce timer fired. Executing filterAndRenderNotes.");
             // Reset to first page when filtering
             currentPage = 1;
             filterAndRenderNotes();
         }, 300);
    }


     // --- Drag and Drop Handlers ---

     // Auto-scroll configuration
     const AUTO_SCROLL_CONFIG = {
         threshold: 50, // Distance from edge to trigger auto-scroll
         speed: 8, // Scroll speed in pixels per frame
         maxSpeed: 20, // Maximum scroll speed
         acceleration: 1.2 // Speed acceleration factor
     };

     let autoScrollInterval = null;
     let autoScrollSpeed = AUTO_SCROLL_CONFIG.speed;

     function handleNoteDragStart(e) {
        // Ensure the event target is a draggable note item and has a key
        if (!e.target.classList.contains('note-item') || !e.target.dataset.noteKey) {
            e.preventDefault(); // Prevent dragging if not a valid note
            return;
        }
        draggedNoteKey = e.target.dataset.noteKey; // Store the key of the dragged note
        e.dataTransfer.effectAllowed = 'move'; // Indicate the allowed operation
        // Set data transfer data (required for Firefox, useful for identifying drop)
        e.dataTransfer.setData('text/plain', draggedNoteKey);
        // Add styling to the dragged element and body
        e.target.classList.add('dragging');
        document.body.classList.add('dragging-note'); // More specific class for body
        if (dropOverlay) dropOverlay.classList.add('visible'); // Show the drop overlay

        // Initialize auto-scroll functionality
        initAutoScroll();

        // Make notebook section more visible during drag
        if (notebookSidebar) {
            notebookSidebar.classList.add('drag-active');
        }

        console.log(`DragStart: Note Key = ${draggedNoteKey}`);
    }

    function handleNoteDragEnd(e) {
        console.log(`DragEnd: Note Key = ${draggedNoteKey}, Drop Effect = ${e.dataTransfer.dropEffect}`);
        // Remove dragging styles from body and the dragged element (if it still exists)
        document.body.classList.remove('dragging-note');
        if (dropOverlay) dropOverlay.classList.remove('visible'); // Hide overlay
        // Find the original element and remove dragging class
        if (draggedNoteKey && resultsDiv) {
             const draggedElement = resultsDiv.querySelector(`.note-item[data-note-key="${draggedNoteKey}"]`);
             draggedElement?.classList.remove('dragging');
        }
         // Clean up highlight on any potential drop targets (notebooks)
        notebookListUl?.querySelectorAll('.notebook-button.drag-over').forEach(el => el.classList.remove('drag-over'));

        // Clean up auto-scroll functionality
        cleanupAutoScroll();

        // Remove drag-active class from notebook sidebar
        if (notebookSidebar) {
            notebookSidebar.classList.remove('drag-active');
        }

         // Reset the tracked dragged note key
         draggedNoteKey = null;
    }

    function handleNotebookDragOver(e) {
        e.preventDefault(); // Necessary to allow dropping
        e.dataTransfer.dropEffect = 'move'; // Indicate this is a valid drop target

        // Add highlight *only* to valid drop target LIs (those with a notebook ID, excluding 'All Notes')
         const liTarget = e.target.closest('li[data-notebook-id]');
         if (liTarget && liTarget.dataset.notebookId !== 'ALL') { // Can drop on Ungrouped or specific notebooks
              // Add highlight class to the BUTTON inside the valid LI for better visual feedback
             liTarget.querySelector('.notebook-button')?.classList.add('drag-over');
             if (dropOverlay) { // Update overlay text based on target
                const targetId = liTarget.dataset.notebookId;
                const targetName = targetId === 'UNGROUPED' ? 'Ungrouped' : (notebooks.find(nb => nb.id === targetId)?.name || 'Notebook');
                dropOverlay.textContent = `Move to "${targetName}"`;
             }
         } else {
             // If dragging over 'All Notes' or outside a valid LI, remove highlights from others
             notebookListUl?.querySelectorAll('.notebook-button.drag-over').forEach(el => el.classList.remove('drag-over'));
             if (dropOverlay) dropOverlay.textContent = "Drop on a notebook to move"; // Reset overlay text
         }
    }

    function handleNotebookDragLeave(e) {
        // Remove highlight when dragging leaves a potential target LI
        const liTarget = e.target.closest('li[data-notebook-id]');
         if (liTarget) {
            // Remove highlight from the button inside this specific LI
            liTarget.querySelector('.notebook-button')?.classList.remove('drag-over');
         }
        // Reset overlay text if leaving the general notebook area (might need refinement)
        // if (!notebookListUl.contains(e.relatedTarget)) { // Check if leaving the list entirely
        //     if (dropOverlay) dropOverlay.textContent = "Drop here to move";
        // }
    }

     async function handleNotebookDrop(e) {
        e.preventDefault(); // Prevent default browser action

        // Check if this is a note or highlight drop
        const droppedNoteKey = draggedNoteKey || e.dataTransfer.getData('text/plain'); // Get key from state or data transfer
        const highlightId = draggedHighlightId || e.dataTransfer.getData('application/highlight-id');
        // Get highlight key (not used currently but might be needed in the future)
        e.dataTransfer.getData('application/highlight-key');

        const liTarget = e.target.closest('li[data-notebook-id]'); // Find the target notebook LI element

        // Clean up highlights immediately
        if (liTarget) {
            liTarget.querySelector('.notebook-button')?.classList.remove('drag-over');
        } else { // Ensure highlights are removed everywhere if drop wasn't on a valid LI
            notebookListUl?.querySelectorAll('.notebook-button.drag-over').forEach(el => el.classList.remove('drag-over'));
        }

        // Validate drop target
        if (!liTarget || liTarget.dataset.notebookId === 'ALL') {
             console.warn("Drop ignored: Invalid target LI (cannot drop on 'All Notes').");
             return;
        }

        const targetNotebookIdRaw = liTarget.dataset.notebookId; // ID from LI's data attribute
        // Map 'UNGROUPED' to null for storage, otherwise use the ID
        const targetNotebookId = (targetNotebookIdRaw === 'UNGROUPED') ? null : targetNotebookIdRaw;

        // Handle highlight drop
        if (highlightId) {
            console.log(`Drop Event: Highlight ID=${highlightId}, Target Notebook ID=${targetNotebookId}`);

            // Find the highlight in our data
            const highlightIndex = allHighlightsData.findIndex(h => h.highlightId === highlightId);
            if (highlightIndex === -1) {
                console.error(`Highlight with ID ${highlightId} not found in data.`);
                return;
            }

            const highlight = allHighlightsData[highlightIndex];

            // Add the highlight to the notebook
            await addHighlightToNotebook(highlight, targetNotebookId);
            return;
        }

        // Handle note drop
        if (!droppedNoteKey) {
            console.warn("Drop ignored: No valid note key or highlight ID found.");
            return;
        }

        console.log(`Drop Event: Note Key=${droppedNoteKey}, Target Notebook ID=${targetNotebookId} (Raw Target: ${targetNotebookIdRaw})`);

         // --- Find the note in our local cache ---
         let originalNotebookId = null; // Store original ID for potential rollback/check
         const noteIndexInCache = allNotesData.findIndex(note => {
              if (note.key === droppedNoteKey) {
                   originalNotebookId = note.notebookId; // Capture original ID
                   return true;
              }
              return false;
         });

        if (noteIndexInCache === -1) {
            console.error("Dropped note data not found in cache! Key:", droppedNoteKey);
            // Don't reset draggedNoteKey here.
            showStatus("Error: Dropped note not found.", 'error');
            return;
        }
        const noteToMove = allNotesData[noteIndexInCache];

        // Prevent dropping onto the *same* notebook it's already in
         if (originalNotebookId === targetNotebookId) {
            console.log("Note dropped onto its current location. No change needed.");
            // Don't reset draggedNoteKey here.
             return; // No action required
         }

         // --- Perform the Update ---
         try {
             // 1. Update local cache first for UI responsiveness (provisional update)
             noteToMove.notebookId = targetNotebookId;
             // Also update the notebookId within the nested 'data' object for consistency
             noteToMove.data.notebookId = targetNotebookId;

             // 2. Update UI immediately
             // Option A: Full re-render (simplest)
             // filterAndRenderNotes();
             // Option B: More intelligent update (remove from old view, maybe update details in place)
             // If the current filter view means the note should disappear, re-render fully.
             const shouldDisappear = (currentNotebookFilter !== null && currentNotebookFilter !== targetNotebookIdRaw);
             if (shouldDisappear) {
                 console.log("Note moved out of current view. Re-rendering list.");
                 filterAndRenderNotes(); // Full re-render is necessary
             } else {
                 // Note remains in view (either 'All Notes' or the target notebook was viewed).
                 // Update the details of the existing element directly for smoother UX.
                 const element = resultsDiv.querySelector(`.note-item[data-note-key="${droppedNoteKey}"]`);
                 if (element) {
                    const detailsElement = element.querySelector('.note-details');
                    if (detailsElement) {
                         console.log("Updating note details in place.");
                         // Re-render just the details span content
                         // Check if this is a global note
                         const isNoteGlobal = noteToMove.data.isGlobal === true || noteToMove.key.includes('global_note');

                         let detailsHTML = `<span class="detail-index">Note ${noteToMove.index}${noteToMove.data.globallyPinned ? ' <span title="Important Note">⭐</span>' : ''}${isNoteGlobal ? ' <span title="Global Note (works across all URLs)">🌐</span>' : ''}</span>`;
                         const notebook = notebooks.find(nb => nb.id === targetNotebookId);
                         const notebookNameDisplay = notebook ? notebook.name.replace(/</g, "<").replace(/>/g, ">") : 'Ungrouped';
                         detailsHTML += `<span class="detail-notebook" title="Notebook: ${notebookNameDisplay}">Notebook: ${notebookNameDisplay}</span>`;
                         if (noteToMove.data.tags?.length) { detailsHTML += `<span class="detail-tags" title="Tags: ${noteToMove.data.tags.join(', ')}">Tags: ${noteToMove.data.tags.map(t => String(t).replace(/</g, "<").replace(/>/g, ">")).join(', ')}</span>`; }
                         if (noteToMove.data.reminder) {
                             try {
                                 const calendarIcon = noteToMove.data.calendarEventId ? ' 📅' : '';
                                 const calendarTitle = noteToMove.data.calendarEventId ? ' (Added to Google Calendar)' : '';
                                 detailsHTML += `<span class="detail-reminder" title="Reminder: ${new Date(noteToMove.data.reminder).toLocaleString()}${calendarTitle}">Reminder: ${new Date(noteToMove.data.reminder).toLocaleString()}${calendarIcon}</span>`;
                             } catch(e){}
                         }
                         if (noteToMove.data.lastSaved) { try {detailsHTML += `<span class="detail-saved" title="Saved: ${new Date(noteToMove.data.lastSaved).toLocaleString()}">Saved: ${new Date(noteToMove.data.lastSaved).toLocaleString()}</span>`; } catch(e){} }
                         detailsElement.innerHTML = detailsHTML;
                    } else {
                         console.warn("Could not find details span to update in place. Falling back to full render.");
                         filterAndRenderNotes(); // Fallback if details span isn't found
                    }
                 } else {
                     console.warn("Could not find note element to update in place. Falling back to full render.");
                     filterAndRenderNotes(); // Fallback if element isn't found (shouldn't happen often)
                 }
             }

             // 3. Update the data in storage
             showStatus(`Moving note...`, 'info');
             // Fetch the most current version from storage before updating
             const noteResult = await chrome.storage.local.get(noteToMove.key);
             const freshNoteData = noteResult[noteToMove.key];

             if (!freshNoteData) {
                 // This indicates a potential data inconsistency issue
                 throw new Error(`Failed to fetch current data for note ${noteToMove.key} from storage before moving.`);
             }
             // Update the notebookId in the fetched data
             freshNoteData.notebookId = targetNotebookId;
             // **Important**: Do *not* modify lastSaved time during a move operation.
             await chrome.storage.local.set({ [noteToMove.key]: freshNoteData });
             showStatus('Note moved successfully.', 'success');

              // 4. Trigger background upload if sync enabled (send the *updated* note data)
              const syncEnabledResult = await chrome.storage.local.get('driveSyncEnabled');
              if (syncEnabledResult.driveSyncEnabled) {
                   console.log("Dashboard: Triggering Drive upload after note move.");
                    await chrome.runtime.sendMessage({
                        action: 'uploadNoteToDrive',
                        noteKey: noteToMove.key,
                        noteData: freshNoteData // Send the data with the updated notebookId
                    });
               }

         } catch (error) {
            console.error("Error moving note:", error);
            showStatus(`Error moving note: ${error.message}`, 'error');
             // Attempt to revert local cache on error to reflect reality
             if (noteToMove) {
                 noteToMove.notebookId = originalNotebookId;
                 noteToMove.data.notebookId = originalNotebookId;
             }
             filterAndRenderNotes(); // Re-render to show original state (or best guess)
        } finally {
             // Ensure draggedNoteKey is reset regardless of success/failure in the drop handler
             // Note: handleNoteDragEnd *also* resets this, but resetting here ensures it happens
             // even if dragend doesn't fire correctly in all scenarios.
             // draggedNoteKey = null; // Let dragEnd handle this.
        }
     }

     // --- Auto-scroll functionality for drag and drop ---

     function initAutoScroll() {
         // Add mousemove listener to track cursor position during drag
         document.addEventListener('dragover', handleDragOverForAutoScroll, { passive: false });
         autoScrollSpeed = AUTO_SCROLL_CONFIG.speed; // Reset speed
     }

     function cleanupAutoScroll() {
         // Remove mousemove listener
         document.removeEventListener('dragover', handleDragOverForAutoScroll);
         // Clear any active auto-scroll interval
         if (autoScrollInterval) {
             clearInterval(autoScrollInterval);
             autoScrollInterval = null;
         }
         // Remove visual indicator classes
         document.body.classList.remove('near-top', 'near-bottom');
         autoScrollSpeed = AUTO_SCROLL_CONFIG.speed; // Reset speed
     }

     function handleDragOverForAutoScroll(e) {
         if (!draggedNoteKey && !draggedHighlightId) return; // Only during active drag operations

         const { clientY } = e;
         const viewportHeight = window.innerHeight;
         const threshold = AUTO_SCROLL_CONFIG.threshold;

         // Check if cursor is near top or bottom edge
         const nearTop = clientY < threshold;
         const nearBottom = clientY > (viewportHeight - threshold);

         // Update body classes for visual indicators
         document.body.classList.toggle('near-top', nearTop);
         document.body.classList.toggle('near-bottom', nearBottom);

         if (nearTop || nearBottom) {
             startAutoScroll(nearTop ? 'up' : 'down', viewportHeight);
         } else {
             stopAutoScroll();
         }

         // Also check if we need to make notebook section more visible
         updateNotebookVisibility(viewportHeight);
     }

     function startAutoScroll(direction, viewportHeight) {
         if (autoScrollInterval) return; // Already scrolling

         autoScrollInterval = setInterval(() => {
             const scrollAmount = direction === 'up' ? -autoScrollSpeed : autoScrollSpeed;
             window.scrollBy(0, scrollAmount);

             // Increase scroll speed for smoother experience
             if (autoScrollSpeed < AUTO_SCROLL_CONFIG.maxSpeed) {
                 autoScrollSpeed *= AUTO_SCROLL_CONFIG.acceleration;
             }

             // Check if we've reached the edge and should stop
             const atTop = direction === 'up' && window.scrollY <= 0;
             const atBottom = direction === 'down' &&
                 (window.scrollY + viewportHeight >= document.documentElement.scrollHeight);

             if (atTop || atBottom) {
                 stopAutoScroll();
             }
         }, 16); // ~60fps
     }

     function stopAutoScroll() {
         if (autoScrollInterval) {
             clearInterval(autoScrollInterval);
             autoScrollInterval = null;
         }
         autoScrollSpeed = AUTO_SCROLL_CONFIG.speed; // Reset speed
     }

     function updateNotebookVisibility(viewportHeight) {
         if (!notebookSidebar) return;

         const sidebarRect = notebookSidebar.getBoundingClientRect();
         const isNotebookVisible = sidebarRect.top < viewportHeight && sidebarRect.bottom > 0;

         // Add enhanced visibility class if notebook is not fully visible
         if (!isNotebookVisible || sidebarRect.top < 0 || sidebarRect.bottom > viewportHeight) {
             notebookSidebar.classList.add('enhanced-visibility');
         } else {
             notebookSidebar.classList.remove('enhanced-visibility');
         }
     }


     // --- Rename Modal Handlers ---

     function openRenameModal(notebookId, currentName) {
        if(!renameModal || !renameInput || !renameIdInput || !renameSaveButton) {
            console.error("Cannot open rename modal: Required elements missing.");
            return;
        }
        renameInput.value = currentName; // Populate with current name
        renameIdInput.value = notebookId; // Store the ID in the hidden input
        renameSaveButton.disabled = false; // Ensure save button is enabled
        renameModal.style.display = 'flex'; // Show the modal
        renameInput.focus(); // Focus the input field
        renameInput.select(); // Select the text for easy replacement
        console.log(`Opening rename modal for notebook ID: ${notebookId}, Name: "${currentName}"`);
    }

    function closeRenameModal() {
        if(!renameModal) return;
        renameModal.style.display = 'none'; // Hide the modal
        // Optionally clear the inputs after closing
         // renameInput.value = '';
         // renameIdInput.value = '';
         console.log("Closing rename modal.");
    }

    // --- Event Listeners Setup ---

    // Tab switching listeners
    if (notesTabBtn) notesTabBtn.addEventListener('click', () => switchTab('notes'));
    if (highlightsTabBtn) highlightsTabBtn.addEventListener('click', () => switchTab('highlights'));

    /** Switch between notes and highlights tabs */
    function switchTab(tabName) {
        // Update active tab state
        activeTab = tabName;

        // Update tab buttons
        if (notesTabBtn) notesTabBtn.classList.toggle('active', tabName === 'notes');
        if (highlightsTabBtn) highlightsTabBtn.classList.toggle('active', tabName === 'highlights');

        // Update tab content visibility
        if (notesTabContent) notesTabContent.classList.toggle('active', tabName === 'notes');
        if (highlightsTabContent) highlightsTabContent.classList.toggle('active', tabName === 'highlights');

        // Update content areas visibility
        if (notesArea) notesArea.style.display = tabName === 'notes' ? 'block' : 'none';
        if (highlightsArea) highlightsArea.style.display = tabName === 'highlights' ? 'block' : 'none';

        // Clear selections and hide bulk action bars when switching tabs
        if (tabName === 'notes') {
            selectedHighlightIds.clear();
            updateHighlightsBulkActionsBar();
        } else if (tabName === 'highlights') {
            selectedNoteKeys.clear();
            updateBulkActionsBar();
        }

        // Load data for the selected tab if needed
        if (tabName === 'notes') {
            // Notes tab is already loaded on initialization
            console.log(`Dashboard: Switched to notes tab. Total notes: ${allNotesData.length}`);
            updateNotesCountDisplay();
        } else if (tabName === 'highlights') {
            // Highlights data should already be loaded during initialization
            // But if for some reason it's not loaded, load it now
            if (allHighlightsData.length === 0) {
                console.log("Dashboard: Highlights data not loaded, loading now...");
                loadAllHighlights();
            } else {
                // Just render the existing highlights data
                console.log(`Dashboard: Switched to highlights tab. Total highlights: ${allHighlightsData.length}`);
                filterAndRenderHighlights();
            }
            updateHighlightsCountDisplay();
        }

        // Update URL hash to reflect current tab (for bookmarking/sharing)
        updateUrlHash();

        // Show status message about tab switch
        showStatus(`Switched to ${tabName} tab`, 'info', 1500);
    }

    /**
     * Updates the URL hash to reflect current tab and viewing state
     */
    function updateUrlHash() {
        let hash = `#tab=${activeTab}`;

        // Add current viewing content to hash for deep linking
        if (currentViewingNoteKey) {
            hash += `&note=${encodeURIComponent(currentViewingNoteKey)}`;
        } else if (currentViewingHighlightId) {
            hash += `&highlight=${encodeURIComponent(currentViewingHighlightId)}`;
        }

        // Add current page if not first page
        if (activeTab === 'notes' && currentPage > 1) {
            hash += `&page=${currentPage}`;
        } else if (activeTab === 'highlights' && highlightsCurrentPage > 1) {
            hash += `&page=${highlightsCurrentPage}`;
        }

        // Update URL without triggering page reload
        if (window.location.hash !== hash) {
            window.history.replaceState(null, null, hash);
        }
    }

    /**
     * Updates the notes count display
     */
    function updateNotesCountDisplay() {
        const notesCountDisplay = document.getElementById('notes-count-display');
        if (notesCountDisplay) {
            const filteredNotes = getFilteredNotes();
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredNotes.length);

            if (filteredNotes.length === 0) {
                notesCountDisplay.textContent = 'No notes found';
            } else {
                notesCountDisplay.textContent = `Showing ${startIndex}-${endIndex} of ${filteredNotes.length} notes`;
            }
        }
    }

    /**
     * Updates the highlights count display
     */
    function updateHighlightsCountDisplay() {
        const highlightsCountDisplay = document.getElementById('highlights-count-display');
        if (highlightsCountDisplay) {
            const filteredHighlights = getFilteredHighlights();
            const startIndex = (highlightsCurrentPage - 1) * highlightsPageSize + 1;
            const endIndex = Math.min(highlightsCurrentPage * highlightsPageSize, filteredHighlights.length);

            if (filteredHighlights.length === 0) {
                highlightsCountDisplay.textContent = 'No highlights found';
            } else {
                highlightsCountDisplay.textContent = `Showing ${startIndex}-${endIndex} of ${filteredHighlights.length} highlights`;
            }
        }
    }

    /**
     * Initializes the dashboard based on URL hash parameters
     */
    function initializeFromUrlHash() {
        const hash = window.location.hash;
        if (!hash) return;

        const params = new URLSearchParams(hash.substring(1));

        // Set active tab from URL
        const tabFromUrl = params.get('tab');
        if (tabFromUrl && (tabFromUrl === 'notes' || tabFromUrl === 'highlights')) {
            activeTab = tabFromUrl;
            switchTab(activeTab);
        }

        // Set page from URL
        const pageFromUrl = parseInt(params.get('page'), 10);
        if (pageFromUrl && pageFromUrl > 0) {
            if (activeTab === 'notes') {
                currentPage = pageFromUrl;
            } else if (activeTab === 'highlights') {
                highlightsCurrentPage = pageFromUrl;
            }
        }

        // Open specific note or highlight from URL
        const noteFromUrl = params.get('note');
        const highlightFromUrl = params.get('highlight');

        if (noteFromUrl) {
            // Delay opening to ensure data is loaded
            setTimeout(() => {
                const decodedNoteKey = decodeURIComponent(noteFromUrl);
                if (allNotesData.find(n => n.key === decodedNoteKey)) {
                    openNoteInModal(decodedNoteKey);
                }
            }, 500);
        } else if (highlightFromUrl) {
            // Delay opening to ensure data is loaded
            setTimeout(() => {
                const decodedHighlightId = decodeURIComponent(highlightFromUrl);
                if (allHighlightsData.find(h => h.highlightId === decodedHighlightId)) {
                    openHighlightInModal(decodedHighlightId);
                }
            }, 500);
        }
    }

    // Enhanced Notes filter/sort listeners with error handling
    if (searchInput) {
        searchInput.addEventListener('input', () => {
            try {
                debouncedFilter();
            } catch (error) {
                console.error("Error in notes search input handler:", error);
                showStatus("Search error occurred. Please try again.", 'error', 3000);
            }
        });
    } else {
        console.warn("Dashboard: Notes search input not found - search functionality may not work");
    }

    if (sortBySelect) {
        sortBySelect.addEventListener('change', () => {
            try {
                filterAndRenderNotes();
            } catch (error) {
                console.error("Error in notes sort handler:", error);
                showStatus("Sort error occurred. Please try again.", 'error', 3000);
            }
        });
    }

    // --- REINSTATED Checkbox Listeners with error handling ---
    if (reminderFilterCheckbox) {
        reminderFilterCheckbox.addEventListener('change', () => {
            try {
                filterAndRenderNotes();
            } catch (error) {
                console.error("Error in reminder filter handler:", error);
                showStatus("Filter error occurred. Please try again.", 'error', 3000);
            }
        });
    }

    if (globalPinFilterCheckbox) {
        globalPinFilterCheckbox.addEventListener('change', () => {
            try {
                filterAndRenderNotes();
            } catch (error) {
                console.error("Error in global pin filter handler:", error);
                showStatus("Filter error occurred. Please try again.", 'error', 3000);
            }
        });
    }
    // --- END Enhanced Checkbox Listeners ---



    // Side-by-Side View toggle button
    if (sideBySideViewBtn) {
        sideBySideViewBtn.addEventListener('click', () => {
            isSideBySideView = !isSideBySideView; // Toggle the state
            sideBySideViewBtn.classList.toggle('active', isSideBySideView); // Update button appearance
            filterAndRenderNotes(); // Re-render notes with the new view
        });
    }

    // View options for notes
    const dashboardViewOption = document.getElementById('dashboard-view-option');
    if (dashboardViewOption) {
        dashboardViewOption.addEventListener('change', filterAndRenderNotes);
    }

    // Enhanced Highlights filter/sort listeners with error handling
    if (highlightsSearchInput) {
        highlightsSearchInput.addEventListener('input', () => {
            try {
                debouncedHighlightsFilter();
            } catch (error) {
                console.error("Error in highlights search input handler:", error);
                showStatus("Highlights search error occurred. Please try again.", 'error', 3000);
            }
        });
    } else {
        console.warn("Dashboard: Highlights search input not found - highlights search functionality may not work");
    }

    // Enhanced Color and style filter listeners
    if (highlightColorFilter) {
        highlightColorFilter.addEventListener('change', () => {
            try {
                filterAndRenderHighlights();
            } catch (error) {
                console.error("Error in highlight color filter handler:", error);
                showStatus("Color filter error occurred. Please try again.", 'error', 3000);
            }
        });
    }

    if (highlightStyleFilter) {
        highlightStyleFilter.addEventListener('change', () => {
            try {
                filterAndRenderHighlights();
            } catch (error) {
                console.error("Error in highlight style filter handler:", error);
                showStatus("Style filter error occurred. Please try again.", 'error', 3000);
            }
        });
    }

    // Enhanced Notes filter listener
    if (highlightNotesFilter) {
        highlightNotesFilter.addEventListener('change', () => {
            try {
                filterAndRenderHighlights();
            } catch (error) {
                console.error("Error in highlight notes filter handler:", error);
                showStatus("Notes filter error occurred. Please try again.", 'error', 3000);
            }
        });
    }

    // Enhanced View options filter
    const highlightsViewOption = document.getElementById('highlights-view-option');
    if (highlightsViewOption) {
        highlightsViewOption.addEventListener('change', () => {
            try {
                filterAndRenderHighlights();
            } catch (error) {
                console.error("Error in highlights view option handler:", error);
                showStatus("View option error occurred. Please try again.", 'error', 3000);
            }
        });
    }

    if (highlightsSortBySelect) highlightsSortBySelect.addEventListener('change', filterAndRenderHighlights);

    // Notes pagination listeners
    if (prevPageBtn) prevPageBtn.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            filterAndRenderNotes();
            updateUrlHash(); // Update URL to reflect page change
        }
    });

    if (nextPageBtn) nextPageBtn.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            filterAndRenderNotes();
            updateUrlHash(); // Update URL to reflect page change
        }
    });

    if (pageSizeSelect) pageSizeSelect.addEventListener('change', () => {
        pageSize = parseInt(pageSizeSelect.value, 10);
        currentPage = 1; // Reset to first page when changing page size
        filterAndRenderNotes();
        updateUrlHash(); // Update URL to reflect page size change
    });

    // Highlights pagination listeners
    if (highlightsPrevPageBtn) highlightsPrevPageBtn.addEventListener('click', () => {
        if (highlightsCurrentPage > 1) {
            highlightsCurrentPage--;
            filterAndRenderHighlights();
            updateUrlHash(); // Update URL to reflect page change
        }
    });

    if (highlightsNextPageBtn) highlightsNextPageBtn.addEventListener('click', () => {
        if (highlightsCurrentPage < highlightsTotalPages) {
            highlightsCurrentPage++;
            filterAndRenderHighlights();
            updateUrlHash(); // Update URL to reflect page change
        }
    });

    if (highlightsPageSizeSelect) highlightsPageSizeSelect.addEventListener('change', () => {
        highlightsPageSize = parseInt(highlightsPageSizeSelect.value, 10);
        highlightsCurrentPage = 1; // Reset to first page when changing page size
        filterAndRenderHighlights();
        updateUrlHash(); // Update URL to reflect page size change
    });

    // Notes select all checkbox listener
    if (selectAllCheckbox) selectAllCheckbox.addEventListener('change', () => {
        const isChecked = selectAllCheckbox.checked;
        const noteItems = resultsDiv.querySelectorAll('.note-item');

        // Clear current selection
        if (!isChecked) {
            selectedNoteKeys.clear();
        } else {
            // Add all visible notes to selection
            noteItems.forEach(item => {
                const key = item.dataset.noteKey;
                if (key) {
                    selectedNoteKeys.add(key);

                    // Update checkbox state
                    const checkbox = item.querySelector('.note-select-checkbox');
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                }
            });
        }

        // Update checkboxes to match select all state
        noteItems.forEach(item => {
            const checkbox = item.querySelector('.note-select-checkbox');
            if (checkbox) {
                checkbox.checked = isChecked;
            }
        });

        updateBulkActionsBar();
    });

    // Highlights select all checkbox listener
    if (selectAllHighlightsCheckbox) selectAllHighlightsCheckbox.addEventListener('change', () => {
        const isChecked = selectAllHighlightsCheckbox.checked;
        const highlightItems = highlightsResultsDiv.querySelectorAll('.highlight-item');

        // Clear current selection
        if (!isChecked) {
            selectedHighlightIds.clear();
        } else {
            // Add all visible highlights to selection
            highlightItems.forEach(item => {
                const id = item.dataset.highlightId;
                if (id) {
                    selectedHighlightIds.add(id);

                    // Update checkbox state
                    const checkbox = item.querySelector('.highlight-checkbox');
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                }
            });
        }

        // Update checkboxes to match select all state
        highlightItems.forEach(item => {
            const checkbox = item.querySelector('.highlight-checkbox');
            if (checkbox) {
                checkbox.checked = isChecked;
            }
        });

        updateHighlightsBulkActionsBar();
    });

    // Notebook List Click Delegation (Handles clicks on sidebar items)
    if (notebookListUl) notebookListUl.addEventListener('click', (event) => {
         // Find the closest ancestor button with a notebook ID
        const notebookButton = event.target.closest('.notebook-button[data-notebook-id]');

        // Ignore clicks if not on a notebook button or its children
        if (!notebookButton) return;

        // Check if the click was on a control button (rename/delete) within the notebook button
        const isRenameControl = event.target.closest('.notebook-controls button[title="Rename Notebook"]');
        const isDeleteControl = event.target.closest('.notebook-controls button[title="Delete Notebook"]');

        // If click was on rename or delete, their inline `onclick` handlers manage it.
        // We stop further processing here to prevent filtering when clicking controls.
        if (isRenameControl || isDeleteControl) {
            console.log(`Control button clicked (handled inline): ${isRenameControl ? 'Rename' : 'Delete'}`);
            return;
        }

        // Click was on the main notebook button area (not controls) -> Change filter
         const notebookId = notebookButton.dataset.notebookId;
         // Map 'ALL' to null for the filter state, otherwise use the ID ('UNGROUPED' or specific ID)
         currentNotebookFilter = (notebookId === 'ALL') ? null : notebookId;

         console.log(`Notebook filter changed to: ${currentNotebookFilter} (Clicked on ID: ${notebookId})`);

         // Update UI: Highlight active notebook, render notes for the new filter
         renderNotebookList(); // Re-render sidebar to update active state

         // Reset pagination to first page when changing notebook
         currentPage = 1;
         highlightsCurrentPage = 1; // Also reset highlights pagination

         filterAndRenderNotes(); // Refresh notes list
         filterAndRenderHighlights(); // Also refresh highlights list

         // Clear bulk selection when changing notebook view
         selectedNoteKeys.clear();
         selectedHighlightIds.clear(); // Also clear highlight selection
         updateBulkActionsBar();
         updateHighlightsBulkActionsBar();
    });

    // Add Notebook Button
    if (addNotebookBtn) addNotebookBtn.addEventListener('click', () => {
         const name = prompt("Enter name for the new notebook:");
         if (name !== null && name.trim() !== '') { // Check for null (cancel) and empty string
             createNotebook(name); // Call create function if name is provided
         } else if (name !== null) { // User entered empty string or only whitespace
             showStatus("Notebook name cannot be empty.", "warning");
         } // If name is null, user cancelled, do nothing.
    });

    // Rename Modal Save Button
    if (renameSaveButton) renameSaveButton.addEventListener('click', async () => {
        renameSaveButton.disabled = true; // Prevent double-click
        const id = renameIdInput.value;
        const newName = renameInput.value;
        const success = await renameNotebook(id, newName);
        if(success) {
            closeRenameModal(); // Close modal only on successful rename
        }
        // Re-enable button regardless of success/failure, allowing user to try again
        renameSaveButton.disabled = false;
    });
     // Rename Modal Input Enter Key submits the form
     if (renameInput) renameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && renameSaveButton && !renameSaveButton.disabled) {
             renameSaveButton.click(); // Trigger click on the save button
        }
     });
    // Rename Modal Close Button/Background Click
     if (modalCloseButton) modalCloseButton.addEventListener('click', closeRenameModal);
     if (renameModal) renameModal.addEventListener('click', (e) => {
         // Close modal if user clicks on the modal background (the modal element itself)
         if (e.target === renameModal) {
            closeRenameModal();
         }
     });
     // Rename Modal Escape Key closes the modal
     window.addEventListener('keydown', (e) => {
         if (e.key === 'Escape' && renameModal?.style.display === 'flex') {
            closeRenameModal();
         }
     });

    // --- Bulk Actions Event Listeners ---

    // Bulk Delete Button
    if (bulkDeleteButton) bulkDeleteButton.addEventListener('click', async () => {
        const keysToDelete = Array.from(selectedNoteKeys);
        if (keysToDelete.length === 0) {
            showStatus("No notes selected for deletion.", "warning");
            return;
        }
        // Confirmation dialog
        if (!confirm(`Are you sure you want to permanently delete ${keysToDelete.length} selected note(s)? This action cannot be undone.`)) {
            return; // User cancelled
        }

        // Disable controls during operation
        bulkDeleteButton.disabled = true;
        if (bulkExportButton) bulkExportButton.disabled = true;
        if (bulkExportFormatSelect) bulkExportFormatSelect.disabled = true;
        if (bulkMoveDropdownButton) bulkMoveDropdownButton.disabled = true;
        showStatus(`Deleting ${keysToDelete.length} note(s)...`, 'info');

        try {
             // Remove notes from storage
             await chrome.storage.local.remove(keysToDelete);
             showStatus(`Successfully deleted ${keysToDelete.length} note(s).`, 'success');

             // Clear the selection set
             selectedNoteKeys.clear();

             // Remove deleted notes from the `allNotesData` cache
             allNotesData = allNotesData.filter(note => !keysToDelete.includes(note.key));

             // Re-render the note list with remaining notes
             filterAndRenderNotes(); // This also calls updateBulkActionsBar

             // Optional: Trigger Drive deletions if sync is enabled
             const syncEnabledResult = await chrome.storage.local.get('driveSyncEnabled');
             if (syncEnabledResult.driveSyncEnabled) {
                 console.log(`Triggering background deletions for ${keysToDelete.length} notes...`);
                 await chrome.runtime.sendMessage({ action: 'deleteNotesFromDrive', noteKeys: keysToDelete });
                 console.log("Finished triggering background deletions.");
             }

        } catch (error) {
            console.error("Error deleting notes:", error);
            showStatus(`Error deleting notes: ${error.message || error}`, 'error', 5000);
            // Re-enable controls based on whether selection theoretically still exists
            // (it shouldn't if deletion failed properly, but as a fallback)
            updateBulkActionsBar();
        }
        // No finally block needed, updateBulkActionsBar is called by filterAndRenderNotes
    });

    // Bulk Export Button
     if (bulkExportButton && bulkExportFormatSelect) bulkExportButton.addEventListener('click', async () => {
         const keysToExport = Array.from(selectedNoteKeys);
         if (keysToExport.length === 0) {
             showStatus("No notes selected for export.", "warning");
             return;
         }
         const selectedFormat = bulkExportFormatSelect.value;

         // Disable controls during preparation
          if (bulkDeleteButton) bulkDeleteButton.disabled = true;
          bulkExportButton.disabled = true;
          bulkExportFormatSelect.disabled = true;
          if (bulkMoveDropdownButton) bulkMoveDropdownButton.disabled = true;
          showStatus(`Preparing export (${selectedFormat.toUpperCase()})...`, 'info');

         // Get the full data for the selected notes from the cache
         const notesToExport = allNotesData.filter(note => keysToExport.includes(note.key));

         if (notesToExport.length === 0) {
            // Should not happen if keysToExport is populated, but check anyway
            showStatus('Selected notes not found in cache for export.', 'error');
            updateBulkActionsBar(); // Re-enable buttons
            return;
         }

         // --- Format the export content based on selection ---
         let fileContent = '', fileExtension = '.txt', mimeType = 'text/plain;charset=utf-8';
         const pageTitleBase = 'Stickara Notes';
         const timestamp = new Date().toISOString().replace(/:/g, '-').slice(0, 19); // YYYY-MM-DDTHH-MM-SS

         if (selectedFormat === 'md') {
             fileExtension = '.md';
             mimeType = 'text/markdown;charset=utf-8';
             const noteSeparator = '\n\n---\n\n';

             // Create a more visually appealing Markdown header
             fileContent = `# ${pageTitleBase}\n\n`;
             // Ensure date is valid before using toLocaleString
             const exportDate = new Date(timestamp);
             const exportDateText = !isNaN(exportDate.getTime())
                 ? `> *Exported on: ${exportDate.toLocaleString()}*\n\n`
                 : `> *Exported on: ${timestamp}*\n\n`;
             fileContent += exportDateText;
             fileContent += `*This document contains ${notesToExport.length} exported notes from Stickara.*\n\n`;
             fileContent += `---\n\n`;

             fileContent += notesToExport.map(n => {
                 const title = n.data.title || n.decodedUrl || '[No Title/URL]';
                 const notebookName = notebooks.find(nb => nb.id === n.notebookId)?.name || 'Ungrouped';

                 // Format tags with emoji and better styling
                 const tags = n.data.tags?.length
                     ? `\n\n🏷️ **Tags:** *${n.data.tags.join('*, *')}*`
                     : '';

                 // Add emoji and better formatting to metadata
                 // Ensure date is valid before using toLocaleDateString
                 const currentDate = new Date();
                 const exportDateText = !isNaN(currentDate.getTime())
                     ? currentDate.toLocaleDateString()
                     : new Date().toISOString().split('T')[0];

                 const metadata = `
🔗 **URL:** ${n.decodedUrl || '[No URL]'}
📁 **Notebook:** ${notebookName}
📅 **Exported:** ${exportDateText}
`;

                 // Use 'md' format to properly handle images in Markdown
                 return `## 📝 Note ${n.index}: ${title}\n${metadata}\n${getPlainText(n.data.text, 'md')}${tags}`;
             }).join(noteSeparator);

             // Add footer with proper date formatting
             const footerDate = new Date();
             const footerDateText = !isNaN(footerDate.getTime())
                 ? footerDate.toLocaleDateString()
                 : new Date().toISOString().split('T')[0];

             fileContent += `\n\n---\n\n*Generated by Stickara on ${footerDateText}*`;
         } else if (selectedFormat === 'html') {
             fileExtension = '.html';
             mimeType = 'text/html;charset=utf-8';
             fileContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitleBase}</title>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #6b8cae;
            --accent-color: #ff7e5f;
            --text-color: #333333;
            --light-bg: #f8f9fa;
            --border-color: #e0e0e0;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #ffffff;
            padding: 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: 8px 8px 0 0;
            margin: 20px 20px 0 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 2.2rem;
            font-weight: 600;
        }

        .header .export-date {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .container {
            padding: 20px;
        }

        article {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        article:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        article h2 {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 1rem;
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        article .meta {
            background-color: var(--light-bg);
            padding: 0.8rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        article .meta p {
            margin: 0.3rem 0;
        }

        article .meta a {
            color: var(--primary-color);
            text-decoration: none;
        }

        article .meta a:hover {
            text-decoration: underline;
        }

        article .content {
            margin-top: 1.5rem;
        }

        article .tags {
            margin-top: 1.5rem;
            font-size: 0.9rem;
        }

        article .tags strong {
            color: var(--secondary-color);
        }

        hr {
            border: none;
            height: 1px;
            background-color: var(--border-color);
            margin: 2rem 0;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: var(--light-bg);
            padding: 1rem;
            border-radius: 6px;
            border-left: 3px solid var(--accent-color);
            font-family: 'Courier New', Courier, monospace;
            margin: 1rem 0;
        }

        code {
            font-family: 'Courier New', Courier, monospace;
            background-color: var(--light-bg);
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
        }

        blockquote {
            border-left: 4px solid var(--secondary-color);
            padding-left: 1rem;
            margin-left: 0;
            color: #555;
            font-style: italic;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1.5rem;
                margin: 10px 10px 0 10px;
            }

            .container {
                padding: 10px;
            }

            article {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${pageTitleBase}</h1>
        <div class="export-date">Exported on: ${(() => {
            const exportDate = new Date(timestamp);
            return !isNaN(exportDate.getTime())
                ? exportDate.toLocaleString()
                : timestamp;
        })()}</div>
    </div>
    <div class="container">`;
             fileContent += notesToExport.map(n => {
                 const title = n.data.title || n.decodedUrl || '[No Title/URL]';
                 const notebookName = notebooks.find(nb => nb.id === n.notebookId)?.name || 'Ungrouped';
                 const tags = n.data.tags?.length ? `<p><strong>Tags:</strong> ${n.data.tags.join(', ')}</p>` : '';
                 // Assume n.data.text is HTML content, display it within a div
                 return `<article>
    <h2>Note ${n.index} (${title})</h2>
    <div class="meta">
        <p><strong>URL:</strong> <a href="${n.decodedUrl || '#'}" target="_blank">${n.decodedUrl || '[No URL]'}</a></p>
        <p><strong>Notebook:</strong> ${notebookName}</p>
        ${n.data.lastSaved ? `<p><strong>Last Modified:</strong> ${new Date(n.data.lastSaved).toLocaleString()}</p>` : ''}
    </div>
    <div class="content">${n.data.text || '[No Content]'}</div>
    ${tags ? `<div class="tags">${tags}</div>` : ''}
</article>`;
             }).join('');
             fileContent += `
    </div>
    <footer style="text-align: center; padding: 20px; color: #666; font-size: 0.9rem; margin-top: 30px; border-top: 1px solid var(--border-color);">
        <p>Generated by Stickara on ${(() => {
            const footerDate = new Date();
            return !isNaN(footerDate.getTime())
                ? footerDate.toLocaleDateString()
                : new Date().toISOString().split('T')[0];
        })()}</p>
    </footer>
</body>
</html>`;
         } else if (selectedFormat === 'pdf') {
            fileExtension = '.pdf';
            mimeType = 'application/pdf';

            // Check if required libraries are available
            if (typeof window.jspdf === 'undefined') {
                showStatus('PDF library not loaded.', 'error');
                console.error("jsPDF library is required for PDF export but not loaded.");
                updateBulkActionsBar(); // Re-enable buttons
                return;
            }

            if (typeof html2canvas === 'undefined') {
                showStatus('HTML2Canvas library not loaded.', 'error');
                console.error("html2canvas library is required for styled PDF export but not loaded.");
                updateBulkActionsBar(); // Re-enable buttons
                return;
            }

            // Generate the same beautiful HTML content as HTML export
            let htmlBodyContent = `
<div class="header">
    <h1>Stickara Notes</h1>
    <div class="export-date">Exported on: ${(() => {
        const exportDate = new Date(timestamp);
        return !isNaN(exportDate.getTime())
            ? exportDate.toLocaleString()
            : timestamp;
    })()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">📝</div>
        <div class="meta-content">
            <div class="meta-label">Total Notes</div>
            <div class="meta-value">${notesToExport.length}</div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">📅</div>
        <div class="meta-content">
            <div class="meta-label">Export Date</div>
            <div class="meta-value">${(() => {
                const exportDate = new Date(timestamp);
                return !isNaN(exportDate.getTime())
                    ? exportDate.toLocaleDateString()
                    : new Date().toISOString().split('T')[0];
            })()}</div>
        </div>
    </div>
</div>`;

            // Add notes to the HTML content
            notesToExport.forEach((n) => {
                const title = n.data.title || n.decodedUrl || '[No Title/URL]';
                const notebookName = notebooks.find(nb => nb.id === n.notebookId)?.name || 'Ungrouped';

                // Handle important note status
                const globalStatus = n.data.globallyPinned
                    ? `<div class="note-global-pin">IMPORTANT NOTE</div>`
                    : "";

                // Format metadata
                const savedDate = n.data.lastSaved ? new Date(n.data.lastSaved) : null;
                const savedText = savedDate && !isNaN(savedDate.getTime())
                    ? `<div class="meta-item"><span class="meta-label">Last Saved:</span> ${savedDate.toLocaleString()}</div>`
                    : "";

                const reminderDate = n.data.reminder ? new Date(n.data.reminder) : null;
                const reminderText = reminderDate && !isNaN(reminderDate.getTime())
                    ? `<div class="meta-item"><span class="meta-label">Reminder:</span> ${reminderDate.toLocaleString()}</div>`
                    : "";

                const tagsText = n.data.tags?.length
                    ? `<div class="meta-item"><span class="meta-label">Tags:</span> ${n.data.tags.join(', ')}</div>`
                    : "";

                const urlText = `<div class="meta-item"><span class="meta-label">URL:</span> ${n.decodedUrl || '[No URL]'}</div>`;
                const notebookText = `<div class="meta-item"><span class="meta-label">Notebook:</span> ${notebookName}</div>`;

                const metadata = `<div class="note-metadata">${[urlText, notebookText, tagsText, reminderText, savedText].filter(Boolean).join('')}</div>`;

                // Process the note content to ensure images are preserved
                let noteContent = n.data.text || '';

                // Create a note card with the content
                htmlBodyContent += `
<div class="note-card">
    <div class="note-header">
        <div class="note-title">Note ${n.index}: ${title}</div>
        ${globalStatus}
    </div>
    <div class="note-content">
        ${noteContent}
    </div>
    ${metadata}
</div>`;
            });

            // Add CSS styling (same as popup notes export)
            const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        min-height: 100vh;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-right: 5px;
        font-weight: bold;
    }

    .meta-value {
        font-weight: 500;
    }

    .note-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 25px 30px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid var(--border-color);
    }

    .note-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }

    .note-title {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--primary-color);
    }

    .note-global-pin {
        background-color:rgb(203, 193, 5);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 400;
    }

    .note-content {
        background-color: var(--light-bg);
        padding: 1.8rem;
        border-radius: 8px;
        margin-bottom: 20px;
        line-height: 1.8;
        border-left: 5px solid var(--accent-color);
        border: 1px solid #e8e8e8;
        font-size: 1.05rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .note-content img {
        max-width: 100%;
        height: auto;
        margin: 15px 0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
    }

    .note-metadata {
        font-size: 0.9rem;
        color: #666;
        padding: 15px;
        background-color: var(--light-bg);
        border-radius: 6px;
        border: 1px solid #e8e8e8;
    }

    .note-metadata .meta-item {
        margin-bottom: 8px;
        min-width: auto;
        flex: none;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .note-card {
            margin: 10px;
        }
    }
</style>`;

            // Add footer
            const footer = `
<footer>
    <p>Generated by Stickara on ${(() => {
        const footerDate = new Date();
        return !isNaN(footerDate.getTime())
            ? footerDate.toLocaleDateString()
            : new Date().toISOString().split('T')[0];
    })()}</p>
</footer>`;

            // Combine all parts into complete HTML document
            const completeHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Notes</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

            // Generate filename
            const filename = `Stickara_notes_${timestamp}.pdf`;

            // Convert styled HTML to PDF
            try {
                await convertStyledHtmlToPdf(completeHtmlContent, filename, 'Generating beautiful PDF...');
                updateBulkActionsBar(); // Re-enable buttons
            } catch (pdfError) {
                console.error("Error generating styled PDF:", pdfError);
                showStatus('PDF generation failed.', 'error');
                updateBulkActionsBar(); // Re-enable buttons on error
            }

            // Return early since PDF handles its own download
            return;

        } else { // Default to TXT
            fileExtension = '.txt';
            mimeType = 'text/plain;charset=utf-8';

            // Create a more visually appealing separator with ASCII art
            const noteSeparator = '\n\n' +
                '+=======================================================================+\n' +
                '|                                                                       |\n' +
                '+=======================================================================+\n\n';

            // Create a decorative header
            fileContent =
                '+=======================================================================+\n' +
                '|                         Stickara NOTES                            |\n' +
                '+=======================================================================+\n\n' +
                `${pageTitleBase}\n` +
                `Exported on: ${(() => {
                    const exportDate = new Date(timestamp);
                    return !isNaN(exportDate.getTime())
                        ? exportDate.toLocaleString()
                        : timestamp;
                })()}\n` +
                `Total Notes: ${notesToExport.length}\n\n` +
                '-----------------------------------------------------------------------\n\n';

            fileContent += notesToExport.map(n => {
                const title = n.data.title || '[No Title]';
                const notebookName = notebooks.find(nb => nb.id === n.notebookId)?.name || 'Ungrouped';
                const tags = n.data.tags?.length ? `Tags: ${n.data.tags.join(', ')}` : 'Tags: None';

                // Create a more structured note header
                const noteHeader =
                    `+------------------------[ NOTE ${n.index} ]-------------------------+\n\n` +
                    `TITLE:    ${title}\n` +
                    `URL:      ${n.decodedUrl || '[No URL]'}\n` +
                    `NOTEBOOK: ${notebookName}\n` +
                    `${tags}\n` +
                    `EXPORTED: ${(() => {
                        const exportDate = new Date();
                        return !isNaN(exportDate.getTime())
                            ? exportDate.toLocaleDateString()
                            : new Date().toISOString().split('T')[0];
                    })()}\n\n` +
                    `CONTENT:\n` +
                    `-----------------------------------------------------------------------\n`;

                // Use 'txt' format to properly handle images in plain text
                return `${noteHeader}\n${getPlainText(n.data.text, 'txt')}\n`;
            }).join(noteSeparator);

            // Add footer
            fileContent += '\n\n' +
                '+=======================================================================+\n' +
                '|                    Generated by Stickara                          |\n' +
                '+=======================================================================+\n';
         }

         // --- Trigger Download ---
         try {
             const blob = new Blob([fileContent], { type: mimeType });
             const objectUrl = URL.createObjectURL(blob);
             const filename = `Stickara_notes_${timestamp}${fileExtension}`;

             chrome.downloads.download({
                 url: objectUrl,
                 filename: filename,
                 saveAs: true // Prompt user for save location
             }, (downloadId) => {
                 // Clean up the object URL once the download starts or fails
                 URL.revokeObjectURL(objectUrl);
                 // Re-enable buttons after download attempt starts/fails/cancels
                 updateBulkActionsBar(); // Handles enabling based on selection count

                 if (chrome.runtime.lastError) {
                     console.error(`Export download failed: ${chrome.runtime.lastError.message}`);
                     showStatus(`Export failed: ${chrome.runtime.lastError.message}`, 'error');
                 } else if (downloadId === undefined) {
                     // This typically means the download was cancelled by the user in the 'Save As' dialog
                     console.log('Export download cancelled by user.');
                     showStatus('Export cancelled.', 'info');
                 } else {
                     console.log(`Export download started with ID: ${downloadId}`);
                     showStatus('Export started!', 'success');
                 }
             });
         } catch (error) {
             console.error("Error during export file creation or download trigger:", error);
             showStatus(`Export preparation failed: ${error.message}`, 'error');
             updateBulkActionsBar(); // Re-enable buttons on error
         }
     });


     // --- NEW: Bulk Move Button Listener ---
    if (bulkMoveDropdownButton && bulkMoveOptionsDiv) {
         // Toggle dropdown visibility on button click
         bulkMoveDropdownButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent the document click listener from closing it immediately
             const isShown = bulkMoveOptionsDiv.classList.toggle('show');
             console.log(`Bulk move dropdown ${isShown ? 'shown' : 'hidden'}`);
        });

         // Handle clicks on move options using event delegation on the container
        bulkMoveOptionsDiv.addEventListener('click', async (e) => {
             // Find the actual button element that was clicked inside the dropdown
             const moveButton = e.target.closest('button[data-target-notebook-id]');
             if (!moveButton) return; // Click was not on a move option button

             const targetNotebookIdRaw = moveButton.dataset.targetNotebookId;
             // Convert 'UNGROUPED' to null, otherwise use the ID
             const targetNotebookId = (targetNotebookIdRaw === 'UNGROUPED') ? null : targetNotebookIdRaw;
             const keysToMove = Array.from(selectedNoteKeys); // Get currently selected keys

             bulkMoveOptionsDiv.classList.remove('show'); // Close dropdown after selection

             if (keysToMove.length === 0) {
                 showStatus("No notes selected to move.", "warning");
                 return;
             }

            const targetNotebookName = targetNotebookId === null ? 'Ungrouped' : (notebooks.find(nb => nb.id === targetNotebookId)?.name || '[Unknown Notebook]');
            console.log(`Bulk moving ${keysToMove.length} notes to target Notebook: ${targetNotebookName} (ID: ${targetNotebookId})`);
            showStatus(`Moving ${keysToMove.length} note(s) to "${targetNotebookName}"...`, 'info');

             // Disable all bulk action buttons during operation
             if (bulkDeleteButton) bulkDeleteButton.disabled = true;
             if (bulkExportButton) bulkExportButton.disabled = true;
             if (bulkExportFormatSelect) bulkExportFormatSelect.disabled = true;
             bulkMoveDropdownButton.disabled = true;

            let errors = 0;
             let movedCount = 0; // Count notes that actually need moving
             const updates = {}; // Store { noteKey: updatedNoteData } for storage.set
             const notesToUpload = []; // Track notes that were actually updated for Drive sync { noteKey, noteData }

             // 1. Fetch current data from storage for all selected notes
             const currentDataResult = await chrome.storage.local.get(keysToMove);

             // 2. Update local cache `allNotesData` and prepare storage updates
             allNotesData = allNotesData.map(note => {
                  if (keysToMove.includes(note.key)) {
                      // Check if the note is *already* in the target notebook
                      if(note.notebookId === targetNotebookId) {
                          console.log(`Note ${note.key} is already in the target notebook. Skipping.`);
                          return note; // No change needed for this one
                      }
                      // Note needs moving
                      movedCount++;
                      const updatedNoteCache = {
                          ...note,
                          notebookId: targetNotebookId,
                          data: { ...note.data, notebookId: targetNotebookId }
                      };

                      // Prepare storage update using the potentially fresher data from storage
                      const currentNoteStorageData = currentDataResult[note.key];
                      if (currentNoteStorageData) {
                          // Check again if storage data differs from cache regarding target
                          if (currentNoteStorageData.notebookId !== targetNotebookId) {
                              const updatedNoteStorage = { ...currentNoteStorageData, notebookId: targetNotebookId };
                              updates[note.key] = updatedNoteStorage;
                              notesToUpload.push({ noteKey: note.key, noteData: updatedNoteStorage });
                          } else {
                              // Storage data was already correct, cache was stale? Log warning.
                              console.warn(`Note ${note.key} cache indicated move needed, but storage data was already in target notebook. Updating cache only.`);
                              movedCount--; // Decrement count as no storage move needed
                          }
                      } else {
                          // Note selected but not found in storage? Problem!
                          console.error(`Data for selected note key ${note.key} not found in storage during bulk move! Skipping update for this note.`);
                          errors++; // Count this as an error
                          movedCount--; // Not successfully moved
                          return note; // Return original note to cache to avoid partial update
                      }
                      return updatedNoteCache; // Return updated note for the cache
                  }
                  return note; // Return unchanged note if not selected
             });

            // 3. Perform storage update if any notes actually need moving
            if (Object.keys(updates).length > 0) {
                 console.log(`Attempting to update ${Object.keys(updates).length} notes in storage.`);
                 try {
                    await chrome.storage.local.set(updates);
                    console.log(`Bulk move: Successfully updated ${Object.keys(updates).length} notes in storage.`);

                     // 4. Trigger background uploads for successfully moved notes
                    const syncEnabledResult = await chrome.storage.local.get('driveSyncEnabled');
                     if (syncEnabledResult.driveSyncEnabled && notesToUpload.length > 0) {
                         console.log(`Triggering ${notesToUpload.length} background uploads for moved notes...`);
                         for (const { noteKey, noteData } of notesToUpload) {
                            try {
                                await chrome.runtime.sendMessage({ action: 'uploadNoteToDrive', noteKey: noteKey, noteData: noteData });
                                await new Promise(res => setTimeout(res, 50)); // Small delay between uploads
                            } catch (uploadError) {
                                console.warn(`Failed to trigger upload for moved note ${noteKey}:`, uploadError);
                                // Continue with other uploads even if one fails
                            }
                        }
                         console.log("Finished triggering background uploads for moved notes.");
                    }

                } catch (storageError) {
                     console.error("CRITICAL Error bulk moving notes in storage:", storageError);
                     errors += Object.keys(updates).length; // Assume all failed if storage.set fails
                     // Attempt to revert local cache on critical storage error? Very difficult.
                     // Safest is to reload everything.
                     await initializeDashboard(); // Reload all data
                     showStatus(`CRITICAL ERROR bulk moving notes. Reloading dashboard. Error: ${storageError.message}`, 'error', 7000);
                     return; // Exit early after triggering reload
                 }
            } else {
                console.log("No notes required storage updates for bulk move.");
            }

            // 5. Update UI after successful operation (or if no storage updates needed)
            selectedNoteKeys.clear(); // Clear selection after move attempt
            filterAndRenderNotes(); // Re-render the current view (notes will be updated/gone)

            // 6. Show final status message
             if (errors > 0) {
                 showStatus(`Moved ${movedCount} note(s) with ${errors} error(s). Some notes might not have moved.`, 'error', 5000);
             } else if (movedCount > 0) {
                 showStatus(`Successfully moved ${movedCount} note(s) to "${targetNotebookName}".`, 'success');
             } else {
                 showStatus('No notes needed to be moved (already in target notebook).', 'info');
             }
             // Re-enable bulk action buttons are handled by updateBulkActionsBar called within filterAndRenderNotes

         });

        // Close dropdown if clicking anywhere outside of it or its button
        document.addEventListener('click', (e) => {
            // Check if the click target is outside the dropdown AND outside the dropdown button
            if (bulkMoveOptionsDiv.classList.contains('show') &&
                !bulkMoveOptionsDiv.contains(e.target) &&
                !bulkMoveDropdownButton.contains(e.target)) {
                bulkMoveOptionsDiv.classList.remove('show');
                console.log("Clicked outside bulk move dropdown. Hiding.");
            }
        });
     }

    // --- Highlights Functions ---

    /** Load all highlights from storage */
    async function loadAllHighlights() {
        if (!highlightsResultsDiv) {
            console.error("Cannot load highlights: Results div is null.");
            return;
        }

        highlightsResultsDiv.innerHTML = '<p class="loading-message">Loading highlights...</p>';
        showStatus('Loading highlights...', 'info');
        allHighlightsData = []; // Reset cache

        try {
            // Check if IndexedDB is available
            if (window.StickaraIndexedDB) {
                console.log("Using IndexedDB for loading highlights");

                // Get highlights from IndexedDB with pagination
                const options = {
                    limit: 1000, // Load in chunks to avoid memory issues
                    offset: 0
                };

                let hasMoreHighlights = true;
                let processedCount = 0;

                while (hasMoreHighlights) {
                    // Get a batch of highlights from IndexedDB
                    const highlights = await window.StickaraIndexedDB.getHighlights(options);

                    if (highlights.length === 0) {
                        hasMoreHighlights = false;
                        continue;
                    }

                    // Process each highlight
                    for (const highlight of highlights) {
                        if (highlight && highlight.text && highlight.id) {
                            const key = highlight.key;
                            const url = key.replace(HIGHLIGHT_KEY_PREFIX, '');
                            const decodedUrl = decodeURIComponent(url);

                            // Extract timestamp from ID if available
                            let timestamp = null;
                            const idParts = highlight.id.split('-');
                            if (idParts.length > 1) {
                                const timestampStr = idParts[1];
                                if (!isNaN(parseInt(timestampStr))) {
                                    timestamp = parseInt(timestampStr);
                                }
                            }

                            allHighlightsData.push({
                                key: key,
                                url: url,
                                decodedUrl: decodedUrl,
                                highlightId: highlight.id,
                                text: highlight.text,
                                color: highlight.color || 'yellow',
                                style: highlight.style || 'color',
                                timestamp: timestamp || Date.now(),
                                notebookId: highlight.notebookId || null, // Load notebookId, default to null (Ungrouped)
                                noteText: highlight.noteText || null, // Include the note text if it exists
                                hasLinkedNote: highlight.hasLinkedNote || false, // Include the linked note flag
                                data: highlight
                            });
                            processedCount++;
                        }
                    }

                    // Update options for next batch
                    options.offset += options.limit;

                    // If we got fewer highlights than the limit, we've reached the end
                    if (highlights.length < options.limit) {
                        hasMoreHighlights = false;
                    }
                }

                console.log(`Dashboard: Processed ${processedCount} highlights from IndexedDB.`);
            } else {
                // Fallback to chrome.storage.local if IndexedDB is not available
                console.log("Falling back to chrome.storage.local for loading highlights");

                // Get all keys from storage
                const allKeys = await new Promise((resolve, reject) => {
                    chrome.storage.local.get(null, (result) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(`Error accessing storage: ${chrome.runtime.lastError.message}`));
                        } else {
                            resolve(Object.keys(result));
                        }
                    });
                });

                // Filter for highlight keys
                const highlightKeys = allKeys.filter(key => key.startsWith(HIGHLIGHT_KEY_PREFIX));
                console.log(`Dashboard: Found ${highlightKeys.length} highlight keys in storage.`);

                if (highlightKeys.length === 0) {
                    highlightsResultsDiv.innerHTML = '<p class="no-results">No highlights found.</p>';
                    showStatus('No highlights found.', 'info', 3000);
                    return;
                }

                // Get all highlight data
                const highlightData = await new Promise((resolve, reject) => {
                    chrome.storage.local.get(highlightKeys, (result) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(`Error loading highlights: ${chrome.runtime.lastError.message}`));
                        } else {
                            resolve(result);
                        }
                    });
                });

                // Process highlight data
                let processedCount = 0;

                // Debug log to check highlight data
                console.log("Dashboard: Raw highlight data from storage:", highlightData);

                for (const key of highlightKeys) {
                    const url = key.replace(HIGHLIGHT_KEY_PREFIX, '');
                    const decodedUrl = decodeURIComponent(url);
                    const highlights = highlightData[key];

                    if (Array.isArray(highlights)) {
                        // Debug log to check individual highlights
                        console.log(`Dashboard: Highlights for key ${key}:`, highlights);

                        highlights.forEach(highlight => {
                            if (highlight && highlight.text && highlight.id) {
                                // Debug log to check individual highlight properties
                                console.log(`Dashboard: Processing highlight ID ${highlight.id}:`, {
                                    hasNoteText: !!highlight.noteText,
                                    noteText: highlight.noteText,
                                    hasLinkedNote: highlight.hasLinkedNote
                                });

                                // Extract timestamp from ID if available
                                let timestamp = null;
                                const idParts = highlight.id.split('-');
                                if (idParts.length > 1) {
                                    const timestampStr = idParts[1];
                                    if (!isNaN(parseInt(timestampStr))) {
                                        timestamp = parseInt(timestampStr);
                                    }
                                }

                                allHighlightsData.push({
                                    key: key,
                                    url: url,
                                    decodedUrl: decodedUrl,
                                    highlightId: highlight.id,
                                    text: highlight.text,
                                    color: highlight.color || 'yellow',
                                    style: highlight.style || 'color',
                                    timestamp: timestamp || Date.now(),
                                    notebookId: highlight.notebookId || null, // Load notebookId, default to null (Ungrouped)
                                    noteText: highlight.noteText || null, // Include the note text if it exists
                                    hasLinkedNote: highlight.hasLinkedNote || false, // Include the linked note flag
                                    data: highlight
                                });
                                processedCount++;
                            }
                        });
                    }
                }

                console.log(`Dashboard: Processed ${processedCount} highlights from chrome.storage.local.`);
            }

            // Render highlights based on default filters
            filterAndRenderHighlights();

            // Update dashboard stats after loading highlights
            updateDashboardStats();

            showStatus(`Loaded ${allHighlightsData.length} highlights.`, 'success', 3000);

        } catch (error) {
            console.error("Dashboard: Error loading highlights:", error);
            highlightsResultsDiv.innerHTML = '<p class="no-results error">Error loading highlights. See console for details.</p>';
            showStatus(`Error loading highlights: ${error.message || error}`, 'error', 10000);
        }
    }

    /** Filter and render highlights based on current filters */
    function filterAndRenderHighlights() {
        if (!highlightsResultsDiv) {
            console.error("Cannot render highlights: Results div is null.");
            return;
        }

        // Clear selection when filtering
        selectedHighlightIds.clear();
        updateHighlightsBulkActionsBar();

        // Get filter values
        const searchQuery = highlightsSearchInput ? highlightsSearchInput.value.trim().toLowerCase() : '';
        const colorFilter = highlightColorFilter ? highlightColorFilter.value : 'all';
        const styleFilter = highlightStyleFilter ? highlightStyleFilter.value : 'all';
        const notesFilter = highlightNotesFilter ? highlightNotesFilter.value : 'all';
        const sortBy = highlightsSortBySelect ? highlightsSortBySelect.value : 'date-desc';
        const viewOption = document.getElementById('highlights-view-option') ?
                          document.getElementById('highlights-view-option').value : 'all';

        // Filter highlights with enhanced error handling
        let filteredHighlights = allHighlightsData.filter(highlight => {
            try {
                // Enhanced text search (including notes) with better null handling
                if (searchQuery) {
                    const textMatch = (highlight.text || '').toLowerCase().includes(searchQuery);
                    const urlMatch = (highlight.decodedUrl || highlight.url || '').toLowerCase().includes(searchQuery);
                    const noteMatch = highlight.noteText && highlight.noteText.toLowerCase().includes(searchQuery);

                    // Must match at least one field
                    if (!(textMatch || urlMatch || noteMatch)) {
                        return false;
                    }
                }
            } catch (searchError) {
                console.warn("Error during highlight search filtering:", highlight.highlightId, searchError);
                return false; // Exclude highlights that cause search errors
            }

            // Color filter
            if (colorFilter !== 'all' && highlight.color !== colorFilter) {
                return false;
            }

            // Style filter
            if (styleFilter !== 'all' && highlight.style !== styleFilter) {
                return false;
            }

            // Notes filter
            if (notesFilter === 'with-notes' && !highlight.noteText) {
                return false;
            }

            // View options filter
            if (viewOption === 'recent') {
                // Filter for highlights from the last 7 days
                const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
                if (highlight.timestamp < sevenDaysAgo) {
                    return false;
                }
            } else if (viewOption === 'current') {
                // Get the current URL from the browser (if available)
                const currentUrl = window.location.href;
                if (currentUrl && highlight.decodedUrl !== currentUrl) {
                    return false;
                }
            }

            // Notebook filter
            if (currentNotebookFilter) {
                if (currentNotebookFilter === 'UNGROUPED') {
                    // Show only highlights without a notebook
                    if (highlight.notebookId) {
                        return false;
                    }
                } else {
                    // Show only highlights in the selected notebook
                    if (highlight.notebookId !== currentNotebookFilter) {
                        return false;
                    }
                }
            }

            return true;
        });

        // Sort highlights
        filteredHighlights.sort((a, b) => {
            switch (sortBy) {
                case 'date-asc':
                    return a.timestamp - b.timestamp;
                case 'date-desc':
                    return b.timestamp - a.timestamp;
                case 'url-asc':
                    return a.decodedUrl.localeCompare(b.decodedUrl);
                case 'url-desc':
                    return b.decodedUrl.localeCompare(a.decodedUrl);
                case 'text-asc':
                    return a.text.localeCompare(b.text);
                case 'text-desc':
                    return b.text.localeCompare(a.text);
                default:
                    return b.timestamp - a.timestamp;
            }
        });

        // Render highlights with pagination
        renderHighlights(filteredHighlights, searchQuery);
    }

    /** Render highlights with pagination */
    function renderHighlights(highlightsToRender, query) {
        if (!highlightsResultsDiv) {
            console.error("Cannot render highlights: Results div is null.");
            return;
        }

        highlightsResultsDiv.innerHTML = '';

        // Reset select all checkbox
        if (selectAllHighlightsCheckbox) {
            selectAllHighlightsCheckbox.checked = false;
        }

        if (highlightsToRender.length === 0) {
            // Display a user-friendly message when no highlights match
            highlightsResultsDiv.innerHTML = '<p class="no-results">No matching highlights found.</p>';

            // Update pagination controls
            updateHighlightsPaginationControls(0);

            // Update highlights count display
            if (highlightsCountDisplay) {
                highlightsCountDisplay.textContent = 'Showing 0 of 0 highlights';
            }
        } else {
            // Calculate pagination
            highlightsTotalPages = Math.ceil(highlightsToRender.length / highlightsPageSize);

            // Ensure current page is valid
            if (highlightsCurrentPage > highlightsTotalPages) {
                highlightsCurrentPage = highlightsTotalPages;
            } else if (highlightsCurrentPage < 1) {
                highlightsCurrentPage = 1;
            }

            // Calculate start and end indices for current page
            const startIndex = (highlightsCurrentPage - 1) * highlightsPageSize;
            const endIndex = Math.min(startIndex + highlightsPageSize, highlightsToRender.length);

            // Get highlights for current page
            const highlightsForCurrentPage = highlightsToRender.slice(startIndex, endIndex);

            // Create fragment for highlights
            const fragment = document.createDocumentFragment();
            highlightsForCurrentPage.forEach(highlight => {
                try {
                    // Create the DOM element for each highlight
                    const element = createHighlightElement(highlight, query);
                    if (element) {
                        fragment.appendChild(element);
                    }
                } catch (e) {
                    console.error(`Error creating element for highlight ${highlight?.highlightId}:`, e);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'highlight-item error-item';
                    errorDiv.textContent = `Error rendering highlight. See console.`;
                    fragment.appendChild(errorDiv);
                }
            });

            highlightsResultsDiv.appendChild(fragment);

            // Update pagination controls
            updateHighlightsPaginationControls(highlightsToRender.length);

            // Update highlights count display
            if (highlightsCountDisplay) {
                highlightsCountDisplay.textContent = `Showing ${startIndex + 1}-${endIndex} of ${highlightsToRender.length} highlights`;
            }
        }
    }

    /** Update highlights pagination controls */
    function updateHighlightsPaginationControls(totalHighlights) {
        if (!highlightsPageIndicator || !highlightsPrevPageBtn || !highlightsNextPageBtn) {
            console.warn("Highlights pagination controls not found in the DOM");
            return;
        }

        // Update page indicator
        highlightsPageIndicator.textContent = totalHighlights === 0 ?
            'No highlights' :
            `Page ${highlightsCurrentPage} of ${highlightsTotalPages}`;

        // Update button states
        highlightsPrevPageBtn.disabled = highlightsCurrentPage <= 1 || totalHighlights === 0;
        highlightsNextPageBtn.disabled = highlightsCurrentPage >= highlightsTotalPages || totalHighlights === 0;
    }

    /** Create a DOM element for a highlight */
    function createHighlightElement(highlight, query) {
        const { highlightId, text, color, style, decodedUrl, timestamp, noteText } = highlight;

        // Debug log to check if noteText is being passed correctly
        console.log(`Creating highlight element for ID ${highlightId}:`, {
            hasNoteText: !!noteText,
            noteText: noteText,
            hasLinkedNote: highlight.hasLinkedNote
        });

        // Main container
        const itemDiv = document.createElement('div');
        itemDiv.className = 'highlight-item';
        itemDiv.dataset.highlightId = highlightId;
        itemDiv.dataset.highlightKey = highlight.key;

        // Add a class if the highlight has a note
        if (noteText) {
            itemDiv.classList.add('has-note');
        }

        // Add drag attributes
        itemDiv.draggable = true;
        itemDiv.setAttribute('aria-grabbed', 'false');
        itemDiv.dataset.highlightUrl = highlight.url;

        // Add drag event listeners
        itemDiv.addEventListener('dragstart', handleHighlightDragStart);
        itemDiv.addEventListener('dragend', handleHighlightDragEnd);

        // Checkbox container
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'highlight-checkbox-container';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'highlight-checkbox';
        checkbox.checked = selectedHighlightIds.has(highlightId);
        checkbox.addEventListener('change', () => {
            if (checkbox.checked) {
                selectedHighlightIds.add(highlightId);
            } else {
                selectedHighlightIds.delete(highlightId);
            }
            updateHighlightsBulkActionsBar();
        });

        checkboxContainer.appendChild(checkbox);
        itemDiv.appendChild(checkboxContainer);

        // Content container
        const contentDiv = document.createElement('div');
        contentDiv.className = 'highlight-content';

        // Highlight text
        const textP = document.createElement('p');
        textP.className = `highlight-text ${style === 'color' ? `color-${color}` : `style-${style}`}`;
        textP.style.cursor = 'pointer'; // Add pointer cursor to indicate clickable

        // Apply search query highlighting if query exists
        let highlightedText = text || '';
        if (query) {
            try {
                const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
                const rgx = new RegExp(escapedQuery, 'gi');
                highlightedText = highlightedText.replace(rgx, '<mark>$&</mark>');
            } catch (e) {
                console.warn("Regex failed for highlighting:", e);
            }
        }

        textP.innerHTML = highlightedText;

        // Make the highlight text clickable to open the modal
        textP.addEventListener('click', (e) => {
            // Don't trigger if clicking on a checkbox or button
            if (e.target.closest('.highlight-checkbox-container') || e.target.closest('.highlight-actions')) {
                return;
            }
            openHighlightInModal(highlightId);
        });

        contentDiv.appendChild(textP);

        // Meta information
        const metaDiv = document.createElement('div');
        metaDiv.className = 'highlight-meta';

        // URL
        const urlSpan = document.createElement('div');
        urlSpan.className = 'highlight-url';

        const urlLink = document.createElement('a');
        urlLink.href = '#';
        urlLink.textContent = decodedUrl;
        urlLink.title = `View highlight content`;
        urlLink.addEventListener('click', (e) => {
            e.preventDefault();
            openHighlightInModal(highlightId); // Open highlight in modal instead of navigating to page
        });

        urlSpan.appendChild(urlLink);
        metaDiv.appendChild(urlSpan);

        // Notebook info
        const notebookSpan = document.createElement('div');
        notebookSpan.className = 'highlight-notebook';

        if (highlight.notebookId) {
            const notebook = notebooks.find(nb => nb.id === highlight.notebookId);
            if (notebook) {
                notebookSpan.textContent = `📓 ${notebook.name}`;
                notebookSpan.title = `Notebook: ${notebook.name}`;
            } else {
                notebookSpan.textContent = '📓 Unknown Notebook';
            }
        } else {
            notebookSpan.textContent = '📓 Ungrouped';
            notebookSpan.title = 'Not in any notebook';
        }

        metaDiv.appendChild(notebookSpan);

        // Date
        const dateSpan = document.createElement('div');
        dateSpan.className = 'highlight-date';

        if (timestamp) {
            const date = new Date(timestamp);
            dateSpan.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        } else {
            dateSpan.textContent = 'Unknown date';
        }

        metaDiv.appendChild(dateSpan);

        // Actions
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'highlight-actions';

        // Delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'highlight-action-btn delete';
        deleteBtn.innerHTML = '🗑️';
        deleteBtn.title = 'Delete highlight';
        deleteBtn.addEventListener('click', () => deleteHighlight(highlight));

        actionsDiv.appendChild(deleteBtn);
        metaDiv.appendChild(actionsDiv);

        contentDiv.appendChild(metaDiv);

        // Add note if it exists
        if (highlight.noteText) {
            const noteDiv = document.createElement('div');
            noteDiv.className = 'highlight-note';

            // Add note icon and label
            const noteLabel = document.createElement('span');
            noteLabel.className = 'note-label';
            noteLabel.innerHTML = '📝 Note: ';
            noteDiv.appendChild(noteLabel);

            // Add note text with search highlighting if query exists
            const noteContent = document.createElement('span');
            noteContent.className = 'note-content';

            let noteText = highlight.noteText;
            // Apply search query highlighting to note text if query exists
            if (query && noteText) {
                try {
                    const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
                    const rgx = new RegExp(escapedQuery, 'gi');
                    noteText = noteText.replace(rgx, '<mark>$&</mark>');
                    noteContent.innerHTML = noteText;
                } catch (e) {
                    console.warn("Regex failed for note highlighting:", e);
                    noteContent.textContent = noteText;
                }
            } else {
                noteContent.textContent = noteText;
            }

            noteDiv.appendChild(noteContent);
            contentDiv.appendChild(noteDiv);
        }

        itemDiv.appendChild(contentDiv);

        return itemDiv;
    }

    /** Open a page with a specific highlight */
    function openHighlightInTab(highlight) {
        const url = highlight.url;
        if (!url) {
            showStatus("Cannot open highlight: URL is missing", "error");
            return;
        }

        // With the new storage approach, URLs are stored in raw format
        // so we can use them directly without complex processing
        let targetUrl = url;

        // Basic validation - ensure URL has a protocol
        if (!targetUrl.includes('://')) {
            targetUrl = 'http://' + targetUrl;
            console.log(`Added http:// prefix to URL: ${targetUrl}`);
        }

        console.log(`Using URL: ${targetUrl}`);

        // Open the page in a new tab
        chrome.tabs.create({ url: targetUrl }, (tab) => {
            if (chrome.runtime.lastError) {
                console.error(`Error creating tab: ${chrome.runtime.lastError.message}`);
                showStatus(`Error opening highlight: ${chrome.runtime.lastError.message}`, "error");
            } else {
                console.log(`Opened highlight in tab ${tab.id}`);
            }
        });
    }

    /** Delete a highlight from storage */
    async function deleteHighlight(highlight) {
        if (!highlight || !highlight.key || !highlight.highlightId) {
            console.error("Cannot delete highlight: Invalid highlight data");
            return;
        }

        try {
            // Get current highlights for this URL
            const result = await new Promise((resolve, reject) => {
                chrome.storage.local.get([highlight.key], (result) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(`Error accessing storage: ${chrome.runtime.lastError.message}`));
                    } else {
                        resolve(result);
                    }
                });
            });

            const highlights = result[highlight.key];
            if (!Array.isArray(highlights)) {
                throw new Error("Invalid highlights data in storage");
            }

            // Remove the highlight
            const updatedHighlights = highlights.filter(h => h.id !== highlight.highlightId);

            // Save back to storage
            await new Promise((resolve, reject) => {
                if (updatedHighlights.length === 0) {
                    // If no highlights left, remove the key entirely
                    chrome.storage.local.remove([highlight.key], () => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(`Error removing from storage: ${chrome.runtime.lastError.message}`));
                        } else {
                            resolve();
                        }
                    });
                } else {
                    // Otherwise, save the updated array
                    chrome.storage.local.set({ [highlight.key]: updatedHighlights }, () => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(`Error saving to storage: ${chrome.runtime.lastError.message}`));
                        } else {
                            resolve();
                        }
                    });
                }
            });

            // Remove from local data
            allHighlightsData = allHighlightsData.filter(h => h.highlightId !== highlight.highlightId);

            // Remove from selection if selected
            selectedHighlightIds.delete(highlight.highlightId);

            // Re-render highlights
            filterAndRenderHighlights();

            showStatus("Highlight deleted successfully.", 'success', 3000);

        } catch (error) {
            console.error("Error deleting highlight:", error);
            showStatus(`Error deleting highlight: ${error.message}`, 'error', 5000);
        }
    }

    /** Delete multiple highlights */
    async function deleteSelectedHighlights() {
        if (selectedHighlightIds.size === 0) {
            showStatus("No highlights selected for deletion.", 'info', 3000);
            return;
        }

        const confirmDelete = confirm(`Are you sure you want to delete ${selectedHighlightIds.size} highlight(s)?`);
        if (!confirmDelete) return;

        showStatus(`Deleting ${selectedHighlightIds.size} highlights...`, 'info');

        try {
            // Group highlights by key for efficient storage operations
            const highlightsByKey = {};

            // Find all selected highlights
            const selectedHighlights = allHighlightsData.filter(h => selectedHighlightIds.has(h.highlightId));

            // Group by storage key
            selectedHighlights.forEach(highlight => {
                if (!highlightsByKey[highlight.key]) {
                    highlightsByKey[highlight.key] = [];
                }
                highlightsByKey[highlight.key].push(highlight.highlightId);
            });

            // Process each key
            for (const key of Object.keys(highlightsByKey)) {
                const highlightIds = highlightsByKey[key];

                // Get current highlights for this key
                const result = await new Promise((resolve, reject) => {
                    chrome.storage.local.get([key], (result) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(`Error accessing storage: ${chrome.runtime.lastError.message}`));
                        } else {
                            resolve(result);
                        }
                    });
                });

                const highlights = result[key];
                if (!Array.isArray(highlights)) {
                    console.warn(`Invalid highlights data for key ${key}, skipping`);
                    continue;
                }

                // Filter out the selected highlights
                const updatedHighlights = highlights.filter(h => !highlightIds.includes(h.id));

                // Save back to storage
                await new Promise((resolve, reject) => {
                    if (updatedHighlights.length === 0) {
                        // If no highlights left, remove the key entirely
                        chrome.storage.local.remove([key], () => {
                            if (chrome.runtime.lastError) {
                                reject(new Error(`Error removing from storage: ${chrome.runtime.lastError.message}`));
                            } else {
                                resolve();
                            }
                        });
                    } else {
                        // Otherwise, save the updated array
                        chrome.storage.local.set({ [key]: updatedHighlights }, () => {
                            if (chrome.runtime.lastError) {
                                reject(new Error(`Error saving to storage: ${chrome.runtime.lastError.message}`));
                            } else {
                                resolve();
                            }
                        });
                    }
                });
            }

            // Remove from local data
            allHighlightsData = allHighlightsData.filter(h => !selectedHighlightIds.has(h.highlightId));

            // Clear selection
            selectedHighlightIds.clear();

            // Re-render highlights
            filterAndRenderHighlights();

            showStatus(`${selectedHighlights.length} highlights deleted successfully.`, 'success', 3000);

        } catch (error) {
            console.error("Error deleting highlights:", error);
            showStatus(`Error deleting highlights: ${error.message}`, 'error', 5000);
        }
    }

    /** Update the highlights bulk actions bar */
    function updateHighlightsBulkActionsBar() {
        if (!highlightsBulkActionsBar || !highlightsSelectionCountSpan) {
            console.warn("Highlights bulk actions bar or selection count span not found");
            return;
        }

        const count = selectedHighlightIds.size;
        highlightsSelectionCountSpan.textContent = `${count} highlight${count !== 1 ? 's' : ''} selected`;

        const shouldBeVisible = count > 0;

        // Make sure to add/remove the visible class
        if (shouldBeVisible) {
            highlightsBulkActionsBar.classList.add('visible');
        } else {
            highlightsBulkActionsBar.classList.remove('visible');
        }

        // Enable/disable bulk action buttons
        if (bulkDeleteHighlightsButton) bulkDeleteHighlightsButton.disabled = !shouldBeVisible;
        if (bulkExportHighlightsButton) bulkExportHighlightsButton.disabled = !shouldBeVisible;
        if (highlightsMoveDropdownButton) highlightsMoveDropdownButton.disabled = !shouldBeVisible;

        // Update "Select All" checkbox state
        if (selectAllHighlightsCheckbox && highlightsResultsDiv) {
            const highlightItems = highlightsResultsDiv.querySelectorAll('.highlight-item');
            const visibleHighlightCount = highlightItems.length;

            if (visibleHighlightCount === 0) {
                selectAllHighlightsCheckbox.checked = false;
                selectAllHighlightsCheckbox.disabled = true;
            } else {
                selectAllHighlightsCheckbox.disabled = false;

                let allSelected = true;
                highlightItems.forEach(item => {
                    const id = item.dataset.highlightId;
                    if (id && !selectedHighlightIds.has(id)) {
                        allSelected = false;
                    }
                });

                selectAllHighlightsCheckbox.checked = allSelected && visibleHighlightCount > 0;
            }
        }
    }

    /** Debounced filter function for highlights search */
    function debouncedHighlightsFilter() {
        clearTimeout(highlightsDebounceTimer);
        highlightsDebounceTimer = setTimeout(() => {
            highlightsCurrentPage = 1; // Reset to first page when filtering
            filterAndRenderHighlights();
        }, 300);
    }

    /** Add a highlight to a notebook */
    async function addHighlightToNotebook(highlight, notebookId) {
        try {
            console.log("Adding highlight to notebook:", { highlight, notebookId });

            // Find the highlight in storage
            const storageKey = highlight.key;
            if (!storageKey) {
                console.error("Highlight has no storage key:", highlight);
                showStatus("Error: Highlight has no storage key", "error");
                return false;
            }

            // Get the current highlight data from storage
            const result = await chrome.storage.local.get(storageKey);
            if (!result || !result[storageKey]) {
                console.error("Highlight not found in storage:", storageKey);
                showStatus("Error: Highlight not found in storage", "error");
                return false;
            }

            // Get the highlights array from storage
            const highlightsArray = result[storageKey];
            if (!Array.isArray(highlightsArray)) {
                console.error("Invalid highlights data format:", highlightsArray);
                showStatus("Error: Invalid highlights data format", "error");
                return false;
            }

            // Find the specific highlight in the array
            const highlightIndex = highlightsArray.findIndex(h => h.id === highlight.highlightId);
            if (highlightIndex === -1) {
                console.error("Highlight not found in array:", highlight.highlightId);
                showStatus("Error: Highlight not found in array", "error");
                return false;
            }

            // Update the highlight in the array with the notebook ID
            highlightsArray[highlightIndex].notebookId = notebookId;

            // Save the updated array back to storage
            await chrome.storage.local.set({ [storageKey]: highlightsArray });
            console.log("Highlight updated in storage with notebookId:", notebookId);

            // Update the highlight in our local data
            const localHighlightIndex = allHighlightsData.findIndex(h => h.highlightId === highlight.highlightId);
            if (localHighlightIndex !== -1) {
                allHighlightsData[localHighlightIndex].notebookId = notebookId;
                if (allHighlightsData[localHighlightIndex].data) {
                    allHighlightsData[localHighlightIndex].data.notebookId = notebookId;
                }
            }

            // Create a notebook metadata entry if it doesn't exist
            const notebookMetadataKey = 'notebook_highlights';
            const notebookMetadata = await chrome.storage.local.get(notebookMetadataKey);
            let notebookHighlights = notebookMetadata[notebookMetadataKey] || {};

            // Add the highlight to the notebook's list
            if (!notebookHighlights[notebookId]) {
                notebookHighlights[notebookId] = [];
            }

            // Check if the highlight is already in the notebook
            if (!notebookHighlights[notebookId].includes(highlight.highlightId)) {
                notebookHighlights[notebookId].push(highlight.highlightId);
            }

            // Save the updated notebook metadata
            await chrome.storage.local.set({ [notebookMetadataKey]: notebookHighlights });
            console.log("Notebook metadata updated:", notebookHighlights);

            // Refresh the highlights view
            filterAndRenderHighlights();

            // Get notebook name for the status message
            let notebookName = "Ungrouped";
            if (notebookId) {
                const notebook = notebooks.find(nb => nb.id === notebookId);
                if (notebook) {
                    notebookName = notebook.name;
                }
            }

            showStatus(`Highlight added to "${notebookName}" notebook.`, 'success');
            return true;
        } catch (error) {
            console.error("Error adding highlight to notebook:", error);
            showStatus(`Error: ${error.message}`, 'error');
            return false;
        }
    }

    // --- Highlight Drag and Drop Handlers ---

    /** Handle highlight drag start */
    function handleHighlightDragStart(e) {
        // Ensure the event target is a draggable highlight item and has an ID
        if (!e.target.classList.contains('highlight-item') || !e.target.dataset.highlightId) {
            e.preventDefault(); // Prevent dragging if not a valid highlight
            return;
        }

        // Get the highlight ID and key
        const highlightId = e.target.dataset.highlightId;
        const highlightKey = e.target.dataset.highlightKey;

        // Set drag data
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/plain', highlightId);
        e.dataTransfer.setData('application/highlight-id', highlightId);
        e.dataTransfer.setData('application/highlight-key', highlightKey);

        // Set a custom drag image (optional)
        const dragIcon = document.createElement('div');
        dragIcon.className = 'drag-icon';
        dragIcon.textContent = '📝';
        dragIcon.style.fontSize = '24px';
        document.body.appendChild(dragIcon);
        e.dataTransfer.setDragImage(dragIcon, 15, 15);
        setTimeout(() => document.body.removeChild(dragIcon), 0);

        // Update UI to show dragging state
        e.target.classList.add('dragging');
        e.target.setAttribute('aria-grabbed', 'true');

        // Store the dragged highlight ID for reference
        draggedHighlightId = highlightId;

        // Initialize auto-scroll functionality for highlights
        initAutoScroll();

        // Make notebook section more visible during drag
        if (notebookSidebar) {
            notebookSidebar.classList.add('drag-active');
        }

        // Show drop overlay on notebooks
        document.querySelectorAll('#notebook-list li').forEach(li => {
            li.classList.add('droppable');
        });

        // Show the drop overlay
        const dropOverlay = document.getElementById('drop-overlay');
        if (dropOverlay) {
            dropOverlay.classList.add('active');
            dropOverlay.textContent = 'Drop highlight to move to notebook';
        }
    }

    /** Handle highlight drag end */
    function handleHighlightDragEnd(e) {
        // Reset UI state
        e.target.classList.remove('dragging');
        e.target.setAttribute('aria-grabbed', 'false');

        // Reset dragged highlight ID
        draggedHighlightId = null;

        // Clean up auto-scroll functionality
        cleanupAutoScroll();

        // Remove drag-active class from notebook sidebar
        if (notebookSidebar) {
            notebookSidebar.classList.remove('drag-active');
            notebookSidebar.classList.remove('enhanced-visibility');
        }

        // Hide drop indicators on notebooks
        document.querySelectorAll('#notebook-list li').forEach(li => {
            li.classList.remove('droppable');
            li.classList.remove('drag-over');
        });

        // Hide the drop overlay
        const dropOverlay = document.getElementById('drop-overlay');
        if (dropOverlay) {
            dropOverlay.classList.remove('active');
        }
    }

    // Add event listeners for highlight bulk actions
    if (bulkDeleteHighlightsButton) bulkDeleteHighlightsButton.addEventListener('click', deleteSelectedHighlights);
    if (bulkExportHighlightsButton) bulkExportHighlightsButton.addEventListener('click', exportSelectedHighlights);

    // Highlights Move Dropdown Button Listener
    if (highlightsMoveDropdownButton && highlightsMoveOptionsDiv) {
        // Toggle dropdown visibility on button click
        highlightsMoveDropdownButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent the document click listener from closing it immediately
            const isShown = highlightsMoveOptionsDiv.classList.toggle('show');
            console.log(`Highlights move dropdown ${isShown ? 'shown' : 'hidden'}`);
        });

        // Handle clicks on move options using event delegation on the container
        highlightsMoveOptionsDiv.addEventListener('click', async (e) => {
            // Find the actual button element that was clicked inside the dropdown
            const moveButton = e.target.closest('button[data-target-notebook-id]');
            if (!moveButton) return; // Click was not on a move option button

            const targetNotebookIdRaw = moveButton.dataset.targetNotebookId;
            // Convert 'UNGROUPED' to null, otherwise use the ID
            const targetNotebookId = (targetNotebookIdRaw === 'UNGROUPED') ? null : targetNotebookIdRaw;
            const highlightIdsToMove = Array.from(selectedHighlightIds); // Get currently selected highlight IDs

            highlightsMoveOptionsDiv.classList.remove('show'); // Close dropdown after selection

            if (highlightIdsToMove.length === 0) {
                showStatus("No highlights selected to move.", "warning");
                return;
            }

            const targetNotebookName = targetNotebookId === null ? 'Ungrouped' : (notebooks.find(nb => nb.id === targetNotebookId)?.name || '[Unknown Notebook]');
            console.log(`Bulk moving ${highlightIdsToMove.length} highlights to target Notebook: ${targetNotebookName} (ID: ${targetNotebookId})`);
            showStatus(`Moving ${highlightIdsToMove.length} highlight(s) to "${targetNotebookName}"...`, 'info');

            // Disable all bulk action buttons during operation
            if (bulkDeleteHighlightsButton) bulkDeleteHighlightsButton.disabled = true;
            if (bulkExportHighlightsButton) bulkExportHighlightsButton.disabled = true;
            if (highlightsExportFormatSelect) highlightsExportFormatSelect.disabled = true;
            highlightsMoveDropdownButton.disabled = true;

            try {
                // Process each selected highlight
                let successCount = 0;
                for (const highlightId of highlightIdsToMove) {
                    // Find the highlight in our data
                    const highlight = allHighlightsData.find(h => h.highlightId === highlightId);
                    if (!highlight) {
                        console.warn(`Highlight with ID ${highlightId} not found in data.`);
                        continue;
                    }

                    // Add the highlight to the notebook
                    const success = await addHighlightToNotebook(highlight, targetNotebookId);
                    if (success) successCount++;
                }

                // Show success message
                showStatus(`Successfully moved ${successCount} of ${highlightIdsToMove.length} highlights to "${targetNotebookName}".`, 'success');

                // Clear selection after successful move
                selectedHighlightIds.clear();
                updateHighlightsBulkActionsBar();

                // Refresh the highlights view
                filterAndRenderHighlights();
            } catch (error) {
                console.error("Error moving highlights to notebook:", error);
                showStatus(`Error moving highlights: ${error.message}`, 'error');
            } finally {
                // Re-enable buttons
                if (bulkDeleteHighlightsButton) bulkDeleteHighlightsButton.disabled = false;
                if (bulkExportHighlightsButton) bulkExportHighlightsButton.disabled = false;
                if (highlightsExportFormatSelect) highlightsExportFormatSelect.disabled = false;
                highlightsMoveDropdownButton.disabled = false;
            }
        });

        // Close dropdown if clicking anywhere outside of it or its button
        document.addEventListener('click', (e) => {
            // Check if the click target is outside the dropdown AND outside the dropdown button
            if (highlightsMoveOptionsDiv.classList.contains('show') &&
                !highlightsMoveOptionsDiv.contains(e.target) &&
                !highlightsMoveDropdownButton.contains(e.target)) {
                highlightsMoveOptionsDiv.classList.remove('show');
                console.log("Clicked outside highlights move dropdown. Hiding.");
            }
        });
    }

    /** Export selected highlights */
    function exportSelectedHighlights() {
        if (selectedHighlightIds.size === 0) {
            showStatus("No highlights selected for export.", 'info', 3000);
            return;
        }

        // Get export format
        const exportFormatSelect = document.getElementById('highlights-export-format-select');
        const format = exportFormatSelect ? exportFormatSelect.value : 'md';

        // Find all selected highlights
        const selectedHighlights = allHighlightsData.filter(h => selectedHighlightIds.has(h.highlightId));

        // Group highlights by URL
        const highlightsByUrl = {};
        selectedHighlights.forEach(highlight => {
            if (!highlightsByUrl[highlight.decodedUrl]) {
                highlightsByUrl[highlight.decodedUrl] = [];
            }
            highlightsByUrl[highlight.decodedUrl].push(highlight);
        });

        let exportContent = '';
        let mimeType = '';
        let fileExtension = '';

        // Create export content based on format
        switch (format) {
            case 'pdf':
                // For PDF, we'll create an HTML document first, then convert it to PDF
                exportContent = createHtmlExport(highlightsByUrl);
                createAndDownloadPDF(exportContent, selectedHighlights.length);
                return; // Early return as PDF is handled differently
            case 'md':
                exportContent = createMarkdownExport(highlightsByUrl);
                mimeType = 'text/markdown';
                fileExtension = 'md';
                break;
            case 'html':
                exportContent = createHtmlExport(highlightsByUrl);
                mimeType = 'text/html';
                fileExtension = 'html';
                break;
            case 'txt':
                exportContent = createTextExport(highlightsByUrl);
                mimeType = 'text/plain';
                fileExtension = 'txt';
                break;
            default:
                exportContent = createMarkdownExport(highlightsByUrl);
                mimeType = 'text/markdown';
                fileExtension = 'md';
        }

        // Create and download file
        const blob = new Blob([exportContent], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Stickara_highlights_${new Date().toISOString().slice(0, 10)}.${fileExtension}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showStatus(`Exported ${selectedHighlights.length} highlights successfully as ${fileExtension.toUpperCase()}.`, 'success', 3000);
    }

    /** Create Markdown export content */
    function createMarkdownExport(highlightsByUrl) {
        let content = `# 🔍 Stickara Highlights\n\n`;
        content += `Exported on: ${new Date().toLocaleString()}\n\n`;

        // Add highlights grouped by URL
        Object.keys(highlightsByUrl).sort().forEach(url => {
            content += `## ${url}\n\n`;

            highlightsByUrl[url].forEach(highlight => {
                // Add date if available
                const date = highlight.timestamp ? new Date(highlight.timestamp).toLocaleString() : 'Unknown date';

                content += `> ${highlight.text}\n\n`;
                content += `*Style: ${highlight.style}${highlight.style === 'color' ? `, Color: ${highlight.color}` : ''}*\n`;
                content += `*Date: ${date}*\n`;

                // Add note if available
                if (highlight.noteText) {
                    content += `\n📝 **Note:** ${highlight.noteText}\n`;
                }

                content += `\n`;
            });
        });

        return content;
    }

    /** Create HTML export content */
    function createHtmlExport(highlightsByUrl) {
        // Calculate total highlights
        const totalHighlights = Object.values(highlightsByUrl).reduce((sum, highlights) => sum + highlights.length, 0);

        // Generate the same beautiful HTML content as popup export
        let htmlBodyContent = `
<div class="header">
    <h1>Stickara Highlights</h1>
    <div class="export-date">Exported on: ${new Date().toLocaleString()}</div>
</div>
<div class="meta-info">
    <div class="meta-item">
        <div class="meta-icon">📊</div>
        <div class="meta-content">
            <div class="meta-label">Total Highlights</div>
            <div class="meta-value">${totalHighlights}</div>
        </div>
    </div>
    <div class="meta-item">
        <div class="meta-icon">🌐</div>
        <div class="meta-content">
            <div class="meta-label">Total Pages</div>
            <div class="meta-value">${Object.keys(highlightsByUrl).length}</div>
        </div>
    </div>
</div>`;

        // Add highlights grouped by URL
        Object.keys(highlightsByUrl).sort().forEach(url => {
            // Add URL section header
            htmlBodyContent += `
<div class="url-section">
    <h2 class="url-header">
        <span class="url-icon">🌐</span>
        <a href="${url}" target="_blank" rel="noopener noreferrer" class="url-link">${url}</a>
    </h2>
</div>`;

            highlightsByUrl[url].forEach((highlight, index) => {
                const style = highlight.style || 'color';
                const color = (style === 'color') ? (highlight.color || 'yellow') : null;
                const colorName = color ? color.charAt(0).toUpperCase() + color.slice(1) : '';

                // Get style emoji and display text
                let styleEmoji = '🟨'; // Default yellow highlight
                let styleText = '';
                let styleCSS = '';

                if (style === 'color' && color) {
                    // Color highlight
                    if (color === 'green') styleEmoji = '🟩';
                    else if (color === 'blue') styleEmoji = '🟦';
                    else if (color === 'red') styleEmoji = '🟥';
                    else if (color === 'purple') styleEmoji = '🟪';
                    styleText = `Color: ${colorName}`;
                    styleCSS = `background-color: ${color};`;
                } else {
                    // Style highlight
                    if (style === 'underline') {
                        styleEmoji = '📝';
                        styleText = 'Style: Underline';
                        styleCSS = 'text-decoration: underline; text-decoration-color: #2196F3;';
                    } else if (style === 'wavy') {
                        styleEmoji = '〰️';
                        styleText = 'Style: Wavy';
                        styleCSS = 'text-decoration: underline wavy; text-decoration-color: #9C27B0;';
                    } else if (style === 'border-thick') {
                        styleEmoji = '🔲';
                        styleText = 'Style: Border';
                        styleCSS = 'border: 2px solid #4CAF50; padding: 2px 4px;';
                    } else if (style === 'strikethrough') {
                        styleEmoji = '❌';
                        styleText = 'Style: Strikethrough';
                        styleCSS = 'text-decoration: line-through; text-decoration-color: #F44336;';
                    }
                }

                // Format timestamp
                const timestamp = highlight.timestamp ? new Date(highlight.timestamp) : null;
                const timestampHtml = timestamp && !isNaN(timestamp.getTime())
                    ? `<div class="highlight-timestamp">📅 ${timestamp.toLocaleString()}</div>`
                    : '';

                // Escape the highlight text for safe HTML display
                const escapedText = escapeHtml(highlight.text);

                // Add note content if available
                const noteHtml = highlight.noteText
                    ? `<div class="highlight-note">
                        <div class="note-header">📝 Note</div>
                        <div class="note-content">${escapeHtml(highlight.noteText)}</div>
                    </div>`
                    : '';

                htmlBodyContent += `
    <div class="highlight-card">
        <div class="highlight-header">
            <div class="highlight-title">${styleEmoji} Highlight ${index + 1}</div>
            <div class="highlight-style">${styleText}</div>
        </div>
        ${timestampHtml}
        <blockquote class="highlight-content" style="${styleCSS} ${style === 'color' ? `border-left: 4px solid ${color};` : ''}">
            ${escapedText}
        </blockquote>
        ${noteHtml}
    </div>`;
            });
        });

        // Add CSS styling (same as popup highlights export)
        const customStyles = `
<style>
    :root {
        --primary-color: #4a6fa5;
        --secondary-color: #6b8cae;
        --accent-color: #ff7e5f;
        --text-color: #333333;
        --light-bg: #f8f9fa;
        --border-color: #e0e0e0;
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: #ffffff;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        min-height: 100vh;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        border-radius: 8px 8px 0 0;
        margin: 20px 20px 0 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .export-date {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 20px;
        background-color: var(--light-bg);
        margin: 0 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .meta-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 250px;
    }

    .meta-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        color: var(--primary-color);
    }

    .meta-label {
        font-size: 0.8rem;
        color: #666;
        margin-right: 5px;
        font-weight: bold;
    }

    .meta-value {
        font-weight: 500;
    }

    .url-section {
        margin: 30px 20px 20px 20px;
    }

    .url-header {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 8px;
        border-left: 5px solid var(--primary-color);
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .url-icon {
        font-size: 1.5rem;
        margin-right: 12px;
        color: var(--primary-color);
    }

    .url-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        font-size: 1.1rem;
        word-break: break-all;
    }

    .url-link:hover {
        text-decoration: underline;
        color: var(--secondary-color);
    }

    .highlight-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
        padding: 1.8rem;
        margin: 20px 30px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        border: 1px solid var(--border-color);
    }

    .highlight-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .highlight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }

    .highlight-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .highlight-style {
        font-size: 0.85rem;
        color: #666;
        background-color: var(--light-bg);
        padding: 4px 8px;
        border-radius: 12px;
        border: 1px solid var(--border-color);
    }

    .highlight-timestamp {
        font-size: 0.8rem;
        color: #888;
        margin-bottom: 12px;
        padding: 6px 10px;
        background-color: var(--light-bg);
        border-radius: 6px;
        border: 1px solid #e8e8e8;
    }

    .highlight-content {
        background-color: var(--light-bg);
        padding: 1.2rem;
        border-radius: 8px;
        margin: 15px 0;
        line-height: 1.7;
        font-size: 1rem;
        border: 1px solid #e8e8e8;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .highlight-note {
        background-color: #fff9e6;
        border: 1px solid #ffd54f;
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        border-left: 4px solid #ffc107;
    }

    .note-header {
        font-weight: 600;
        color: #f57c00;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .note-content {
        color: #333;
        line-height: 1.5;
        font-size: 0.95rem;
    }

    footer {
        text-align: center;
        padding: 20px;
        color: #666;
        font-size: 0.9rem;
        margin-top: 30px;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .header {
            padding: 1.5rem;
            margin: 10px 10px 0 10px;
        }

        .meta-info, .url-section, .highlight-card {
            margin: 10px;
        }

        .highlight-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }
    }
</style>`;

        // Add footer
        const footer = `
<footer>
    <p>Generated by Stickara on ${new Date().toLocaleDateString()}</p>
</footer>`;

        // Combine all parts into complete HTML document
        const completeHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickara Highlights</title>
    ${customStyles}
</head>
<body>
    ${htmlBodyContent}
    ${footer}
</body>
</html>`;

        return completeHtmlContent;
    }

    /** Create plain text export content */
    function createTextExport(highlightsByUrl) {
        let content = `STICKARA HIGHLIGHTS\n\n`;
        content += `Exported on: ${new Date().toLocaleString()}\n\n`;

        // Add highlights grouped by URL
        Object.keys(highlightsByUrl).sort().forEach(url => {
            content += `URL: ${url}\n`;
            content += `${'='.repeat(url.length + 5)}\n\n`;

            highlightsByUrl[url].forEach(highlight => {
                // Add date if available
                const date = highlight.timestamp ? new Date(highlight.timestamp).toLocaleString() : 'Unknown date';

                content += `"${highlight.text}"\n\n`;
                content += `Style: ${highlight.style}${highlight.style === 'color' ? `, Color: ${highlight.color}` : ''}\n`;
                content += `Date: ${date}\n`;

                // Add note if available
                if (highlight.noteText) {
                    content += `Note: ${highlight.noteText}\n`;
                }

                content += `\n${`-`.repeat(40)}\n\n`;
            });

            content += `\n\n`;
        });

        return content;
    }

    /**
     * Convert styled HTML content to PDF using html2canvas and jsPDF
     * @param {string} htmlContent - The complete HTML document content with styling
     * @param {string} filename - The filename for the PDF
     * @param {string} statusMessage - Status message to show during conversion
     * @returns {Promise} - Promise that resolves when PDF is generated and downloaded
     */
    async function convertStyledHtmlToPdf(htmlContent, filename, statusMessage) {
        try {
            showStatus(statusMessage, 'info');

            // Create a temporary container for the HTML content with enhanced capture settings
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.top = '-9999px';
            tempContainer.style.width = '1200px'; // Increased width for better capture
            tempContainer.style.backgroundColor = 'white';
            tempContainer.style.padding = '20px';
            tempContainer.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            tempContainer.innerHTML = htmlContent;

            document.body.appendChild(tempContainer);

            // Wait for all images (including video screenshots) to load completely
            const images = tempContainer.querySelectorAll('img');
            console.log(`Dashboard: Waiting for ${images.length} images to load for export...`);

            await Promise.all(Array.from(images).map((img, index) => {
                return new Promise((resolve) => {
                    if (img.complete && img.naturalWidth > 0) {
                        console.log(`Dashboard: Image ${index + 1} already loaded`);
                        resolve();
                    } else {
                        console.log(`Dashboard: Loading image ${index + 1}...`);
                        img.onload = () => {
                            console.log(`Dashboard: Image ${index + 1} loaded successfully`);
                            resolve();
                        };
                        img.onerror = () => {
                            console.warn(`Dashboard: Image ${index + 1} failed to load`);
                            resolve(); // Continue even if image fails to load
                        };
                        // Force reload if src is data URL
                        if (img.src.startsWith('data:image/')) {
                            const originalSrc = img.src;
                            img.src = '';
                            img.src = originalSrc;
                        }
                    }
                });
            }));

            console.log('Dashboard: All images loaded, capturing with html2canvas...');

            // Use html2canvas with enhanced settings for complete capture including video screenshots
            const canvas = await html2canvas(tempContainer, {
                width: 1200,
                height: tempContainer.scrollHeight,
                scale: 2, // Higher scale for better quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                imageTimeout: 20000, // 20 second timeout for large images
                removeContainer: false,
                foreignObjectRendering: true,
                scrollX: 0,
                scrollY: 0,
                windowWidth: 1200,
                windowHeight: tempContainer.scrollHeight,
                onclone: (clonedDoc) => {
                    // Ensure all images in the cloned document are properly sized
                    const clonedImages = clonedDoc.querySelectorAll('img');
                    clonedImages.forEach(img => {
                        img.style.maxWidth = '100%';
                        img.style.height = 'auto';
                        img.style.display = 'block';
                    });
                }
            });

            // Remove the temporary container
            document.body.removeChild(tempContainer);

            // Create PDF with jsPDF
            const pdf = new window.jspdf.jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            // Calculate dimensions to fit the canvas in PDF
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;

            // Calculate scaling to fit width
            const scale = pdfWidth / (canvasWidth / 2); // Divide by 2 because of scale: 2 in html2canvas
            const scaledHeight = (canvasHeight / 2) * scale;

            // Add the canvas as image to PDF
            const imgData = canvas.toDataURL('image/png');

            if (scaledHeight <= pdfHeight) {
                // Single page
                pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, scaledHeight);
            } else {
                // Multiple pages needed
                let yPosition = 0;
                const pageHeight = pdfHeight;

                while (yPosition < scaledHeight) {
                    if (yPosition > 0) {
                        pdf.addPage();
                    }

                    pdf.addImage(imgData, 'PNG', 0, -yPosition, pdfWidth, scaledHeight);
                    yPosition += pageHeight;
                }
            }

            // Download the PDF
            const pdfBlob = pdf.output('blob');
            const objectUrl = URL.createObjectURL(pdfBlob);

            chrome.downloads.download({
                url: objectUrl,
                filename: filename,
                saveAs: true
            }, (id) => {
                URL.revokeObjectURL(objectUrl);
                if (chrome.runtime.lastError) {
                    showStatus(`PDF export failed: ${chrome.runtime.lastError.message}`, 'error');
                } else if (!id) {
                    showStatus('PDF export may have been cancelled or blocked.', 'info');
                } else {
                    showStatus('PDF export completed successfully!', 'success');
                }
            });

        } catch (error) {
            console.error('Error converting HTML to PDF:', error);
            showStatus('PDF generation failed. Please try again.', 'error');
            throw error;
        }
    }

    /** Create and download PDF export */
    async function createAndDownloadPDF(htmlContent, highlightCount) {
        // Check if required libraries are available
        if (typeof window.jspdf === 'undefined') {
            showStatus('PDF library not loaded.', 'error');
            console.error("jsPDF library is required for PDF export but not loaded.");
            return;
        }

        if (typeof html2canvas === 'undefined') {
            showStatus('HTML2Canvas library not loaded.', 'error');
            console.error("html2canvas library is required for styled PDF export but not loaded.");
            return;
        }

        try {
            // Use the provided HTML content directly for styled PDF export
            const filename = `highlights_export_${new Date().toISOString().slice(0, 10)}.pdf`;
            await convertStyledHtmlToPdf(htmlContent, filename, `Generating beautiful PDF with ${highlightCount} highlights...`);
        } catch (pdfError) {
            console.error("Error generating styled PDF:", pdfError);
            showStatus('PDF generation failed.', 'error');
        }
    }

    /** Escape HTML special characters */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /** Safely highlight search terms in text */
    function safeHighlightSearchTerms(text, query) {
        if (!text || !query) return escapeHtml(text);

        try {
            // First escape the text to prevent XSS
            const escapedText = escapeHtml(text);

            // Escape special regex characters in the query
            const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');

            // Create regex for case-insensitive matching
            const regex = new RegExp(`(${escapedQuery})`, 'gi');

            // Highlight matches with <mark> tags
            return escapedText.replace(regex, '<mark>$1</mark>');
        } catch (error) {
            console.warn("Error highlighting search terms:", error);
            return escapeHtml(text); // Fallback to escaped text
        }
    }

    // Make the function globally available for use in other parts of the dashboard
    window.safeHighlightSearchTerms = safeHighlightSearchTerms;





    // --- Initial Load Sequence ---
    async function initializeDashboard() {
         console.log("Dashboard: Starting initialization sequence...");
         showStatus('Loading dashboard data...', 'info');
         try {
             await loadNotebooks(); // Load notebook definitions first
             await loadAllNotes(); // Load all notes, which calls filterAndRenderNotes

             // Load highlights data in the background for accurate stats
             // This ensures the dashboard stats show correct counts immediately
             console.log("Dashboard: Loading highlights data for stats...");
             await loadAllHighlights();

             // Set up initial tab state
             switchTab('notes');

             // Initialize from URL hash parameters (for deep linking)
             initializeFromUrlHash();

             showStatus(''); // Clear loading message on success
             console.log("Dashboard: Initialization complete.");
         } catch (error) {
             console.error("Dashboard: Initialization failed.", error);
             showStatus(`Dashboard failed to initialize: ${error.message}`, 'error', 10000);
             // Display error in the results area as well
             if (resultsDiv) resultsDiv.innerHTML = `<p class="no-results error">Failed to load dashboard data. Error: ${error.message}. Check console for details.</p>`;
         }
    }

    // --- Start Initialization ---
    console.log("Dashboard: Script loaded. Initializing dashboard...");
    initializeDashboard();

}); // End DOMContentLoaded
// --- END OF FILE dashboard.js ---
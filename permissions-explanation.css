body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    color: #333;
}
h1 {
    color: #34D399;
    border-bottom: 2px solid #34D399;
    padding-bottom: 10px;
}
h2 {
    color: #10B981;
    margin-top: 30px;
}
.permission {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}
.permission h3 {
    margin-top: 0;
    color: #1F2937;
    display: flex;
    align-items: center;
    gap: 10px;
}
.permission-icon {
    font-size: 1.2em;
}
.permission-reason {
    margin-top: 10px;
    padding-left: 15px;
    border-left: 3px solid #D1FAE5;
}
.optional {
    background-color: #DBEAFE;
    border-color: #93C5FD;
}
.optional h3 {
    color: #1E40AF;
}
.optional .permission-reason {
    border-left-color: #BFDBFE;
}
.badge {
    display: inline-block;
    font-size: 12px;
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 10px;
}
.required {
    background-color: #FEE2E2;
    color: #B91C1C;
}
.optional-badge {
    background-color: #DBEAFE;
    color: #1E40AF;
}
.last-updated {
    font-style: italic;
    color: #666;
    margin-top: 40px;
}

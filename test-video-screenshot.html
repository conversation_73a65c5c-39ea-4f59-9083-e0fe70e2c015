<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Timestamp Screenshot Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .video-container {
            margin: 20px 0;
            text-align: center;
        }

        video {
            width: 100%;
            max-width: 600px;
            height: auto;
        }

        .timestamp-examples {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .timestamp {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            cursor: pointer;
            text-decoration: none;
        }

        .timestamp:hover {
            background: #0056b3;
        }

        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }

        .note-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Video Timestamp Screenshot Test</h1>

    <div class="instructions">
        <h3>🎯 How to Test the Enhanced Video Screenshot Feature</h3>
        <ol>
            <li>Make sure the Stickara browser extension is installed and active</li>
            <li>Open Stickara notes (click the extension icon)</li>
            <li>Play the video below</li>
            <li>Hold <strong>Ctrl</strong> and click on any timestamp link below</li>
            <li>The extension will:
                <ul>
                    <li>Seek the video to that timestamp</li>
                    <li>Capture a high-resolution screenshot (4K/2K quality)</li>
                    <li><strong>Directly insert the screenshot after the timestamp in your note</strong></li>
                </ul>
            </li>
            <li>Click on the inserted screenshot image to see options:
                <ul>
                    <li><strong>💾 Save</strong> - Download to your PC</li>
                    <li><strong>✏️ Edit</strong> - Open annotation editor (resize, add text, draw)</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="video-container">
        <video controls id="test-video">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.webm" type="video/webm">
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="timestamp-examples">
        <h3>📍 Test Timestamps (Ctrl+Click to capture screenshots)</h3>
        <p>Try Ctrl+clicking on these timestamps to capture video screenshots:</p>

        <a href="#" class="timestamp" data-time="10">0:10</a>
        <a href="#" class="timestamp" data-time="30">0:30</a>
        <a href="#" class="timestamp" data-time="60">1:00</a>
        <a href="#" class="timestamp" data-time="90">1:30</a>
        <a href="#" class="timestamp" data-time="120">2:00</a>
        <a href="#" class="timestamp" data-time="150">2:30</a>
        <a href="#" class="timestamp" data-time="180">3:00</a>

        <br><br>

        <p>Different timestamp formats:</p>
        <span class="timestamp">[@0:45]</span>
        <span class="timestamp">(1:15)</span>
        <span class="timestamp">at 2:45</span>
        <span class="timestamp">@3:15</span>
        <span class="timestamp">[4:00]</span>
    </div>

    <div class="note-section">
        <h3>📝 Using with Stickara Notes</h3>
        <p>If you have Stickara notes with timestamp links (like <span style="background: #ffeb3b; padding: 2px 4px; border-radius: 2px;">[@1:23]</span>), you can also Ctrl+click on those to capture video screenshots!</p>
    </div>

    <script>
        // Add click handlers for timestamp links to seek video
        document.querySelectorAll('.timestamp[data-time]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const video = document.getElementById('test-video');
                const time = parseInt(this.getAttribute('data-time'));
                video.currentTime = time;
                video.play();
            });
        });

        // Log when video time changes for debugging
        document.getElementById('test-video').addEventListener('timeupdate', function() {
            // Only log occasionally to avoid spam
            if (Math.floor(this.currentTime) % 5 === 0) {
                console.log('Video time:', Math.floor(this.currentTime) + 's');
            }
        });
    </script>
</body>
</html>
